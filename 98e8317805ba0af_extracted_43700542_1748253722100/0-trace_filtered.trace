{"version":7,"type":"context-options","origin":"library","browserName":"chromium","options":{"noDefaultViewport":false,"viewport":{"width":1280,"height":720},"ignoreHTTPSErrors":false,"javaScriptEnabled":true,"bypassCSP":false,"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.6778.33 Safari/537.36","locale":"en-US","permissions":["microphone","notifications","camera"],"offline":false,"deviceScaleFactor":1,"isMobile":false,"hasTouch":false,"colorScheme":"light","acceptDownloads":"accept","baseURL":"https://po-automation.mytalkdesk.eu","recordVideo":{"dir":"/test-results/.playwright-artifacts-1","size":{"width":800,"height":450}},"serviceWorkers":"allow","storageState":{"cookies":[{"name":"SESSION","value":"ZmE3MDcwZmQtODVlOC00M2MzLWJkM2MtNDk3NTIyMWUxNTVl","domain":"tiger.mytalkdesk.com","path":"/atlas/","expires":-1,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"ATLAS_DEVICE_ID","value":"2fb891d0-846d-4540-ad44-1df370cff1c8","domain":".mytalkdesk.com","path":"/atlas","expires":1706669448.535065,"httpOnly":true,"secure":false,"sameSite":"Lax"},{"name":"X-Platform-Tid","value":"9e405752-1af2-4ff3-93e1-a73d04346035","domain":"tiger.talkdeskid.com","path":"/","expires":-1,"httpOnly":false,"secure":false,"sameSite":"Lax"},{"name":"JSESSIONID","value":"41674d71aa39e769","domain":".nr-data.net","path":"/","expires":-1,"httpOnly":false,"secure":true,"sameSite":"None"},{"name":"SESSION","value":"Y2VhOTg2YzktMmQwYi00ODk5LWFkZjQtZjY3ZDdiYWNhYzc0","domain":"tiger.talkdeskid.com","path":"/","expires":-1,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"SESSION","value":"MTI0YzA5MWUtNzc3Mi00YThjLWFiYTUtN2FmNzY2ODM0ZGQ3","domain":"jingwei.trytalkdesk.com","path":"/atlas/","expires":-1,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"ATLAS_DEVICE_ID","value":"de664737-17d8-4d13-8dd8-306a43cd587a","domain":".trytalkdesk.com","path":"/atlas","expires":1706669988.809223,"httpOnly":true,"secure":false,"sameSite":"Lax"},{"name":"X-Platform-Tid","value":"9cabe775-741f-442e-9f35-07a817c1359f","domain":"jingwei.talkdeskqaid.com","path":"/","expires":-1,"httpOnly":false,"secure":false,"sameSite":"Lax"},{"name":"SESSION","value":"OWRmYzRhMTItNGY2MC00YTljLTkxNTYtMmZlMzI5Mjc4ZWQ4","domain":"dialer-team-qa-aux.trytalkdesk.com","path":"/atlas/","expires":-1,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"SESSION","value":"OTI0NjcyNGYtODBkZS00MjI0LWI4YWMtYjdjMGM5ZTljZjgy","domain":"email-qa.trytalkdesk.com","path":"/atlas/","expires":-1,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"SESSION","value":"Yzg3YTQ1MGEtMDIzZi00NTU5LWI0MmUtYTQ0ZDhhYjgzYjk3","domain":"qa-yeva.trytalkdesk.com","path":"/atlas/","expires":-1,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"SESSION","value":"OTQ2MjhlOTAtNDAzMi00YTZmLWE5ZDItN2Y4NmU2YTU5ODZj","domain":"diting.trytalkdesk.com","path":"/atlas/","expires":-1,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"SESSION","value":"NTlkMGUwYzYtYTA5ZS00OTliLThlNDUtOWJkM2RlNzVlY2Y4","domain":"deeting-fe.gettalkdesk.com","path":"/atlas/","expires":-1,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"ATLAS_DEVICE_ID","value":"76f94700-228e-4a20-90d1-2f3f3e527aba","domain":".gettalkdesk.com","path":"/atlas","expires":1706671745.599724,"httpOnly":true,"secure":false,"sameSite":"Lax"},{"name":"X-Platform-Tid","value":"a176a748-81bb-435a-aa17-82187787d68c","domain":"deeting-fe.talkdeskstgid.com","path":"/","expires":-1,"httpOnly":false,"secure":false,"sameSite":"Lax"},{"name":"SESSION","value":"OTI5NDdjOTMtODU3MS00MmM2LTk5MzQtYzI4YWY5ZWU0YTg1","domain":"deeting-fe.talkdeskstgid.com","path":"/","expires":-1,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"SESSION","value":"OGE1M2VmNmItYWZkNi00YzRiLTk1OWYtNmFkZWNlZDQwZTg5","domain":"deeting.mytalkdesk.com","path":"/atlas/","expires":-1,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"SESSION","value":"ODRlZmRjNjgtMjMyMC00Yzg0LWJiNGEtZmVjNDNjNDNjNDFh","domain":"outbound-tiger.mytalkdesk.eu","path":"/atlas/","expires":-1,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"ATLAS_DEVICE_ID","value":"19cc444c-5340-4645-9512-1e6abbeab9e4","domain":".mytalkdesk.eu","path":"/atlas","expires":1706672132.910514,"httpOnly":true,"secure":false,"sameSite":"Lax"},{"name":"X-Platform-Tid","value":"5b41f23e-7d3b-4ca5-a97c-bef0c7edaddb","domain":"outbound-tiger.talkdeskid.eu","path":"/","expires":-1,"httpOnly":false,"secure":false,"sameSite":"Lax"},{"name":"SESSION","value":"NDViYTlhZTktM2Y3ZC00OWQwLTkzOTktMDYyZGQ5ODMxMWM2","domain":"outbound-tiger.talkdeskid.eu","path":"/","expires":-1,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"SESSION","value":"YzEwN2VmMTAtZDllNS00OTBjLTlhYTgtZDgxYTdmZjMwMWVl","domain":"po-automationca.mytalkdeskca.com","path":"/atlas/","expires":-1,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"ATLAS_DEVICE_ID","value":"06be50fc-ee6f-45ef-bf51-10cf49ce785e","domain":".mytalkdeskca.com","path":"/atlas","expires":1706672232.625874,"httpOnly":true,"secure":false,"sameSite":"Lax"},{"name":"X-Platform-Tid","value":"8f72fbf3-2c5e-4a37-8156-22e9f655bb3b","domain":"po-automationca.talkdeskidca.com","path":"/","expires":-1,"httpOnly":false,"secure":false,"sameSite":"Lax"},{"name":"SESSION","value":"ZjVkYjdlNDMtMmFjNy00MDE3LWI3ZjEtMDgxOTEwMWU0Y2Zm","domain":"po-automationca.talkdeskidca.com","path":"/","expires":-1,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"SESSION","value":"MDBmZDEyYzctMzI2Zi00YTVjLTk5MmMtODI0Njc0MGY3NzU4","domain":"po-automation.mytalkdesk.eu","path":"/atlas/","expires":-1,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"mongoMachineId","value":"3342665","domain":"diting.trytalkdesk.com","path":"/","expires":**********.520341,"httpOnly":false,"secure":false,"sameSite":"Lax"},{"name":"tdaccount","value":"diting","domain":"diting.trytalkdesk.com","path":"/","expires":-1,"httpOnly":false,"secure":true,"sameSite":"Lax"},{"name":"_talkdesk_session","value":"c2e6e48024c1e7898607f33debd70af1","domain":"diting.trytalkdesk.com","path":"/","expires":-1,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"X-Platform-Tid","value":"0de2b405-b438-4938-961a-7178b39010d6","domain":"diting.talkdeskqaid.com","path":"/","expires":-1,"httpOnly":false,"secure":false,"sameSite":"Lax"},{"name":"SESSION","value":"MWI5ZWY0YTQtZTE5Ni00NTZhLTgyMTMtNTczNWFkZTc1Njk2","domain":"diting.talkdeskqaid.com","path":"/","expires":-1,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"SESSION","value":"NmFkNWM4ZDgtZTI1Ny00MGYyLTkwYjYtZjkwNmExMjg2OTU4","domain":"jingwei.talkdeskqaid.com","path":"/","expires":-1,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"X-Platform-Tid","value":"f2577081-dafd-437e-9149-ddc01c60f983","domain":"po-automation.talkdeskid.eu","path":"/","expires":-1,"httpOnly":false,"secure":false,"sameSite":"Lax"},{"name":"SESSION","value":"Y2E4OGU2ZDYtNzBlMi00ZDhkLTk0Y2EtYTZmMDg1MGU4MDUw","domain":"po-automation.talkdeskid.eu","path":"/","expires":-1,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"X-Platform-Tid","value":"1da22c41-d904-4bad-928c-f09d943234eb","domain":"dialer-team-qa-aux.talkdeskqaid.com","path":"/","expires":-1,"httpOnly":false,"secure":false,"sameSite":"Lax"},{"name":"SESSION","value":"ZTc3YzBjMGUtOWFiOS00NGRkLWI5NzYtYzBhNmYwZDc3OWZl","domain":"dialer-team-qa-aux.talkdeskqaid.com","path":"/","expires":-1,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"X-Platform-Tid","value":"ca1305b2-6d06-4446-bf96-398b2d4c21bf","domain":"deeting.talkdeskid.com","path":"/","expires":-1,"httpOnly":false,"secure":false,"sameSite":"Lax"},{"name":"SESSION","value":"ZjRiNGEwNTgtZTZhNC00NGViLWI4MTYtNTk5YTlmOTc3ZmM1","domain":"deeting.talkdeskid.com","path":"/","expires":-1,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"AWSALBCORS","value":"ILR6rgXARXNVWnTuUQiiLK3QyLPtVqQS2GZIZyyH2fbJ4Iv3AOd2sb5VxqXVdvefh90IdSFp8S+o0O2DJaxhpfTLOJPt45pef0gjlUSX3EU5BXHFI/fQbXmIQUXD","domain":"webrtc.cpaas.talkdeskqa.com","path":"/","expires":1679715457.137436,"httpOnly":false,"secure":true,"sameSite":"None"},{"name":"SESSION","value":"YjFiZWVlYzktZTg5Zi00NWQ0LWFkODEtMzhmNjlmZjBmNzg3","domain":"jingwei.trytalkdesk.com","path":"/atlas","expires":-1,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"SESSION","value":"MTgxMzAzMDYtNjY1Yi00NWU2LWE2ZjQtYWQzZTc1YTI4ZGRl","domain":"cfm-automatic.trytalkdesk.com","path":"/atlas","expires":-1,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"X-Platform-Tid","value":"dcf6f925-b8c3-4700-b8a7-80b7b878adab","domain":"cfm-automatic.talkdeskqaid.com","path":"/","expires":-1,"httpOnly":false,"secure":false,"sameSite":"Lax"},{"name":"SESSION","value":"ZTRiODE1MjEtZWMxZi00YjU5LTg1YzItYzQ5ZTU4NTZmOWVj","domain":"cfm-automatic.talkdeskqaid.com","path":"/","expires":-1,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"SESSION","value":"OTM4MmRjNjAtYTFmMy00OGZkLWFkNmItNWU2YWI3NDEzYTJk","domain":"po-automation.mytalkdesk.eu","path":"/atlas","expires":-1,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"SESSION","value":"YmY5MWY2ZTctYWYyNS00ZTdlLWI2ZjYtYTU2ZmY0OWFlZDdm","domain":"tiger.mytalkdesk.com","path":"/atlas","expires":-1,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"test_cookie","value":"CheckForPermission","domain":".doubleclick.net","path":"/","expires":1693564161.931661,"httpOnly":false,"secure":true,"sameSite":"None"},{"name":"_gd_visitor","value":"b7e80772-8708-4fe6-8e47-505c5b21660a","domain":"td-frontend-app.meza.talkdeskqa.com","path":"/","expires":1728123261.973548,"httpOnly":false,"secure":true,"sameSite":"None"},{"name":"_gd_session","value":"555026b8-0947-4443-8afa-7c4a72829db4","domain":"td-frontend-app.meza.talkdeskqa.com","path":"/","expires":1693577661,"httpOnly":false,"secure":true,"sameSite":"None"},{"name":"__cf_bm","value":"G_XrFgwas9cK5xEzpm1GDZ_roVG4S1d6U.wQE04zk_Q-1693563262-0-AdXrhQ1WTAYxVTkTIhA9rMEEO11aArFyyiKEx+22hpbqe1ijxFiHTFAn1SrRz2exggWh3T1Ecp4V0EDItEJGDkA=","domain":".learn.talkdesk.com","path":"/","expires":1693565062.277784,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"MUID","value":"09E1A783187861190F14B4FC19A1609F","domain":".bing.com","path":"/","expires":1727259262.716438,"httpOnly":false,"secure":true,"sameSite":"None"},{"name":"MR","value":"0","domain":".bat.bing.com","path":"/","expires":1694168062.716535,"httpOnly":false,"secure":true,"sameSite":"None"},{"name":"AWSALBTGCORS","value":"uNyjpkdRB7hLt6GRdAbskfNDPECW+7hkZpwSFDzbWoYvBrXVdaGZbg6eAxoY5Pff1YFsUqiGywbjaxl5R35Y1d9lvJJARirr7Di7YZki2oJ7XFKE6dG3YiMBqsw786REkNdVu44Ylif6KAbxnUhTQhthLviHO0CwJqffq3jA48Wvmso71hc=","domain":"talkdeskinc.us-5.evergage.com","path":"/","expires":1694168062.89109,"httpOnly":false,"secure":true,"sameSite":"None"},{"name":"__cflb","value":"0H28vkj1CsG1kVja9aXDWFTuLhooECYNUox2Ujg4gAw","domain":"tiger.talkdeskid.com","path":"/","expires":1715676417.636742,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"SESSION","value":"Y2I1ZmE1MDAtMGFkYS00MGE2LWFkNDUtODk1YWMwYzNiNzRk","domain":"dialerintegrations.mytalkdesk.eu","path":"/atlas","expires":-1,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"__cflb","value":"0H28v1ZSB7SR68a6L8SoJge4ZzLtDkizaNNznGtiU3m","domain":"dialerintegrations.talkdeskid.eu","path":"/","expires":1715676885.193663,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"SESSION","value":"YjM0MmVmMDYtMWQ5OS00ZDcxLTg2OWUtOThlZWQ0NTgyZDQ0","domain":"dialerintegrations.talkdeskid.eu","path":"/","expires":-1,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"X-Platform-Tid","value":"fb84b65b-ad49-4be2-afa6-f9916c78376e","domain":"dialerintegrations.talkdeskid.eu","path":"/","expires":-1,"httpOnly":false,"secure":false,"sameSite":"Lax"},{"name":"dtCookie","value":"v_4_srv_6_sn_49790B898BE917FEC0E0C457E0A60E47_perc_100000_ol_0_mul_1_app-3Aea7c4b59f27d43eb_1","domain":".mytalkdesk.eu","path":"/","expires":-1,"httpOnly":false,"secure":true,"sameSite":"Lax"},{"name":"__cflb","value":"0H28vinTqC1t14n4CPrgvpqfCWcVE3gm44nz41BUKGX","domain":"po-automation.talkdeskid.eu","path":"/","expires":1748548905.15815,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"dtCookie","value":"v_4_srv_8_sn_3950A7C0EE07C86DF7E599E0A4503CF1_perc_100000_ol_0_mul_1_app-3Aea7c4b59f27d43eb_1_app-3A541303d2f763bcff_1","domain":".talkdeskid.eu","path":"/","expires":-1,"httpOnly":false,"secure":true,"sameSite":"Lax"},{"name":"rxVisitor","value":"1747944105325443M4MQ3O1VIKC6MF0QD7TQTLHDKD331","domain":".talkdeskid.eu","path":"/","expires":-1,"httpOnly":false,"secure":true,"sameSite":"Lax"},{"name":"rxvt","value":"1747945905727|1747944105327","domain":".talkdeskid.eu","path":"/","expires":-1,"httpOnly":false,"secure":true,"sameSite":"Lax"},{"name":"dtSa","value":"false%7CC%7C3%7CLogin%7Cfetch%7C1747944105725%7C144105324_676%7Chttps%3A%2F%2Fpo-automation.talkdeskid.eu%2Flogin%7C%7C%7C%7C","domain":".talkdeskid.eu","path":"/","expires":-1,"httpOnly":false,"secure":true,"sameSite":"Lax"},{"name":"dtPC","value":"8$144105324_676h-vRELRNPKDAWMWCCVMRMHPRHFDLCEPLUHH-0e0","domain":".talkdeskid.eu","path":"/","expires":-1,"httpOnly":false,"secure":true,"sameSite":"Lax"}],"origins":[{"origin":"https://tiger.mytalkdesk.com","localStorage":[{"name":"USER_PREFERENCES/62c6b963bbdb3a26c0b26249","value":"{\"drawerFixed\":null,\"secondaryAreaFixed\":null,\"secondaryAreaOpenedApp\":{\"icon\":{\"fallbackUrl\":\"https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/app_helper.svg\"},\"title\":\"Smart Help\",\"selected\":false,\"badge\":{\"hasError\":false,\"hasOngoingAction\":false},\"slug\":\"contextual-help\",\"path\":\"/\",\"key\":\"contextual-help\",\"type\":\"droplet\",\"location\":{\"slug\":\"contextual-help\",\"path\":\"\",\"search\":\"\",\"pathLength\":0,\"url\":\"contextual-help\"}},\"userPrefersDrawerOverSecondary\":false}"},{"name":"_pendo_visitorId.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"{\"ttl\":*************,\"value\":\"62c6b963bbdb3a26c0b26249\"}"},{"name":"API_USER_PREFERENCES/62c6b963bbdb3a26c0b26249","value":"{\"language\":\"en-US\",\"alwaysOnTop\":true}"},{"name":"_pendo_accountId.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"{\"ttl\":*************,\"value\":\"619f4d30f5d6b0e4e7563024\"}"},{"name":"_pendo_meta.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"{\"ttl\":*************,\"value\":*********}"},{"name":"NRBA_SESSION","value":"{\"value\":\"b7da7565fbc81da7\",\"inactiveAt\":*************,\"expiresAt\":*************,\"updatedAt\":*************,\"sessionReplayMode\":0,\"sessionReplaySentFirstChunk\":false,\"sessionTraceMode\":0,\"traceHarvestStarted\":false,\"serverTimeDiff\":405,\"custom\":{}}"}]},{"origin":"https://prd-cdn-talkdesk.talkdesk.com","localStorage":[{"name":"_pendo_visitorId.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"{\"ttl\":*************,\"value\":\"637b6dce34a968316773099e\"}"},{"name":"_pendo_guides_blocked.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"{\"ttl\":*************,\"value\":\"0\"}"},{"name":"_pendo_sessionId.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"{\"ttl\":*************,\"value\":\"{\\\"sessionId\\\":\\\"6LSODiL8BTDaWIOL\\\",\\\"timestamp\\\":*************}\"}"},{"name":"_pendo_accountId.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"{\"ttl\":*************,\"value\":\"637b656738e6007d9c4e004b\"}"},{"name":"_pendo_meta.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"{\"ttl\":*************,\"value\":**********}"}]},{"origin":"https://jingwei.trytalkdesk.com","localStorage":[{"name":"NRBA_SESSION","value":"{\"value\":\"821b26c950a9312f\",\"inactiveAt\":*************,\"expiresAt\":*************,\"updatedAt\":*************,\"sessionReplay\":0,\"sessionTraceMode\":0,\"custom\":{}}"},{"name":"USER_PREFERENCES/undefined","value":"{\"drawerFixed\":null,\"secondaryAreaFixed\":null,\"secondaryAreaOpenedApp\":null,\"userPrefersDrawerOverSecondary\":false}"},{"name":"pusherTransportTLS","value":"{\"timestamp\":*************,\"transport\":\"ws\",\"latency\":628}"}]},{"origin":"https://qa-cdn-talkdesk.talkdeskdev.com","localStorage":[{"name":"bugsnag-anonymous-id","value":"cle5ndc9y00003b6aoivppive"}]},{"origin":"https://dialer-team-qa-aux.trytalkdesk.com","localStorage":[{"name":"USER_PREFERENCES/undefined","value":"{\"drawerFixed\":null,\"secondaryAreaFixed\":null,\"secondaryAreaOpenedApp\":null,\"userPrefersDrawerOverSecondary\":false}"},{"name":"pusherTransportTLS","value":"{\"timestamp\":1676463983566,\"transport\":\"ws\",\"latency\":980}"}]},{"origin":"https://email-qa.trytalkdesk.com","localStorage":[{"name":"USER_PREFERENCES/undefined","value":"{\"drawerFixed\":null,\"secondaryAreaFixed\":null,\"secondaryAreaOpenedApp\":null,\"userPrefersDrawerOverSecondary\":false}"},{"name":"pusherTransportTLS","value":"{\"timestamp\":1675520085671,\"transport\":\"ws\",\"latency\":1311}"}]},{"origin":"https://qa-yeva.trytalkdesk.com","localStorage":[{"name":"USER_PREFERENCES/undefined","value":"{\"drawerFixed\":null,\"secondaryAreaFixed\":null,\"secondaryAreaOpenedApp\":null,\"userPrefersDrawerOverSecondary\":false}"},{"name":"pusherTransportTLS","value":"{\"timestamp\":1675135570555,\"transport\":\"ws\",\"latency\":964}"}]},{"origin":"https://diting.trytalkdesk.com","localStorage":[{"name":"_pendo_visitorId.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"{\"ttl\":*************,\"value\":\"63a3c66d7d6a980ae307229e\"}"},{"name":"_pendo_guides_blocked.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"{\"ttl\":*************,\"value\":\"0\"}"},{"name":"USER_PREFERENCES/undefined","value":"{\"drawerFixed\":null,\"secondaryAreaFixed\":null,\"secondaryAreaOpenedApp\":null,\"userPrefersDrawerOverSecondary\":false}"},{"name":"_pendo_accountId.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"612e017f14d9b0e42c81f132"},{"name":"pusherTransportTLS","value":"{\"timestamp\":*************,\"transport\":\"ws\",\"latency\":919}"},{"name":"_pendo_meta.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"*********"},{"name":"mongoMachineId","value":"3342665"}]},{"origin":"https://deeting-fe.gettalkdesk.com","localStorage":[{"name":"_pendo_visitorId.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"{\"ttl\":*************,\"value\":\"63b38cc65399951be5a85bfa\"}"},{"name":"_pendo_guides_blocked.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"{\"ttl\":*************,\"value\":\"0\"}"},{"name":"USER_PREFERENCES/undefined","value":"{\"drawerFixed\":null,\"secondaryAreaFixed\":null,\"secondaryAreaOpenedApp\":null,\"userPrefersDrawerOverSecondary\":false}"},{"name":"_pendo_accountId.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"6142f79f738eb0e4a44b18c4"},{"name":"pusherTransportTLS","value":"{\"timestamp\":*************,\"transport\":\"ws\",\"latency\":1529}"},{"name":"_pendo_meta.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"**********"}]},{"origin":"https://stg-cdn-talkdesk.talkdeskdev.com","localStorage":[{"name":"bugsnag-anonymous-id","value":"cldjokn6100003969jvrgra4y"}]},{"origin":"https://deeting.mytalkdesk.com","localStorage":[{"name":"_pendo_visitorId.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"{\"ttl\":*************,\"value\":\"6410355f136e3c7d91f3b4f7\"}"},{"name":"_pendo_guides_blocked.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"{\"ttl\":*************,\"value\":\"0\"}"},{"name":"USER_PREFERENCES/undefined","value":"{\"drawerFixed\":null,\"secondaryAreaFixed\":null,\"secondaryAreaOpenedApp\":{\"icon\":{\"fallbackUrl\":\"https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/agent-assistant.svg\"},\"title\":\"Product Help\",\"selected\":false,\"badge\":{\"hasError\":false,\"hasOngoingAction\":false},\"slug\":\"contextual-help\",\"path\":\"/\",\"key\":\"contextual-help\",\"type\":\"droplet\",\"location\":{\"slug\":\"contextual-help\",\"path\":\"\",\"search\":\"\",\"pathLength\":0,\"url\":\"contextual-help\"}},\"userPrefersDrawerOverSecondary\":false}"},{"name":"_pendo_accountId.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"618a35d25b1fb0e4a4931dad"},{"name":"pusherTransportTLS","value":"{\"timestamp\":*************,\"transport\":\"ws\",\"latency\":847}"},{"name":"_pendo_lastStepAdvanced.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"{\"ttl\":*************,\"value\":\"[{\\\"tabId\\\":\\\"WWBYFTxMhHzWhtVH\\\",\\\"guideId\\\":\\\"7o-hPxO6EjC3yXoacnNDH6nX9r0\\\",\\\"guideStepId\\\":\\\"dbAJ0nsqSJhDcLhZY5KI5akCZCE\\\",\\\"time\\\":*************,\\\"state\\\":\\\"active\\\",\\\"seenReason\\\":\\\"auto\\\",\\\"visitorId\\\":\\\"6410355f136e3c7d91f3b4f7\\\"}]\"}"},{"name":"_pendo_meta.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"**********"}]},{"origin":"https://outbound-tiger.mytalkdesk.eu","localStorage":[{"name":"_pendo_visitorId.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"{\"ttl\":*************,\"value\":\"62e9e0cf4bda17732aea92de\"}"},{"name":"_pendo_guides_blocked.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"{\"ttl\":*************,\"value\":\"0\"}"},{"name":"USER_PREFERENCES/undefined","value":"{\"drawerFixed\":null,\"secondaryAreaFixed\":null,\"secondaryAreaOpenedApp\":{\"icon\":{\"fallbackUrl\":\"https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/agent-assistant.svg\"},\"title\":\"Agent Assist\",\"selected\":false,\"badge\":{\"hasError\":false,\"hasOngoingAction\":false},\"slug\":\"agent-assist-secondary\",\"path\":\"/\",\"key\":\"agent-assist-secondary\",\"type\":\"droplet\",\"location\":{\"slug\":\"agent-assist-secondary\",\"path\":\"\",\"search\":\"\",\"pathLength\":0,\"url\":\"agent-assist-secondary\"}},\"userPrefersDrawerOverSecondary\":false}"},{"name":"_pendo_accountId.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"62e9dec86ec83c24420b280d"},{"name":"pusherTransportTLS","value":"{\"timestamp\":*************,\"transport\":\"ws\",\"latency\":1439}"},{"name":"_pendo_meta.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"**********"}]},{"origin":"https://po-automationca.mytalkdeskca.com","localStorage":[{"name":"_pendo_visitorId.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"{\"ttl\":*************,\"value\":\"637b6d301f05cb5c9508b7dc\"}"},{"name":"_pendo_guides_blocked.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"{\"ttl\":*************,\"value\":\"0\"}"},{"name":"USER_PREFERENCES/undefined","value":"{\"drawerFixed\":null,\"secondaryAreaFixed\":null,\"secondaryAreaOpenedApp\":{\"icon\":{\"fallbackUrl\":\"https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/agent-assistant.svg\"},\"title\":\"Agent Assist\",\"selected\":false,\"badge\":{\"hasError\":false,\"hasOngoingAction\":false},\"slug\":\"agent-assist-secondary\",\"path\":\"/\",\"key\":\"agent-assist-secondary\",\"type\":\"droplet\",\"location\":{\"slug\":\"agent-assist-secondary\",\"path\":\"\",\"search\":\"\",\"pathLength\":0,\"url\":\"agent-assist-secondary\"}},\"userPrefersDrawerOverSecondary\":false}"},{"name":"_pendo_accountId.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"637b683d24e55622d4899257"},{"name":"pusherTransportTLS","value":"{\"timestamp\":*************,\"transport\":\"ws\",\"latency\":1146}"},{"name":"_pendo_meta.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"**********"}]},{"origin":"https://po-automation.mytalkdesk.eu","localStorage":[{"name":"USER_PREFERENCES/637b6dce34a968316773099e","value":"{\"drawerFixed\":null,\"secondaryAreaFixed\":true,\"secondaryAreaOpenedApp\":{\"icon\":{\"fallbackUrl\":\"https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/agent-assistant.svg\"},\"title\":\"Copilot\",\"selected\":false,\"badge\":{\"hasError\":false,\"hasOngoingAction\":false},\"slug\":\"agent-assist-secondary\",\"path\":\"/\",\"key\":\"agent-assist-secondary\",\"type\":\"droplet\",\"location\":{\"slug\":\"agent-assist-secondary\",\"path\":\"\",\"search\":\"\",\"pathLength\":0,\"url\":\"agent-assist-secondary\"}},\"userPrefersDrawerOverSecondary\":false}"},{"name":"_pendo_visitorId.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"{\"ttl\":*************,\"value\":\"637b6dce34a968316773099e\"}"},{"name":"_pendo_guides_blocked.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"{\"ttl\":*************,\"value\":\"0\"}"},{"name":"_pendo_sessionId.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"{\"ttl\":*************,\"value\":\"{\\\"sessionId\\\":\\\"ltj0a1zaqPeG2n3h\\\",\\\"timestamp\\\":*************}\"}"},{"name":"_pendo_accountId.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"{\"ttl\":*************,\"value\":\"637b656738e6007d9c4e004b\"}"},{"name":"_pendo_meta.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"{\"ttl\":*************,\"value\":**********}"}]},{"origin":"https://td-frontend-app.meza.talkdeskqa.com","localStorage":[{"name":"td_marketing","value":"{\"thankYou\":{\"lastResourceName\":\"fourzerofour | Talkdesk\"}}"},{"name":"uc_ui_version","value":"3.26.0"}]},{"origin":"https://www.talkdesk.com","localStorage":[{"name":"uc_ui_version","value":"3.26.0"}]},{"origin":"https://dialerintegrations.mytalkdesk.eu","localStorage":[{"name":"USER_PREFERENCES/6639b60a55687e5a333dc666","value":"{\"drawerFixed\":null,\"secondaryAreaFixed\":null,\"secondaryAreaOpenedApp\":{\"icon\":{\"fallbackUrl\":\"https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/agent-assistant.svg\"},\"title\":\"Copilot\",\"selected\":false,\"badge\":{\"hasError\":false,\"hasOngoingAction\":false},\"slug\":\"agent-assist-secondary\",\"path\":\"/\",\"key\":\"agent-assist-secondary\",\"type\":\"droplet\",\"location\":{\"slug\":\"agent-assist-secondary\",\"path\":\"\",\"search\":\"\",\"pathLength\":0,\"url\":\"agent-assist-secondary\"}},\"userPrefersDrawerOverSecondary\":false}"},{"name":"_pendo_visitorId.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"{\"ttl\":*************,\"value\":\"6639b60a55687e5a333dc666\"}"},{"name":"_pendo_accountId.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"{\"ttl\":*************,\"value\":\"65b789aa3595f951cfb5cc8c\"}"},{"name":"_pendo_meta.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"{\"ttl\":*************,\"value\":**********}"},{"name":"API_USER_PREFERENCES/6639b60a55687e5a333dc666","value":"{\"language\":\"en-US\",\"alwaysOnTop\":true}"},{"name":"pusherTransportTLS","value":"{\"timestamp\":*************,\"transport\":\"ws\",\"latency\":7048,\"cacheSkipCount\":0}"},{"name":"NRBA_SESSION","value":"{\"value\":\"ac39654405eebe04\",\"inactiveAt\":*************,\"expiresAt\":*************,\"updatedAt\":*************,\"sessionReplayMode\":0,\"sessionReplaySentFirstChunk\":false,\"sessionTraceMode\":0,\"traceHarvestStarted\":false,\"serverTimeDiff\":-323,\"custom\":{}}"}]}]}},"platform":"linux","wallTime":*************,"monotonicTime":64204.177,"sdkLanguage":"javascript","testIdAttributeName":"data-testid","channel":"chromium","title":"smoke/test_campaign_record_flow_clone.spec.js:565 › Smoke-Predicative Campaign Create/Start/Pause/Resume/View/Edit/Check/Dup"}
{"type":"before","callId":"call@494","startTime":64206.941,"apiName":"browserContext.newPage","class":"BrowserContext","method":"newPage","params":{},"stepId":"pw:api@6","beforeSnapshot":"before@call@494"}
{"type":"event","time":64245.965,"class":"BrowserContext","method":"page","params":{"pageId":"page@ab6c8088dd9487f369c16d7dd0446acd"}}
{"type":"after","callId":"call@494","endTime":64246.146,"result":{"page":"<Page>"},"afterSnapshot":"after@call@494"}
{"type":"before","callId":"call@496","startTime":64260.24,"apiName":"page.goto","class":"Frame","method":"goto","params":{"url":"/atlas/apps/outbound-dialer","waitUntil":"load"},"stepId":"pw:api@9","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@496"}
{"type":"before","callId":"call@498","startTime":64261.263,"apiName":"page.waitForNavigation","class":"Page","method":"waitForEventInfo","params":{"info":{"waitId":"9cb2411f842504a72ac3a9810453093b","phase":"before","event":""}},"stepId":"pw:api@10","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@498"}
{"type":"log","callId":"call@498","time":64262.181,"message":"waiting for navigation until \"load\""}
{"type":"log","callId":"call@496","time":64264.736,"message":"navigating to \"https://po-automation.mytalkdesk.eu/atlas/apps/outbound-dialer\", waiting until \"load\""}
{"type":"log","callId":"call@498","time":64911.397,"message":"  navigated to \"https://po-automation.mytalkdesk.eu/atlas/apps/outbound-dialer\""}
{"type":"console","messageType":"error","text":"Refused to load the script 'https://po-automation.mytalkdesk.eu/atlas/ruxitagentjs_ICA7NVfqrux_10313250422105919.js' because it violates the following Content Security Policy directive: \"script-src 'nonce-PmAy21iDRIBaReIUMmCxyOKwePpph9fpwAkZToi8NFHHOhjtA4pDLBM7sYTY77OdXrOg81j3Y8uWPcy2VxICCPjFAJGeKm/bVPTLE+SmokzbgqddTGEEOGkANkYDUp+D73V9q2Hxoe3NJMLrpLBz5DeDttpb6ne+PEmWezKhAwc=' 'strict-dynamic' 'unsafe-inline' http: https:\". Note that 'strict-dynamic' is present, so host-based allowlisting is disabled. Note that 'script-src-elem' was not explicitly set, so 'script-src' is used as a fallback.\n","args":[],"location":{"url":"https://po-automation.mytalkdesk.eu/atlas/apps/outbound-dialer","lineNumber":0,"columnNumber":0},"time":64916.016,"pageId":"page@ab6c8088dd9487f369c16d7dd0446acd"}
{"type":"log","callId":"call@498","time":65463.069,"message":"  \"domcontentloaded\" event fired"}
{"type":"log","callId":"call@498","time":65578.184,"message":"  \"load\" event fired"}
{"type":"after","callId":"call@498","endTime":65580.302,"afterSnapshot":"after@call@498"}
{"type":"before","callId":"call@512","startTime":65580.843,"apiName":"page.waitForTimeout","class":"Frame","method":"waitForTimeout","params":{"timeout":3000},"stepId":"pw:api@11","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@512"}
{"type":"console","messageType":"log","text":"Fetching new desktop versions is not available on Web engine.","args":[{"preview":"Fetching new desktop versions is not available on Web engine.","value":"Fetching new desktop versions is not available on Web engine."}],"location":{"url":"https://prd-cdn-talkdesk.talkdesk.com/atlas/0.176.12_atlas-runtime/runtime/main.js","lineNumber":1,"columnNumber":2547123},"time":65603.359,"pageId":"page@ab6c8088dd9487f369c16d7dd0446acd"}
{"type":"after","callId":"call@496","endTime":65576.046,"result":{"response":"<Response>"},"afterSnapshot":"after@call@496"}
{"type":"console","messageType":"startGroup","text":"Additional details:","args":[{"preview":"Additional details:","value":"Additional details:"}],"location":{"url":"https://prd-cdn-talkdesk.talkdesk.com/atlas/0.176.12_atlas-runtime/runtime/main.js","lineNumber":1,"columnNumber":945484},"time":66585.749,"pageId":"page@ab6c8088dd9487f369c16d7dd0446acd"}
{"type":"console","messageType":"endGroup","text":"console.groupEnd","args":[{"preview":"console.groupEnd","value":"console.groupEnd"}],"location":{"url":"https://prd-cdn-talkdesk.talkdesk.com/atlas/0.176.12_atlas-runtime/runtime/main.js","lineNumber":1,"columnNumber":945536},"time":66585.931,"pageId":"page@ab6c8088dd9487f369c16d7dd0446acd"}
{"type":"console","messageType":"error","text":"Error: The 'voicemails:show-details' protocol is already registered. Skipping registration.\n    at new t (https://prd-cdn-talkdesk.talkdesk.com/atlas/0.176.12_atlas-runtime/runtime/main.js:2:2378270)\n    at Object.on (https://prd-cdn-talkdesk.talkdesk.com/atlas/0.176.12_atlas-runtime/runtime/main.js:2:2378614)\n    at Proxy.<anonymous> (https://prd-cdn-talkdesk.talkdesk.com/atlas/0.176.12_atlas-runtime/runtime/main.js:2:3103309)\n    at d (https://prd-cdn-talkdesk.talkdesk.com/atlas/0.176.12_atlas-runtime/runtime/main.js:2:2525762)\n    at service (https://prd-cdn-talkdesk.talkdesk.com/atlas/0.176.12_atlas-runtime/runtime/main.js:2:2526339)\n    at https://prd-cdn-talkdesk.talkdesk.com/atlas/0.176.12_atlas-runtime/runtime/main.js:2:2519006\n    at Array.reduce (<anonymous>)\n    at e.value (https://prd-cdn-talkdesk.talkdesk.com/atlas/0.176.12_atlas-runtime/runtime/main.js:2:2518896)\n    at j.r (https://prd-cdn-talkdesk.talkdesk.com/atlas/0.176.12_atlas-runtime/runtime/main.js:2:2518060)\n    at j.emit (https://prd-cdn-talkdesk.talkdesk.com/atlas/0.176.12_atlas-runtime/runtime/main.js:2:1026259)","args":[{"preview":"Error: The 'voicemails:show-details' protocol is already registered. Skipping registration.\n    at new t (https://prd-cdn-talkdesk.talkdesk.com/atlas/0.176.12_atlas-runtime/runtime/main.js:2:2378270)\n    at Object.on (https://prd-cdn-talkdesk.talkdesk.com/atlas/0.176.12_atlas-runtime/runtime/main.js:2:2378614)\n    at Proxy.<anonymous> (https://prd-cdn-talkdesk.talkdesk.com/atlas/0.176.12_atlas-runtime/runtime/main.js:2:3103309)\n    at d (https://prd-cdn-talkdesk.talkdesk.com/atlas/0.176.12_atlas-runtime/runtime/main.js:2:2525762)\n    at service (https://prd-cdn-talkdesk.talkdesk.com/atlas/0.176.12_atlas-runtime/runtime/main.js:2:2526339)\n    at https://prd-cdn-talkdesk.talkdesk.com/atlas/0.176.12_atlas-runtime/runtime/main.js:2:2519006\n    at Array.reduce (<anonymous>)\n    at e.value (https://prd-cdn-talkdesk.talkdesk.com/atlas/0.176.12_atlas-runtime/runtime/main.js:2:2518896)\n    at j.r (https://prd-cdn-talkdesk.talkdesk.com/atlas/0.176.12_atlas-runtime/runtime/main.js:2:2518060)\n    at j.emit (https://prd-cdn-talkdesk.talkdesk.com/atlas/0.176.12_atlas-runtime/runtime/main.js:2:1026259)"}],"location":{"url":"https://prd-cdn-talkdesk.talkdesk.com/atlas/0.176.12_atlas-runtime/runtime/main.js","lineNumber":1,"columnNumber":2526300},"time":66781.775,"pageId":"page@ab6c8088dd9487f369c16d7dd0446acd"}
{"type":"console","messageType":"warning","text":"Unrecognized feature: 'speaker'.","args":[],"location":{"url":"https://prd-cdn-talkdesk.talkdesk.com/atlas/0.176.12_atlas-runtime/runtime/main.js","lineNumber":1,"columnNumber":0},"time":66838.898,"pageId":"page@ab6c8088dd9487f369c16d7dd0446acd"}
{"type":"console","messageType":"warning","text":"An iframe which has both allow-scripts and allow-same-origin for its sandbox attribute can escape its sandboxing.","args":[],"location":{"url":"https://prd-cdn-talkdesk.talkdesk.com/campaign-manager-ui/latest/index.html?monitoringConfig=eyJwcm92aWRlciI6ImR5bmF0cmFjZS1vdGxwIiwidXJsIjoiaHR0cHM6Ly90YWxrZGVzay1wcm9kLWV1LTEubGl2ZS5keW5hdHJhY2UuY29tL2FwaS92Mi9vdGxwIiwidG9rZW4iOiJkdDBjMDEuSUlIT1k1M0czQUhSWDJPUlJCNkhJREVaLjVLMjZVQUU1QjVHWVU1SVRMWFdPWUlGTzNMTDdYSlBFNEU3TkhOSjNSVEdaNkFXVlhNSlNIV0daWlJUMlFCQkYiLCJwcm9wZXJ0aWVzIjp7Im5hbWUiOiJvdXRib3VuZC1kaWFsZXItcHJkLXRkLWV1LTEtZXUtd2VzdC0xIiwiYmF0Y2hEZWxheSI6NTAwMCwibWV0cmljcyI6eyJpbnRlcnZhbCI6MTAwMDB9LCJpZ25vcmVVcmxzIjpbImV1XFwuanNcXC5sb2dzXFwuaW5zaWdodFxcLnJhcGlkN1xcLmNvbSIsInJcXC5sci1pblxcLmNvbSIsInJcXC5pbnRha2UtbHJcXC5jb20iLCJldmVudGd3XFwudHdpbGlvXFwuY29tIl0sInByb3BhZ2F0ZUhlYWRlclVybHMiOlsiYXBpXFwudGFsa2Rlc2thcHBcXC5ldSJdfSwiYXR0cmlidXRlcyI6eyJhdGxhcy51c2VyX2lkIjoiNjM3YjZkY2UzNGE5NjgzMTY3NzMwOTllIiwiYXRsYXMudGVuYW50X2lkIjoiNjM3YjY1NjczOGU2MDA3ZDljNGUwMDRiIiwiYXRsYXMudGVuYW50X25hbWUiOiJwby1hdXRvbWF0aW9uIiwiYXRsYXMudmVyc2lvbiI6IjAuMTc2LjEyIiwiYXRsYXMuc2Vzc2lvbl90aWQiOiJiODljMmViMi0xZmZhLTQ2ZDktODYwZC05YTg3YWNlZDQ3ZjMiLCJhdGxhcy5lbmdpbmVfdHlwZSI6IndlYiIsImF0bGFzLmVuZ2luZV92ZXJzaW9uIjoiMTMxLjAuNjc3OC4zMyIsImF0bGFzLmVuZ2luZV9leHRlbnNpb24iOiJ1bmtub3duIiwiYXRsYXMucmVnaW9uIjoidGQtZXUtMSIsImF0bGFzLnByb3ZpZGVyX3JlZ2lvbiI6ImV1LWNlbnRyYWwtMSIsImF0bGFzLmFjY291bnRfYWZmaW5pdHkiOiJldS13ZXN0LTEiLCJhdGxhcy51c2VyX2FnZW50IjoiTW96aWxsYS81LjAgKFdpbmRvd3MgTlQgMTAuMDsgV2luNjQ7IHg2NCkgQXBwbGVXZWJLaXQvNTM3LjM2IChLSFRNTCwgbGlrZSBHZWNrbykgQ2hyb21lLzEzMS4wLjY3NzguMzMgU2FmYXJpLzUzNy4zNiIsImF0bGFzLmVudiI6InByZCIsImF0bGFzLmFwcF9zbHVnIjoib3V0Ym91bmQtZGlhbGVyIiwiYXRsYXMub3duZXIiOiJjb250aW51b3VzZW5nLWRlZXRpbmcifX0%3D&nonce=ddf4f165-d29e-4438-b00f-02596c26d506&targetOrigin=https%3A%2F%2Fpo-automation.mytalkdesk.eu&depth=1","lineNumber":0,"columnNumber":0},"time":67077.309,"pageId":"page@ab6c8088dd9487f369c16d7dd0446acd"}
{"type":"console","messageType":"warning","text":"An iframe which has both allow-scripts and allow-same-origin for its sandbox attribute can escape its sandboxing.","args":[],"location":{"url":"https://prd-cdn-talkdesk.talkdesk.com/uc-voice-ui/latest/index.html?monitoringConfig=eyJwcm92aWRlciI6ImR5bmF0cmFjZSIsInNuaXBwZXRVcmwiOiJodHRwczovL2pzLWNkbi5keW5hdHJhY2UuY29tL2pzdGFnLzE4OGQ5NTZlYzBmL2JmMTgxODV1Z3EvZTliYzRlNDJjMTY5ZGFfY29tcGxldGUuanMiLCJhY2NvdW50SUQiOiIxNTk2MzQ3IiwiYmVhY29uIjoiYmFtLm5yLWRhdGEubmV0IiwiZXJyb3JCZWFjb24iOiJiYW0ubnItZGF0YS5uZXQiLCJsaWNlbnNlS2V5IjoiTlJCUi00ZDRmZTY5YmFjYzgxMzYzYmE4IiwidHJ1c3RLZXkiOiIzMzkxMTExIiwibmFtZSI6InVjLXZvaWNlIn0%3D&nonce=8d2f827c-a4ff-47ba-935f-dadcd908e266&targetOrigin=https%3A%2F%2Fpo-automation.mytalkdesk.eu&depth=1","lineNumber":0,"columnNumber":0},"time":67082.618,"pageId":"page@ab6c8088dd9487f369c16d7dd0446acd"}
{"type":"console","messageType":"warning","text":"An iframe which has both allow-scripts and allow-same-origin for its sandbox attribute can escape its sandboxing.","args":[],"location":{"url":"https://conversationapp.svc.talkdeskapp.com/conversation-app/runtime/3.37.0/runtime/index.html?monitoringConfig=eyJwcm92aWRlciI6ImR5bmF0cmFjZS1vdGxwIiwidXJsIjoiaHR0cHM6Ly90YWxrZGVzay1wcm9kLWV1LTEubGl2ZS5keW5hdHJhY2UuY29tL2FwaS92Mi9vdGxwIiwidG9rZW4iOiJkdDBjMDEuSUlIT1k1M0czQUhSWDJPUlJCNkhJREVaLjVLMjZVQUU1QjVHWVU1SVRMWFdPWUlGTzNMTDdYSlBFNEU3TkhOSjNSVEdaNkFXVlhNSlNIV0daWlJUMlFCQkYiLCJwcm9wZXJ0aWVzIjp7Im5hbWUiOiJjb252ZXJzYXRpb24tcHJkLXRkLWV1LTEtZXUtd2VzdC0xIiwiYmF0Y2hEZWxheSI6NTAwMCwibWV0cmljcyI6eyJpbnRlcnZhbCI6MTAwMDB9LCJpZ25vcmVVcmxzIjpbImV1XFwuanNcXC5sb2dzXFwuaW5zaWdodFxcLnJhcGlkN1xcLmNvbSIsInJcXC5sci1pblxcLmNvbSIsInJcXC5pbnRha2UtbHJcXC5jb20iLCJldmVudGd3XFwudHdpbGlvXFwuY29tIl0sInByb3BhZ2F0ZUhlYWRlclVybHMiOlsiYXBpXFwudGFsa2Rlc2thcHBcXC5ldSJdfSwiYXR0cmlidXRlcyI6eyJhdGxhcy51c2VyX2lkIjoiNjM3YjZkY2UzNGE5NjgzMTY3NzMwOTllIiwiYXRsYXMudGVuYW50X2lkIjoiNjM3YjY1NjczOGU2MDA3ZDljNGUwMDRiIiwiYXRsYXMudGVuYW50X25hbWUiOiJwby1hdXRvbWF0aW9uIiwiYXRsYXMudmVyc2lvbiI6IjAuMTc2LjEyIiwiYXRsYXMuc2Vzc2lvbl90aWQiOiJiODljMmViMi0xZmZhLTQ2ZDktODYwZC05YTg3YWNlZDQ3ZjMiLCJhdGxhcy5lbmdpbmVfdHlwZSI6IndlYiIsImF0bGFzLmVuZ2luZV92ZXJzaW9uIjoiMTMxLjAuNjc3OC4zMyIsImF0bGFzLmVuZ2luZV9leHRlbnNpb24iOiJ1bmtub3duIiwiYXRsYXMucmVnaW9uIjoidGQtZXUtMSIsImF0bGFzLnByb3ZpZGVyX3JlZ2lvbiI6ImV1LWNlbnRyYWwtMSIsImF0bGFzLmFjY291bnRfYWZmaW5pdHkiOiJldS13ZXN0LTEiLCJhdGxhcy51c2VyX2FnZW50IjoiTW96aWxsYS81LjAgKFdpbmRvd3MgTlQgMTAuMDsgV2luNjQ7IHg2NCkgQXBwbGVXZWJLaXQvNTM3LjM2IChLSFRNTCwgbGlrZSBHZWNrbykgQ2hyb21lLzEzMS4wLjY3NzguMzMgU2FmYXJpLzUzNy4zNiIsImF0bGFzLmVudiI6InByZCIsImF0bGFzLmFwcF9zbHVnIjoiY29udmVyc2F0aW9uIiwiYXRsYXMub3duZXIiOiJjb252ZXJzYXRpb24tY29yZSJ9fQ%3D%3D&nonce=f1f998c4-2f0a-467d-9d9c-9907d1260e14&targetOrigin=https%3A%2F%2Fpo-automation.mytalkdesk.eu&depth=1","lineNumber":0,"columnNumber":0},"time":67089.356,"pageId":"page@ab6c8088dd9487f369c16d7dd0446acd"}
{"type":"console","messageType":"warning","text":"An iframe which has both allow-scripts and allow-same-origin for its sandbox attribute can escape its sandboxing.","args":[],"location":{"url":"https://prd-cdn-talkdesk.talkdesk.com/ur-app/1.19.0/index.html?monitoringConfig=eyJwcm92aWRlciI6ImR5bmF0cmFjZS1vdGxwIiwidXJsIjoiaHR0cHM6Ly90YWxrZGVzay1wcm9kLWV1LTEubGl2ZS5keW5hdHJhY2UuY29tL2FwaS92Mi9vdGxwIiwidG9rZW4iOiJkdDBjMDEuSUlIT1k1M0czQUhSWDJPUlJCNkhJREVaLjVLMjZVQUU1QjVHWVU1SVRMWFdPWUlGTzNMTDdYSlBFNEU3TkhOSjNSVEdaNkFXVlhNSlNIV0daWlJUMlFCQkYiLCJwcm9wZXJ0aWVzIjp7Im5hbWUiOiJ1ci1hcHAtcHJkLXRkLWV1LTEtZXUtd2VzdC0xIiwiYmF0Y2hEZWxheSI6NTAwMCwibWV0cmljcyI6eyJpbnRlcnZhbCI6MTAwMDB9LCJpZ25vcmVVcmxzIjpbImV1XFwuanNcXC5sb2dzXFwuaW5zaWdodFxcLnJhcGlkN1xcLmNvbSIsInJcXC5sci1pblxcLmNvbSIsInJcXC5pbnRha2UtbHJcXC5jb20iLCJldmVudGd3XFwudHdpbGlvXFwuY29tIl0sInByb3BhZ2F0ZUhlYWRlclVybHMiOlsiYXBpXFwudGFsa2Rlc2thcHBcXC5ldSJdfSwiYXR0cmlidXRlcyI6eyJhdGxhcy51c2VyX2lkIjoiNjM3YjZkY2UzNGE5NjgzMTY3NzMwOTllIiwiYXRsYXMudGVuYW50X2lkIjoiNjM3YjY1NjczOGU2MDA3ZDljNGUwMDRiIiwiYXRsYXMudGVuYW50X25hbWUiOiJwby1hdXRvbWF0aW9uIiwiYXRsYXMudmVyc2lvbiI6IjAuMTc2LjEyIiwiYXRsYXMuc2Vzc2lvbl90aWQiOiJiODljMmViMi0xZmZhLTQ2ZDktODYwZC05YTg3YWNlZDQ3ZjMiLCJhdGxhcy5lbmdpbmVfdHlwZSI6IndlYiIsImF0bGFzLmVuZ2luZV92ZXJzaW9uIjoiMTMxLjAuNjc3OC4zMyIsImF0bGFzLmVuZ2luZV9leHRlbnNpb24iOiJ1bmtub3duIiwiYXRsYXMucmVnaW9uIjoidGQtZXUtMSIsImF0bGFzLnByb3ZpZGVyX3JlZ2lvbiI6ImV1LWNlbnRyYWwtMSIsImF0bGFzLmFjY291bnRfYWZmaW5pdHkiOiJldS13ZXN0LTEiLCJhdGxhcy51c2VyX2FnZW50IjoiTW96aWxsYS81LjAgKFdpbmRvd3MgTlQgMTAuMDsgV2luNjQ7IHg2NCkgQXBwbGVXZWJLaXQvNTM3LjM2IChLSFRNTCwgbGlrZSBHZWNrbykgQ2hyb21lLzEzMS4wLjY3NzguMzMgU2FmYXJpLzUzNy4zNiIsImF0bGFzLmVudiI6InByZCIsImF0bGFzLmFwcF9zbHVnIjoidXItYXBwIiwiYXRsYXMub3duZXIiOiJ3Zm8tZWNobyJ9fQ%3D%3D&nonce=9d51e258-effc-494a-ba06-b0452ac051f6&targetOrigin=https%3A%2F%2Fpo-automation.mytalkdesk.eu&depth=1","lineNumber":0,"columnNumber":0},"time":67095.683,"pageId":"page@ab6c8088dd9487f369c16d7dd0446acd"}
{"type":"console","messageType":"warning","text":"An iframe which has both allow-scripts and allow-same-origin for its sandbox attribute can escape its sandboxing.","args":[],"location":{"url":"https://prd-cdn-talkdesk.talkdesk.com/ai-agent-assist-frontoffice/3.27.5/secondary/index.html?monitoringConfig=eyJwcm92aWRlciI6ImR5bmF0cmFjZS1vdGxwIiwidXJsIjoiaHR0cHM6Ly90YWxrZGVzay1wcm9kLWV1LTEubGl2ZS5keW5hdHJhY2UuY29tL2FwaS92Mi9vdGxwIiwidG9rZW4iOiJkdDBjMDEuSUlIT1k1M0czQUhSWDJPUlJCNkhJREVaLjVLMjZVQUU1QjVHWVU1SVRMWFdPWUlGTzNMTDdYSlBFNEU3TkhOSjNSVEdaNkFXVlhNSlNIV0daWlJUMlFCQkYiLCJwcm9wZXJ0aWVzIjp7Im5hbWUiOiJhZ2VudC1hc3Npc3Qtc2Vjb25kYXJ5LXByZC10ZC1ldS0xLWV1LXdlc3QtMSIsImJhdGNoRGVsYXkiOjUwMDAsIm1ldHJpY3MiOnsiaW50ZXJ2YWwiOjEwMDAwfSwiaWdub3JlVXJscyI6WyJldVxcLmpzXFwubG9nc1xcLmluc2lnaHRcXC5yYXBpZDdcXC5jb20iLCJyXFwubHItaW5cXC5jb20iLCJyXFwuaW50YWtlLWxyXFwuY29tIiwiZXZlbnRnd1xcLnR3aWxpb1xcLmNvbSJdLCJwcm9wYWdhdGVIZWFkZXJVcmxzIjpbImFwaVxcLnRhbGtkZXNrYXBwXFwuZXUiXX0sImF0dHJpYnV0ZXMiOnsiYXRsYXMudXNlcl9pZCI6IjYzN2I2ZGNlMzRhOTY4MzE2NzczMDk5ZSIsImF0bGFzLnRlbmFudF9pZCI6IjYzN2I2NTY3MzhlNjAwN2Q5YzRlMDA0YiIsImF0bGFzLnRlbmFudF9uYW1lIjoicG8tYXV0b21hdGlvbiIsImF0bGFzLnZlcnNpb24iOiIwLjE3Ni4xMiIsImF0bGFzLnNlc3Npb25fdGlkIjoiYjg5YzJlYjItMWZmYS00NmQ5LTg2MGQtOWE4N2FjZWQ0N2YzIiwiYXRsYXMuZW5naW5lX3R5cGUiOiJ3ZWIiLCJhdGxhcy5lbmdpbmVfdmVyc2lvbiI6IjEzMS4wLjY3NzguMzMiLCJhdGxhcy5lbmdpbmVfZXh0ZW5zaW9uIjoidW5rbm93biIsImF0bGFzLnJlZ2lvbiI6InRkLWV1LTEiLCJhdGxhcy5wcm92aWRlcl9yZWdpb24iOiJldS1jZW50cmFsLTEiLCJhdGxhcy5hY2NvdW50X2FmZmluaXR5IjoiZXUtd2VzdC0xIiwiYXRsYXMudXNlcl9hZ2VudCI6Ik1vemlsbGEvNS4wIChXaW5kb3dzIE5UIDEwLjA7IFdpbjY0OyB4NjQpIEFwcGxlV2ViS2l0LzUzNy4zNiAoS0hUTUwsIGxpa2UgR2Vja28pIENocm9tZS8xMzEuMC42Nzc4LjMzIFNhZmFyaS81MzcuMzYiLCJhdGxhcy5lbnYiOiJwcmQiLCJhdGxhcy5hcHBfc2x1ZyI6ImFnZW50LWFzc2lzdC1zZWNvbmRhcnkiLCJhdGxhcy5vd25lciI6ImFpLWVuZ2luZWVyaW5nIn19&nonce=3ea0b1ad-7946-449c-88cb-b8e4f08b8196&targetOrigin=https%3A%2F%2Fpo-automation.mytalkdesk.eu&depth=1","lineNumber":0,"columnNumber":0},"time":67102.02,"pageId":"page@ab6c8088dd9487f369c16d7dd0446acd"}
{"type":"console","messageType":"warning","text":"[zustand devtools middleware]: `serialize.options` is deprecated, just use `serialize`","args":[{"preview":"[zustand devtools middleware]: `serialize.options` is deprecated, just use `serialize`","value":"[zustand devtools middleware]: `serialize.options` is deprecated, just use `serialize`"}],"location":{"url":"https://prd-cdn-talkdesk.talkdesk.com/ai-agent-assist-frontoffice/3.27.5/secondary/main.cf4f56d8399439160f11.js","lineNumber":2175,"columnNumber":109317},"time":68084.416,"pageId":"page@ab6c8088dd9487f369c16d7dd0446acd"}
{"type":"console","messageType":"warning","text":"[zustand devtools middleware] Please install/enable Redux devtools extension","args":[{"preview":"[zustand devtools middleware] Please install/enable Redux devtools extension","value":"[zustand devtools middleware] Please install/enable Redux devtools extension"}],"location":{"url":"https://prd-cdn-talkdesk.talkdesk.com/ai-agent-assist-frontoffice/3.27.5/secondary/main.cf4f56d8399439160f11.js","lineNumber":2175,"columnNumber":109551},"time":68084.479,"pageId":"page@ab6c8088dd9487f369c16d7dd0446acd"}
{"type":"console","messageType":"warning","text":"Warning: You are attempting to use a basename on a page whose URL path does not begin with the basename. Expected path \"/ai-agent-assist-frontoffice/3.27.5/secondary/index.html?monitoringConfig=eyJwcm92aWRlciI6ImR5bmF0cmFjZS1vdGxwIiwidXJsIjoiaHR0cHM6Ly90YWxrZGVzay1wcm9kLWV1LTEubGl2ZS5keW5hdHJhY2UuY29tL2FwaS92Mi9vdGxwIiwidG9rZW4iOiJkdDBjMDEuSUlIT1k1M0czQUhSWDJPUlJCNkhJREVaLjVLMjZVQUU1QjVHWVU1SVRMWFdPWUlGTzNMTDdYSlBFNEU3TkhOSjNSVEdaNkFXVlhNSlNIV0daWlJUMlFCQkYiLCJwcm9wZXJ0aWVzIjp7Im5hbWUiOiJhZ2VudC1hc3Npc3Qtc2Vjb25kYXJ5LXByZC10ZC1ldS0xLWV1LXdlc3QtMSIsImJhdGNoRGVsYXkiOjUwMDAsIm1ldHJpY3MiOnsiaW50ZXJ2YWwiOjEwMDAwfSwiaWdub3JlVXJscyI6WyJldVxcLmpzXFwubG9nc1xcLmluc2lnaHRcXC5yYXBpZDdcXC5jb20iLCJyXFwubHItaW5cXC5jb20iLCJyXFwuaW50YWtlLWxyXFwuY29tIiwiZXZlbnRnd1xcLnR3aWxpb1xcLmNvbSJdLCJwcm9wYWdhdGVIZWFkZXJVcmxzIjpbImFwaVxcLnRhbGtkZXNrYXBwXFwuZXUiXX0sImF0dHJpYnV0ZXMiOnsiYXRsYXMudXNlcl9pZCI6IjYzN2I2ZGNlMzRhOTY4MzE2NzczMDk5ZSIsImF0bGFzLnRlbmFudF9pZCI6IjYzN2I2NTY3MzhlNjAwN2Q5YzRlMDA0YiIsImF0bGFzLnRlbmFudF9uYW1lIjoicG8tYXV0b21hdGlvbiIsImF0bGFzLnZlcnNpb24iOiIwLjE3Ni4xMiIsImF0bGFzLnNlc3Npb25fdGlkIjoiYjg5YzJlYjItMWZmYS00NmQ5LTg2MGQtOWE4N2FjZWQ0N2YzIiwiYXRsYXMuZW5naW5lX3R5cGUiOiJ3ZWIiLCJhdGxhcy5lbmdpbmVfdmVyc2lvbiI6IjEzMS4wLjY3NzguMzMiLCJhdGxhcy5lbmdpbmVfZXh0ZW5zaW9uIjoidW5rbm93biIsImF0bGFzLnJlZ2lvbiI6InRkLWV1LTEiLCJhdGxhcy5wcm92aWRlcl9yZWdpb24iOiJldS1jZW50cmFsLTEiLCJhdGxhcy5hY2NvdW50X2FmZmluaXR5IjoiZXUtd2VzdC0xIiwiYXRsYXMudXNlcl9hZ2VudCI6Ik1vemlsbGEvNS4wIChXaW5kb3dzIE5UIDEwLjA7IFdpbjY0OyB4NjQpIEFwcGxlV2ViS2l0LzUzNy4zNiAoS0hUTUwsIGxpa2UgR2Vja28pIENocm9tZS8xMzEuMC42Nzc4LjMzIFNhZmFyaS81MzcuMzYiLCJhdGxhcy5lbnYiOiJwcmQiLCJhdGxhcy5hcHBfc2x1ZyI6ImFnZW50LWFzc2lzdC1zZWNvbmRhcnkiLCJhdGxhcy5vd25lciI6ImFpLWVuZ2luZWVyaW5nIn19&nonce=3ea0b1ad-7946-449c-88cb-b8e4f08b8196&targetOrigin=https%3A%2F%2Fpo-automation.mytalkdesk.eu&depth=1\" to begin with \"/ai-agent-assist-frontoffice/latest/secondary\".","args":[{"preview":"Warning: You are attempting to use a basename on a page whose URL path does not begin with the basename. Expected path \"/ai-agent-assist-frontoffice/3.27.5/secondary/index.html?monitoringConfig=eyJwcm92aWRlciI6ImR5bmF0cmFjZS1vdGxwIiwidXJsIjoiaHR0cHM6Ly90YWxrZGVzay1wcm9kLWV1LTEubGl2ZS5keW5hdHJhY2UuY29tL2FwaS92Mi9vdGxwIiwidG9rZW4iOiJkdDBjMDEuSUlIT1k1M0czQUhSWDJPUlJCNkhJREVaLjVLMjZVQUU1QjVHWVU1SVRMWFdPWUlGTzNMTDdYSlBFNEU3TkhOSjNSVEdaNkFXVlhNSlNIV0daWlJUMlFCQkYiLCJwcm9wZXJ0aWVzIjp7Im5hbWUiOiJhZ2VudC1hc3Npc3Qtc2Vjb25kYXJ5LXByZC10ZC1ldS0xLWV1LXdlc3QtMSIsImJhdGNoRGVsYXkiOjUwMDAsIm1ldHJpY3MiOnsiaW50ZXJ2YWwiOjEwMDAwfSwiaWdub3JlVXJscyI6WyJldVxcLmpzXFwubG9nc1xcLmluc2lnaHRcXC5yYXBpZDdcXC5jb20iLCJyXFwubHItaW5cXC5jb20iLCJyXFwuaW50YWtlLWxyXFwuY29tIiwiZXZlbnRnd1xcLnR3aWxpb1xcLmNvbSJdLCJwcm9wYWdhdGVIZWFkZXJVcmxzIjpbImFwaVxcLnRhbGtkZXNrYXBwXFwuZXUiXX0sImF0dHJpYnV0ZXMiOnsiYXRsYXMudXNlcl9pZCI6IjYzN2I2ZGNlMzRhOTY4MzE2NzczMDk5ZSIsImF0bGFzLnRlbmFudF9pZCI6IjYzN2I2NTY3MzhlNjAwN2Q5YzRlMDA0YiIsImF0bGFzLnRlbmFudF9uYW1lIjoicG8tYXV0b21hdGlvbiIsImF0bGFzLnZlcnNpb24iOiIwLjE3Ni4xMiIsImF0bGFzLnNlc3Npb25fdGlkIjoiYjg5YzJlYjItMWZmYS00NmQ5LTg2MGQtOWE4N2FjZWQ0N2YzIiwiYXRsYXMuZW5naW5lX3R5cGUiOiJ3ZWIiLCJhdGxhcy5lbmdpbmVfdmVyc2lvbiI6IjEzMS4wLjY3NzguMzMiLCJhdGxhcy5lbmdpbmVfZXh0ZW5zaW9uIjoidW5rbm93biIsImF0bGFzLnJlZ2lvbiI6InRkLWV1LTEiLCJhdGxhcy5wcm92aWRlcl9yZWdpb24iOiJldS1jZW50cmFsLTEiLCJhdGxhcy5hY2NvdW50X2FmZmluaXR5IjoiZXUtd2VzdC0xIiwiYXRsYXMudXNlcl9hZ2VudCI6Ik1vemlsbGEvNS4wIChXaW5kb3dzIE5UIDEwLjA7IFdpbjY0OyB4NjQpIEFwcGxlV2ViS2l0LzUzNy4zNiAoS0hUTUwsIGxpa2UgR2Vja28pIENocm9tZS8xMzEuMC42Nzc4LjMzIFNhZmFyaS81MzcuMzYiLCJhdGxhcy5lbnYiOiJwcmQiLCJhdGxhcy5hcHBfc2x1ZyI6ImFnZW50LWFzc2lzdC1zZWNvbmRhcnkiLCJhdGxhcy5vd25lciI6ImFpLWVuZ2luZWVyaW5nIn19&nonce=3ea0b1ad-7946-449c-88cb-b8e4f08b8196&targetOrigin=https%3A%2F%2Fpo-automation.mytalkdesk.eu&depth=1\" to begin with \"/ai-agent-assist-frontoffice/latest/secondary\".","value":"Warning: You are attempting to use a basename on a page whose URL path does not begin with the basename. Expected path \"/ai-agent-assist-frontoffice/3.27.5/secondary/index.html?monitoringConfig=eyJwcm92aWRlciI6ImR5bmF0cmFjZS1vdGxwIiwidXJsIjoiaHR0cHM6Ly90YWxrZGVzay1wcm9kLWV1LTEubGl2ZS5keW5hdHJhY2UuY29tL2FwaS92Mi9vdGxwIiwidG9rZW4iOiJkdDBjMDEuSUlIT1k1M0czQUhSWDJPUlJCNkhJREVaLjVLMjZVQUU1QjVHWVU1SVRMWFdPWUlGTzNMTDdYSlBFNEU3TkhOSjNSVEdaNkFXVlhNSlNIV0daWlJUMlFCQkYiLCJwcm9wZXJ0aWVzIjp7Im5hbWUiOiJhZ2VudC1hc3Npc3Qtc2Vjb25kYXJ5LXByZC10ZC1ldS0xLWV1LXdlc3QtMSIsImJhdGNoRGVsYXkiOjUwMDAsIm1ldHJpY3MiOnsiaW50ZXJ2YWwiOjEwMDAwfSwiaWdub3JlVXJscyI6WyJldVxcLmpzXFwubG9nc1xcLmluc2lnaHRcXC5yYXBpZDdcXC5jb20iLCJyXFwubHItaW5cXC5jb20iLCJyXFwuaW50YWtlLWxyXFwuY29tIiwiZXZlbnRnd1xcLnR3aWxpb1xcLmNvbSJdLCJwcm9wYWdhdGVIZWFkZXJVcmxzIjpbImFwaVxcLnRhbGtkZXNrYXBwXFwuZXUiXX0sImF0dHJpYnV0ZXMiOnsiYXRsYXMudXNlcl9pZCI6IjYzN2I2ZGNlMzRhOTY4MzE2NzczMDk5ZSIsImF0bGFzLnRlbmFudF9pZCI6IjYzN2I2NTY3MzhlNjAwN2Q5YzRlMDA0YiIsImF0bGFzLnRlbmFudF9uYW1lIjoicG8tYXV0b21hdGlvbiIsImF0bGFzLnZlcnNpb24iOiIwLjE3Ni4xMiIsImF0bGFzLnNlc3Npb25fdGlkIjoiYjg5YzJlYjItMWZmYS00NmQ5LTg2MGQtOWE4N2FjZWQ0N2YzIiwiYXRsYXMuZW5naW5lX3R5cGUiOiJ3ZWIiLCJhdGxhcy5lbmdpbmVfdmVyc2lvbiI6IjEzMS4wLjY3NzguMzMiLCJhdGxhcy5lbmdpbmVfZXh0ZW5zaW9uIjoidW5rbm93biIsImF0bGFzLnJlZ2lvbiI6InRkLWV1LTEiLCJhdGxhcy5wcm92aWRlcl9yZWdpb24iOiJldS1jZW50cmFsLTEiLCJhdGxhcy5hY2NvdW50X2FmZmluaXR5IjoiZXUtd2VzdC0xIiwiYXRsYXMudXNlcl9hZ2VudCI6Ik1vemlsbGEvNS4wIChXaW5kb3dzIE5UIDEwLjA7IFdpbjY0OyB4NjQpIEFwcGxlV2ViS2l0LzUzNy4zNiAoS0hUTUwsIGxpa2UgR2Vja28pIENocm9tZS8xMzEuMC42Nzc4LjMzIFNhZmFyaS81MzcuMzYiLCJhdGxhcy5lbnYiOiJwcmQiLCJhdGxhcy5hcHBfc2x1ZyI6ImFnZW50LWFzc2lzdC1zZWNvbmRhcnkiLCJhdGxhcy5vd25lciI6ImFpLWVuZ2luZWVyaW5nIn19&nonce=3ea0b1ad-7946-449c-88cb-b8e4f08b8196&targetOrigin=https%3A%2F%2Fpo-automation.mytalkdesk.eu&depth=1\" to begin with \"/ai-agent-assist-frontoffice/latest/secondary\"."}],"location":{"url":"https://prd-cdn-talkdesk.talkdesk.com/ai-agent-assist-frontoffice/3.27.5/secondary/main.cf4f56d8399439160f11.js","lineNumber":2399,"columnNumber":2098},"time":68151.878,"pageId":"page@ab6c8088dd9487f369c16d7dd0446acd"}
{"type":"after","callId":"call@512","endTime":68641.522,"afterSnapshot":"after@call@512"}
{"type":"before","callId":"call@514","startTime":69222.409,"apiName":"locator.isVisible","class":"Frame","method":"isVisible","params":{"selector":"div:not([style*=\"display: none\"])[data-testid=\"app\"] svg.loader-module__loader >> nth=0","strict":true},"stepId":"pw:api@12","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@514"}
{"type":"log","callId":"call@514","time":69298.997,"message":"  checking visibility of locator('div:not([style*=\"display: none\"])[data-testid=\"app\"] svg.loader-module__loader').first()"}
{"type":"after","callId":"call@514","endTime":69378.868,"result":{"value":true},"afterSnapshot":"after@call@514"}
{"type":"before","callId":"call@516","startTime":69435.899,"apiName":"page.waitForTimeout","class":"Frame","method":"waitForTimeout","params":{"timeout":3000},"stepId":"pw:api@13","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@516"}
{"type":"console","messageType":"error","text":"Failed to load resource: the server responded with a status of 404 ()","args":[],"location":{"url":"https://prd-cdn-talkdesk.talkdesk.com/i18n/live/agent-assist-secondary/en-US/latest/translation.json","lineNumber":0,"columnNumber":0},"time":69665.107,"pageId":"page@ab6c8088dd9487f369c16d7dd0446acd"}
{"type":"console","messageType":"error","text":"Failed to load resource: the server responded with a status of 404 ()","args":[],"location":{"url":"https://api.talkdeskapp.eu/emergency/settings","lineNumber":0,"columnNumber":0},"time":70188.849,"pageId":"page@ab6c8088dd9487f369c16d7dd0446acd"}
{"type":"console","messageType":"debug","text":"Returning value for CONVERSATION_APP_enable_digital_connect: false","args":[{"preview":"Returning value for CONVERSATION_APP_enable_digital_connect: false","value":"Returning value for CONVERSATION_APP_enable_digital_connect: false"}],"location":{"url":"https://conversationapp.svc.talkdeskapp.com/conversation-app/runtime/3.37.0/runtime/main.17805132c5224d076ed8.js","lineNumber":7,"columnNumber":251090},"time":70819.574,"pageId":"page@ab6c8088dd9487f369c16d7dd0446acd"}
{"type":"console","messageType":"debug","text":"Returning value for CONVERSATION_APP_enable_fax: false","args":[{"preview":"Returning value for CONVERSATION_APP_enable_fax: false","value":"Returning value for CONVERSATION_APP_enable_fax: false"}],"location":{"url":"https://conversationapp.svc.talkdeskapp.com/conversation-app/runtime/3.37.0/runtime/main.17805132c5224d076ed8.js","lineNumber":7,"columnNumber":251090},"time":70819.832,"pageId":"page@ab6c8088dd9487f369c16d7dd0446acd"}
{"type":"console","messageType":"debug","text":"Returning value for CONVERSATION_APP_enable_demo: false","args":[{"preview":"Returning value for CONVERSATION_APP_enable_demo: false","value":"Returning value for CONVERSATION_APP_enable_demo: false"}],"location":{"url":"https://conversationapp.svc.talkdeskapp.com/conversation-app/runtime/3.37.0/runtime/main.17805132c5224d076ed8.js","lineNumber":7,"columnNumber":251090},"time":70819.929,"pageId":"page@ab6c8088dd9487f369c16d7dd0446acd"}
{"type":"console","messageType":"debug","text":"Returning value for CONVERSATION_APP_enable_whatsapp: false","args":[{"preview":"Returning value for CONVERSATION_APP_enable_whatsapp: false","value":"Returning value for CONVERSATION_APP_enable_whatsapp: false"}],"location":{"url":"https://conversationapp.svc.talkdeskapp.com/conversation-app/runtime/3.37.0/runtime/main.17805132c5224d076ed8.js","lineNumber":7,"columnNumber":251090},"time":70820.012,"pageId":"page@ab6c8088dd9487f369c16d7dd0446acd"}
{"type":"console","messageType":"debug","text":"Returning value for DCE_Infobip_WhatsApp: false","args":[{"preview":"Returning value for DCE_Infobip_WhatsApp: false","value":"Returning value for DCE_Infobip_WhatsApp: false"}],"location":{"url":"https://conversationapp.svc.talkdeskapp.com/conversation-app/runtime/3.37.0/runtime/main.17805132c5224d076ed8.js","lineNumber":7,"columnNumber":251090},"time":70820.092,"pageId":"page@ab6c8088dd9487f369c16d7dd0446acd"}
{"type":"console","messageType":"debug","text":"Returning value for DCE_Infobip_AMB: false","args":[{"preview":"Returning value for DCE_Infobip_AMB: false","value":"Returning value for DCE_Infobip_AMB: false"}],"location":{"url":"https://conversationapp.svc.talkdeskapp.com/conversation-app/runtime/3.37.0/runtime/main.17805132c5224d076ed8.js","lineNumber":7,"columnNumber":251090},"time":70820.172,"pageId":"page@ab6c8088dd9487f369c16d7dd0446acd"}
{"type":"console","messageType":"warning","text":"Unsatisfied version 0.46.1 from @conversation-app/runtime of shared singleton module @atlas/sdk (required =0.46.2)","args":[{"preview":"Unsatisfied version 0.46.1 from @conversation-app/runtime of shared singleton module @atlas/sdk (required =0.46.2)","value":"Unsatisfied version 0.46.1 from @conversation-app/runtime of shared singleton module @atlas/sdk (required =0.46.2)"}],"location":{"url":"https://conversationapp.svc.talkdeskapp.com/conversation-app/voice-channel/1.48.0/voice-channel/remoteEntry.js","lineNumber":0,"columnNumber":6115},"time":70879.974,"pageId":"page@ab6c8088dd9487f369c16d7dd0446acd"}
{"type":"after","callId":"call@516","endTime":72451.091,"afterSnapshot":"after@call@516"}
{"type":"before","callId":"call@518","startTime":72475.585,"apiName":"page.waitForTimeout","class":"Frame","method":"waitForTimeout","params":{"timeout":3000},"stepId":"pw:api@14","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@518"}
{"type":"after","callId":"call@518","endTime":75484.304,"afterSnapshot":"after@call@518"}
{"type":"before","callId":"call@520","startTime":75510.193,"apiName":"locator.isVisible","class":"Frame","method":"isVisible","params":{"selector":"div:not([style*=\"display: none\"])[data-testid=\"app\"] svg.loader-module__loader >> nth=0","strict":true},"stepId":"pw:api@15","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@520"}
{"type":"log","callId":"call@520","time":75528.037,"message":"  checking visibility of locator('div:not([style*=\"display: none\"])[data-testid=\"app\"] svg.loader-module__loader').first()"}
{"type":"after","callId":"call@520","endTime":75529.668,"result":{"value":false},"afterSnapshot":"after@call@520"}
{"type":"before","callId":"call@522","startTime":75540.035,"apiName":"page.evaluate","class":"Frame","method":"evaluateExpression","params":{"expression":"() => {\n      console.log(\"========START EXECUTE JQUERY=============\");\n      // let toastRoot = document.querySelector('div#toast');\n      const toastRoot = document.querySelector('div[data-testid=\"toaster\"]');\n      let MutationObserver = window.MutationObserver || window.WebKitMutationObserver || window.MozMutationObserver;\n      let mutationObserver = new MutationObserver(mutations => {\n        const toast = document.querySelector('div[data-testid=\"toaster\"] div[data-co-name=\"Message\"] div[data-testid]');\n        const toast_content = document.querySelector('div[data-testid=\"toaster\"] div[data-co-name=\"Message\"] div[data-testid] p').textContent;\n        console.log(toast);\n        if (toast_content.includes('Conversations is open in another tab or device')) {\n          console.log(\"=====find the conversation conflict toast======\");\n          toast.remove();\n        } else {\n          console.log(\"=======No need to fix this toast=======\");\n        }\n      });\n      mutationObserver.observe(toastRoot, {\n        childList: true,\n        //\n        // attributes: true, //\n        // characterData: true, //\n        subtree: true //\n        // attributesFilter: ['class', 'style'], //\n        // attributesOldValue: true, //\n        // characterDataOldValue: true //\n      });\n      console.log(\"========START EXECUTE JQUERY=============\");\n    }","isFunction":true,"arg":{"value":{"v":"undefined"},"handles":[]}},"stepId":"pw:api@16","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@522"}
{"type":"after","callId":"call@522","endTime":75554.538,"result":{"value":{"v":"undefined"}},"afterSnapshot":"after@call@522"}
{"type":"before","callId":"call@524","startTime":75565.037,"apiName":"expect.toBeVisible","class":"Frame","method":"expect","params":{"selector":"header.dock-drawer-component-module__header h4","expression":"to.be.visible","expectedValue":{"value":{"v":"undefined"},"handles":[]},"isNot":false,"timeout":6000},"stepId":"expect@17","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@524"}
{"type":"log","callId":"call@524","time":75572.688,"message":"expect.toBeVisible with timeout 6000ms"}
{"type":"log","callId":"call@524","time":75572.698,"message":"waiting for locator('header.dock-drawer-component-module__header h4')"}
{"type":"log","callId":"call@524","time":75575.904,"message":"  locator resolved to <h4 data-co-name=\"Heading\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-heading react-typography_2-0-1_co-heading--4\">Dialer</h4>"}
{"type":"after","callId":"call@524","endTime":75575.994,"result":{"matches":true,"received":{"b":true}},"afterSnapshot":"after@call@524"}
{"type":"before","callId":"call@526","startTime":75585.719,"apiName":"locator.textContent","class":"Frame","method":"textContent","params":{"selector":"header.dock-drawer-component-module__header h4","strict":true},"stepId":"pw:api@18","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@526"}
{"type":"log","callId":"call@526","time":75593.934,"message":"waiting for locator('header.dock-drawer-component-module__header h4')"}
{"type":"log","callId":"call@526","time":75595.708,"message":"  locator resolved to <h4 data-co-name=\"Heading\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-heading react-typography_2-0-1_co-heading--4\">Dialer</h4>"}
{"type":"after","callId":"call@526","endTime":75595.752,"result":{"value":"Dialer"},"afterSnapshot":"after@call@526"}
{"type":"before","callId":"call@528","startTime":75606.161,"apiName":"locator.isVisible","class":"Frame","method":"isVisible","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> header h1","strict":true},"stepId":"pw:api@20","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@528"}
{"type":"log","callId":"call@528","time":75614.954,"message":"  checking visibility of locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('header h1')"}
{"type":"after","callId":"call@528","endTime":75629.88,"result":{"value":true},"afterSnapshot":"after@call@528"}
{"type":"before","callId":"call@530","startTime":75639.322,"apiName":"locator.isVisible","class":"Frame","method":"isVisible","params":{"selector":"button.secondary-area-module__active","strict":true},"stepId":"pw:api@21","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@530"}
{"type":"log","callId":"call@530","time":75646.68,"message":"  checking visibility of locator('button.secondary-area-module__active')"}
{"type":"after","callId":"call@530","endTime":75648.107,"result":{"value":true},"afterSnapshot":"after@call@530"}
{"type":"before","callId":"call@532","startTime":75658.401,"apiName":"locator.isVisible","class":"Frame","method":"isVisible","params":{"selector":"div._pendo-step-container-size","strict":true},"stepId":"pw:api@22","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@532"}
{"type":"log","callId":"call@532","time":75667.806,"message":"  checking visibility of locator('div._pendo-step-container-size')"}
{"type":"after","callId":"call@532","endTime":75669.19,"result":{"value":false},"afterSnapshot":"after@call@532"}
{"type":"before","callId":"call@534","startTime":75677.972,"apiName":"locator.click","class":"Frame","method":"click","params":{"selector":"button.secondary-area-module__active","strict":true},"stepId":"pw:api@23","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@534"}
{"type":"log","callId":"call@534","time":75685.505,"message":"waiting for locator('button.secondary-area-module__active')"}
{"type":"log","callId":"call@534","time":75687.979,"message":"  locator resolved to <button data-co-name=\"Button\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" aria-label=\"Toggle secondary area\" data-testid=\"secondary-area-btn-toggle\" class=\"react-button_2-0-1_co-button react-button_2-0-1_co-button--secondary react-button_2-0-1_co-button--medium react-button_2-0-1_co-button--transparent secondary-area-module__active secondary-area-module__button\">…</button>"}
{"type":"log","callId":"call@534","time":75688.782,"message":"attempting click action"}
{"type":"log","callId":"call@534","time":75688.812,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@534","time":75692.269,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@534","time":75692.281,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@534","time":75692.618,"message":"  done scrolling"}
{"type":"log","callId":"call@534","time":75702.696,"message":"  performing click action"}
{"type":"log","callId":"call@534","time":75761.661,"message":"  click action done"}
{"type":"log","callId":"call@534","time":75761.681,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@534","time":75761.883,"message":"  navigations have finished"}
{"type":"after","callId":"call@534","endTime":75761.991,"point":{"x":1260,"y":20},"afterSnapshot":"after@call@534"}
{"type":"before","callId":"call@536","startTime":75878.713,"apiName":"locator.isVisible","class":"Frame","method":"isVisible","params":{"selector":"div[data-testid=\"toaster\"] div[data-co-name=\"Message\"]:nth-child(1) div[data-testid]","strict":true},"stepId":"pw:api@24","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@536"}
{"type":"log","callId":"call@536","time":75889.608,"message":"  checking visibility of locator('div[data-testid=\"toaster\"] div[data-co-name=\"Message\"]:nth-child(1) div[data-testid]')"}
{"type":"after","callId":"call@536","endTime":75892.775,"result":{"value":true},"afterSnapshot":"after@call@536"}
{"type":"before","callId":"call@538","startTime":75906.474,"apiName":"locator.isVisible","class":"Frame","method":"isVisible","params":{"selector":"div[data-testid=\"toaster\"] div[data-co-name=\"Message\"]:nth-child(1) div[data-testid] >> button>i:text(\"close\")","strict":true},"stepId":"pw:api@25","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@538"}
{"type":"log","callId":"call@538","time":75913.97,"message":"  checking visibility of locator('div[data-testid=\"toaster\"] div[data-co-name=\"Message\"]:nth-child(1) div[data-testid]').locator('button>i:text(\"close\")')"}
{"type":"after","callId":"call@538","endTime":75916.465,"result":{"value":true},"afterSnapshot":"after@call@538"}
{"type":"before","callId":"call@540","startTime":75938.301,"apiName":"locator.click","class":"Frame","method":"click","params":{"selector":"div[data-testid=\"toaster\"] div[data-co-name=\"Message\"]:nth-child(1) div[data-testid] >> button>i:text(\"close\")","strict":true},"stepId":"pw:api@26","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@540"}
{"type":"log","callId":"call@540","time":75948.404,"message":"waiting for locator('div[data-testid=\"toaster\"] div[data-co-name=\"Message\"]:nth-child(1) div[data-testid]').locator('button>i:text(\"close\")')"}
{"type":"log","callId":"call@540","time":75953.583,"message":"  locator resolved to <i aria-hidden=\"true\" class=\"react-icon_2-1-0_co-icon react-icon_2-1-0_co-icon--small\">close</i>"}
{"type":"log","callId":"call@540","time":75954.541,"message":"attempting click action"}
{"type":"log","callId":"call@540","time":75954.61,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@540","time":75975.988,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@540","time":75976.003,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@540","time":75977.119,"message":"  done scrolling"}
{"type":"log","callId":"call@540","time":75997.933,"message":"  performing click action"}
{"type":"log","callId":"call@540","time":76029.509,"message":"  click action done"}
{"type":"log","callId":"call@540","time":76029.527,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@540","time":76029.849,"message":"  navigations have finished"}
{"type":"after","callId":"call@540","endTime":76029.939,"point":{"x":1220,"y":623.5},"afterSnapshot":"after@call@540"}
{"type":"before","callId":"call@542","startTime":76141.077,"apiName":"page.waitForTimeout","class":"Frame","method":"waitForTimeout","params":{"timeout":1000},"stepId":"pw:api@27","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@542"}
{"type":"event","time":76512.34,"class":"BrowserContext","method":"pageError","params":{"error":{"error":{"message":"Cannot read properties of null (reading 'textContent')","stack":"TypeError: Cannot read properties of null (reading 'textContent')\n    at MutationObserver.eval (eval at evaluate (:234:30), <anonymous>:8:130)","name":"TypeError"}}},"pageId":"page@ab6c8088dd9487f369c16d7dd0446acd"}
{"type":"after","callId":"call@542","endTime":77152.306,"afterSnapshot":"after@call@542"}
{"type":"before","callId":"call@544","startTime":77170.746,"apiName":"locator.isVisible","class":"Frame","method":"isVisible","params":{"selector":"div[data-testid=\"toaster\"] div[data-co-name=\"Message\"]:nth-child(1) div[data-testid]","strict":true},"stepId":"pw:api@28","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@544"}
{"type":"log","callId":"call@544","time":77179.403,"message":"  checking visibility of locator('div[data-testid=\"toaster\"] div[data-co-name=\"Message\"]:nth-child(1) div[data-testid]')"}
{"type":"after","callId":"call@544","endTime":77180.881,"result":{"value":false},"afterSnapshot":"after@call@544"}
{"type":"before","callId":"call@546","startTime":77191.125,"apiName":"locator.click","class":"Frame","method":"click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div button[data-pendo-campaignlist-header-createbutton]","strict":true},"stepId":"pw:api@30","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@546"}
{"type":"log","callId":"call@546","time":77198.684,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div button[data-pendo-campaignlist-header-createbutton]')"}
{"type":"log","callId":"call@546","time":77237.804,"message":"  locator resolved to <button data-co-name=\"Button\" data-co-version=\"1.4.0\" data-co-project=\"cobalt\" data-pendo-campaignlist-header-createbutton=\"true\" class=\"react-button_1-4-0_co-button react-button_1-4-0_co-button--primary react-button_1-4-0_co-button--medium\">Create campaign</button>"}
{"type":"log","callId":"call@546","time":77240.765,"message":"attempting click action"}
{"type":"log","callId":"call@546","time":77241.436,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@546","time":77276.06,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@546","time":77276.08,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@546","time":77276.615,"message":"  done scrolling"}
{"type":"log","callId":"call@546","time":77294.413,"message":"  performing click action"}
{"type":"log","callId":"call@546","time":77400.535,"message":"  click action done"}
{"type":"log","callId":"call@546","time":77400.549,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@546","time":77401.49,"message":"  navigations have finished"}
{"type":"after","callId":"call@546","endTime":77401.635,"point":{"x":1184.74,"y":84},"afterSnapshot":"after@call@546"}
{"type":"before","callId":"call@548","startTime":77557.646,"apiName":"page.waitForResponse","class":"Page","method":"waitForEventInfo","params":{"info":{"waitId":"f793dd5fe1c8880fcea0f2b18bdc1060","phase":"before","event":"response"}},"stepId":"pw:api@31","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@548"}
{"type":"before","callId":"call@552","startTime":77560.992,"apiName":"locator.fill","class":"Frame","method":"fill","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-modal__dialog >> #name-form-field-id","strict":true,"value":"metoto_test_jfflsuxs"},"stepId":"pw:api@32","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@552"}
{"type":"log","callId":"call@552","time":77636.337,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div.co-modal__dialog').locator('#name-form-field-id')"}
{"type":"log","callId":"call@552","time":77667.811,"message":"  locator resolved to <input value=\"\" type=\"text\" required=\"\" maxlength=\"35\" autocomplete=\"off\" id=\"name-form-field-id\" data-testid=\"modal-name-input\" placeholder=\"e.g. Black Friday\" data-pendo-campaignlist-createcampaign-campaignnameinput=\"true\"/>"}
{"type":"log","callId":"call@552","time":77669.202,"message":"  fill(\"metoto_test_jfflsuxs\")"}
{"type":"log","callId":"call@552","time":77669.217,"message":"attempting fill action"}
{"type":"log","callId":"call@552","time":77681.826,"message":"  waiting for element to be visible, enabled and editable"}
{"type":"after","callId":"call@552","endTime":77702.646,"afterSnapshot":"after@call@552"}
{"type":"console","messageType":"error","text":"Failed to load resource: the server responded with a status of 404 ()","args":[],"location":{"url":"https://api.talkdeskapp.eu/campaigns/metoto_test_jfflsuxs","lineNumber":0,"columnNumber":0},"time":78155.737,"pageId":"page@ab6c8088dd9487f369c16d7dd0446acd"}
{"type":"after","callId":"call@548","endTime":78156.956,"afterSnapshot":"after@call@548"}
{"type":"before","callId":"call@560","startTime":78160.525,"apiName":"locator.click","class":"Frame","method":"click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-modal__dialog >> button[data-testid=\"confirmButton\"]","strict":true},"stepId":"pw:api@33","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@560"}
{"type":"log","callId":"call@560","time":78184.91,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div.co-modal__dialog').locator('button[data-testid=\"confirmButton\"]')"}
{"type":"log","callId":"call@560","time":78189.519,"message":"  locator resolved to <button type=\"button\" data-testid=\"confirmButton\" class=\"co-button co--primary\">…</button>"}
{"type":"log","callId":"call@560","time":78190.033,"message":"attempting click action"}
{"type":"log","callId":"call@560","time":78190.342,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@560","time":78209.152,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@560","time":78209.175,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@560","time":78209.62,"message":"  done scrolling"}
{"type":"log","callId":"call@560","time":78251.191,"message":"  performing click action"}
{"type":"log","callId":"call@560","time":78274.208,"message":"  click action done"}
{"type":"log","callId":"call@560","time":78274.227,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@560","time":78329.213,"message":"  navigations have finished"}
{"type":"after","callId":"call@560","endTime":78329.394,"point":{"x":920.92,"y":522.89},"afterSnapshot":"after@call@560"}
{"type":"before","callId":"call@562","startTime":78452.656,"apiName":"expect.toBeEnabled","class":"Frame","method":"expect","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> [data-testid=\"discardButton\"]","expression":"to.be.enabled","expectedValue":{"value":{"v":"undefined"},"handles":[]},"isNot":false,"timeout":6000},"stepId":"expect@34","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@562"}
{"type":"log","callId":"call@562","time":78631.812,"message":"expect.toBeEnabled with timeout 6000ms"}
{"type":"log","callId":"call@562","time":78631.821,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('[data-testid=\"discardButton\"]')"}
{"type":"log","callId":"call@562","time":78667.454,"message":"  locator resolved to <button type=\"button\" class=\"co-link co--danger\" data-testid=\"discardButton\" data-pendo-campaignform-header-discardbutton=\"true\">…</button>"}
{"type":"after","callId":"call@562","endTime":78667.563,"result":{"matches":true,"received":{"b":true}},"afterSnapshot":"after@call@562"}
{"type":"before","callId":"call@564","startTime":78746.467,"apiName":"expect.toBeVisible","class":"Frame","method":"expect","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> [data-testid=\"previousButton\"]","expression":"to.be.visible","expectedValue":{"value":{"v":"undefined"},"handles":[]},"isNot":false,"timeout":6000},"stepId":"expect@35","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@564"}
{"type":"log","callId":"call@564","time":78756.958,"message":"expect.toBeVisible with timeout 6000ms"}
{"type":"log","callId":"call@564","time":78756.971,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('[data-testid=\"previousButton\"]')"}
{"type":"log","callId":"call@564","time":78768.798,"message":"  locator resolved to <button disabled type=\"button\" data-testid=\"previousButton\" class=\"co-button co--disabled co--secondary\" data-pendo-campaign-header-previousbutton=\"true\">…</button>"}
{"type":"after","callId":"call@564","endTime":78768.898,"result":{"matches":true,"received":{"b":true}},"afterSnapshot":"after@call@564"}
{"type":"before","callId":"call@566","startTime":78782.77,"apiName":"expect.not.toBeEnabled","class":"Frame","method":"expect","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> [data-testid=\"previousButton\"]","expression":"to.be.enabled","expectedValue":{"value":{"v":"undefined"},"handles":[]},"isNot":true,"timeout":6000},"stepId":"expect@36","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@566"}
{"type":"log","callId":"call@566","time":78789.667,"message":"expect.not.toBeEnabled with timeout 6000ms"}
{"type":"log","callId":"call@566","time":78789.678,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('[data-testid=\"previousButton\"]')"}
{"type":"log","callId":"call@566","time":78793.471,"message":"  locator resolved to <button disabled type=\"button\" data-testid=\"previousButton\" class=\"co-button co--disabled co--secondary\" data-pendo-campaign-header-previousbutton=\"true\">…</button>"}
{"type":"after","callId":"call@566","endTime":78793.564,"result":{"matches":false,"received":{"b":false}},"afterSnapshot":"after@call@566"}
{"type":"before","callId":"call@568","startTime":78832.712,"apiName":"expect.toBeVisible","class":"Frame","method":"expect","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> [data-testid=\"nextButton\"]","expression":"to.be.visible","expectedValue":{"value":{"v":"undefined"},"handles":[]},"isNot":false,"timeout":6000},"stepId":"expect@37","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@568"}
{"type":"log","callId":"call@568","time":78840.764,"message":"expect.toBeVisible with timeout 6000ms"}
{"type":"log","callId":"call@568","time":78840.776,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('[data-testid=\"nextButton\"]')"}
{"type":"log","callId":"call@568","time":78846.366,"message":"  locator resolved to <button disabled type=\"button\" data-testid=\"nextButton\" data-campaign-header-nextbutton=\"true\" class=\"co-button co--disabled co--primary\">…</button>"}
{"type":"after","callId":"call@568","endTime":78846.473,"result":{"matches":true,"received":{"b":true}},"afterSnapshot":"after@call@568"}
{"type":"before","callId":"call@570","startTime":78856.922,"apiName":"expect.not.toBeEnabled","class":"Frame","method":"expect","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> [data-testid=\"nextButton\"]","expression":"to.be.enabled","expectedValue":{"value":{"v":"undefined"},"handles":[]},"isNot":true,"timeout":6000},"stepId":"expect@38","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@570"}
{"type":"log","callId":"call@570","time":78867.549,"message":"expect.not.toBeEnabled with timeout 6000ms"}
{"type":"log","callId":"call@570","time":78867.558,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('[data-testid=\"nextButton\"]')"}
{"type":"log","callId":"call@570","time":78871.649,"message":"  locator resolved to <button disabled type=\"button\" data-testid=\"nextButton\" data-campaign-header-nextbutton=\"true\" class=\"co-button co--disabled co--primary\">…</button>"}
{"type":"after","callId":"call@570","endTime":78871.733,"result":{"matches":false,"received":{"b":false}},"afterSnapshot":"after@call@570"}
{"type":"before","callId":"call@572","startTime":78882.967,"apiName":"page.screenshot","class":"Page","method":"screenshot","params":{},"stepId":"pw:api@39","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@572"}
{"type":"log","callId":"call@572","time":78892.617,"message":"taking page screenshot"}
{"type":"log","callId":"call@572","time":78899.615,"message":"waiting for fonts to load..."}
{"type":"log","callId":"call@572","time":78901.316,"message":"fonts loaded"}
{"type":"after","callId":"call@572","endTime":78997.298,"result":{"binary":"<Buffer>"},"afterSnapshot":"after@call@572"}
{"type":"before","callId":"call@574","startTime":79027.279,"apiName":"locator.isVisible","class":"Frame","method":"isVisible","params":{"selector":"div[data-testid=\"toaster\"] div[data-co-name=\"Message\"]:nth-child(1) div[data-testid]","strict":true},"stepId":"pw:api@43","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@574"}
{"type":"log","callId":"call@574","time":79039.301,"message":"  checking visibility of locator('div[data-testid=\"toaster\"] div[data-co-name=\"Message\"]:nth-child(1) div[data-testid]')"}
{"type":"after","callId":"call@574","endTime":79041.61,"result":{"value":false},"afterSnapshot":"after@call@574"}
{"type":"before","callId":"call@576","startTime":79052.706,"apiName":"locator.textContent","class":"Frame","method":"textContent","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-grid__column.co-grid__column--min>h2.co-heading","strict":true},"stepId":"pw:api@44","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@576"}
{"type":"log","callId":"call@576","time":79059.203,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div.co-grid__column.co-grid__column--min>h2.co-heading')"}
{"type":"log","callId":"call@576","time":79062.163,"message":"  locator resolved to <h2 class=\"co-heading\">Mode</h2>"}
{"type":"after","callId":"call@576","endTime":79062.21,"result":{"value":"Mode"},"afterSnapshot":"after@call@576"}
{"type":"before","callId":"call@578","startTime":79070.791,"apiName":"locator.click","class":"Frame","method":"click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-section__content:has(label[for=\"DIALING_MODE_FIELD_ID\"]) >> div#dialDropdown","strict":true},"stepId":"pw:api@46","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@578"}
{"type":"log","callId":"call@578","time":79077.466,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div.co-section__content:has(label[for=\"DIALING_MODE_FIELD_ID\"])').locator('div#dialDropdown')"}
{"type":"log","callId":"call@578","time":79081.985,"message":"  locator resolved to <div tabindex=\"0\" id=\"dialDropdown\" class=\"co-dropdown\" data-testid=\"dialDropdown\">…</div>"}
{"type":"log","callId":"call@578","time":79082.751,"message":"attempting click action"}
{"type":"log","callId":"call@578","time":79083.08,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@578","time":79092.116,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@578","time":79092.129,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@578","time":79092.594,"message":"  done scrolling"}
{"type":"log","callId":"call@578","time":79105.848,"message":"  performing click action"}
{"type":"log","callId":"call@578","time":79140.198,"message":"  click action done"}
{"type":"log","callId":"call@578","time":79140.215,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@578","time":79140.624,"message":"  navigations have finished"}
{"type":"after","callId":"call@578","endTime":79140.763,"point":{"x":410.75,"y":490},"afterSnapshot":"after@call@578"}
{"type":"before","callId":"call@580","startTime":79255.622,"apiName":"locator.click","class":"Frame","method":"click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-section__content:has(label[for=\"DIALING_MODE_FIELD_ID\"]) >> div#dialDropdown >> ul >> span.co-list__item-content:not(.co-list__item-content--minimal):text(\"Predictive dialing\")","strict":true},"stepId":"pw:api@47","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@580"}
{"type":"log","callId":"call@580","time":79264.763,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div.co-section__content:has(label[for=\"DIALING_MODE_FIELD_ID\"])').locator('div#dialDropdown').locator('ul').locator('span.co-list__item-content:not(.co-list__item-content--minimal):text(\"Predictive dialing\")')"}
{"type":"log","callId":"call@580","time":79270.076,"message":"  locator resolved to <span class=\"co-list__item-content\">Predictive dialing</span>"}
{"type":"log","callId":"call@580","time":79270.943,"message":"attempting click action"}
{"type":"log","callId":"call@580","time":79271.568,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@580","time":79292.274,"message":"  element is not stable"}
{"type":"log","callId":"call@580","time":79292.29,"message":"retrying click action"}
{"type":"log","callId":"call@580","time":79293.327,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@580","time":79331.465,"message":"  element is not stable"}
{"type":"log","callId":"call@580","time":79331.488,"message":"retrying click action"}
{"type":"log","callId":"call@580","time":79331.499,"message":"  waiting 20ms"}
{"type":"log","callId":"call@580","time":79354.093,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@580","time":79375.647,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@580","time":79375.665,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@580","time":79376.554,"message":"  done scrolling"}
{"type":"log","callId":"call@580","time":79394.06,"message":"  performing click action"}
{"type":"log","callId":"call@580","time":79434.254,"message":"  click action done"}
{"type":"log","callId":"call@580","time":79434.27,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@580","time":79436.606,"message":"  navigations have finished"}
{"type":"after","callId":"call@580","endTime":79436.734,"point":{"x":424.75,"y":379.79},"afterSnapshot":"after@call@580"}
{"type":"before","callId":"call@582","startTime":79549.393,"apiName":"expect.toBeVisible","class":"Frame","method":"expect","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div[id=\"priority-id\"]","expression":"to.be.visible","expectedValue":{"value":{"v":"undefined"},"handles":[]},"isNot":false,"timeout":6000},"stepId":"expect@48","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@582"}
{"type":"log","callId":"call@582","time":79566.303,"message":"expect.toBeVisible with timeout 6000ms"}
{"type":"log","callId":"call@582","time":79566.311,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div[id=\"priority-id\"]')"}
{"type":"log","callId":"call@582","time":79572.618,"message":"  locator resolved to <div tabindex=\"-1\" id=\"priority-id\" class=\"co-rating\">…</div>"}
{"type":"after","callId":"call@582","endTime":79572.735,"result":{"matches":true,"received":{"b":true}},"afterSnapshot":"after@call@582"}
{"type":"before","callId":"call@584","startTime":79629.666,"apiName":"locator.click","class":"Frame","method":"click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> label[for=\"priority-id-9\"]","strict":true},"stepId":"pw:api@49","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@584"}
{"type":"log","callId":"call@584","time":79642.075,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('label[for=\"priority-id-9\"]')"}
{"type":"log","callId":"call@584","time":79647.503,"message":"  locator resolved to <label for=\"priority-id-9\" class=\"co-rating__label\">10</label>"}
{"type":"log","callId":"call@584","time":79648.261,"message":"attempting click action"}
{"type":"log","callId":"call@584","time":79648.601,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@584","time":79679.689,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@584","time":79679.704,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@584","time":79680.232,"message":"  done scrolling"}
{"type":"log","callId":"call@584","time":79737.617,"message":"  performing click action"}
{"type":"log","callId":"call@584","time":79765.144,"message":"  click action done"}
{"type":"log","callId":"call@584","time":79765.158,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@584","time":79765.487,"message":"  navigations have finished"}
{"type":"after","callId":"call@584","endTime":79765.612,"point":{"x":742,"y":183},"afterSnapshot":"after@call@584"}
{"type":"before","callId":"call@586","startTime":79877.247,"apiName":"locator.getAttribute","class":"Frame","method":"getAttribute","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> label[for=\"priority-id-0\"]","strict":true,"name":"class"},"stepId":"pw:api@50","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@586"}
{"type":"log","callId":"call@586","time":79885.661,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('label[for=\"priority-id-0\"]')"}
{"type":"log","callId":"call@586","time":79890.003,"message":"  locator resolved to <label for=\"priority-id-0\" class=\"co-rating__label co--bg-green-500\">1</label>"}
{"type":"after","callId":"call@586","endTime":79890.057,"result":{"value":"co-rating__label co--bg-green-500"},"afterSnapshot":"after@call@586"}
{"type":"before","callId":"call@588","startTime":79904.269,"apiName":"locator.getAttribute","class":"Frame","method":"getAttribute","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> label[for=\"priority-id-1\"]","strict":true,"name":"class"},"stepId":"pw:api@52","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@588"}
{"type":"log","callId":"call@588","time":79928.005,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('label[for=\"priority-id-1\"]')"}
{"type":"log","callId":"call@588","time":79931.42,"message":"  locator resolved to <label for=\"priority-id-1\" class=\"co-rating__label co--bg-green-500\">2</label>"}
{"type":"after","callId":"call@588","endTime":79931.467,"result":{"value":"co-rating__label co--bg-green-500"},"afterSnapshot":"after@call@588"}
{"type":"before","callId":"call@590","startTime":79940.839,"apiName":"locator.getAttribute","class":"Frame","method":"getAttribute","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> label[for=\"priority-id-2\"]","strict":true,"name":"class"},"stepId":"pw:api@54","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@590"}
{"type":"log","callId":"call@590","time":79947.917,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('label[for=\"priority-id-2\"]')"}
{"type":"log","callId":"call@590","time":79950.974,"message":"  locator resolved to <label for=\"priority-id-2\" class=\"co-rating__label co--bg-green-500\">3</label>"}
{"type":"after","callId":"call@590","endTime":79951.018,"result":{"value":"co-rating__label co--bg-green-500"},"afterSnapshot":"after@call@590"}
{"type":"before","callId":"call@592","startTime":79961.141,"apiName":"locator.getAttribute","class":"Frame","method":"getAttribute","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> label[for=\"priority-id-3\"]","strict":true,"name":"class"},"stepId":"pw:api@56","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@592"}
{"type":"log","callId":"call@592","time":79968.337,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('label[for=\"priority-id-3\"]')"}
{"type":"log","callId":"call@592","time":79971.262,"message":"  locator resolved to <label for=\"priority-id-3\" class=\"co-rating__label co--bg-green-500\">4</label>"}
{"type":"after","callId":"call@592","endTime":79971.304,"result":{"value":"co-rating__label co--bg-green-500"},"afterSnapshot":"after@call@592"}
{"type":"before","callId":"call@594","startTime":79980.051,"apiName":"locator.getAttribute","class":"Frame","method":"getAttribute","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> label[for=\"priority-id-4\"]","strict":true,"name":"class"},"stepId":"pw:api@58","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@594"}
{"type":"log","callId":"call@594","time":79988.69,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('label[for=\"priority-id-4\"]')"}
{"type":"log","callId":"call@594","time":79993.016,"message":"  locator resolved to <label for=\"priority-id-4\" class=\"co-rating__label co--bg-green-500\">5</label>"}
{"type":"after","callId":"call@594","endTime":79993.061,"result":{"value":"co-rating__label co--bg-green-500"},"afterSnapshot":"after@call@594"}
{"type":"before","callId":"call@596","startTime":80003.787,"apiName":"locator.getAttribute","class":"Frame","method":"getAttribute","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> label[for=\"priority-id-5\"]","strict":true,"name":"class"},"stepId":"pw:api@60","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@596"}
{"type":"log","callId":"call@596","time":80027.646,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('label[for=\"priority-id-5\"]')"}
{"type":"log","callId":"call@596","time":80031.294,"message":"  locator resolved to <label for=\"priority-id-5\" class=\"co-rating__label co--bg-green-500\">6</label>"}
{"type":"after","callId":"call@596","endTime":80031.357,"result":{"value":"co-rating__label co--bg-green-500"},"afterSnapshot":"after@call@596"}
{"type":"before","callId":"call@598","startTime":80041.474,"apiName":"locator.getAttribute","class":"Frame","method":"getAttribute","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> label[for=\"priority-id-6\"]","strict":true,"name":"class"},"stepId":"pw:api@62","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@598"}
{"type":"log","callId":"call@598","time":80048.167,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('label[for=\"priority-id-6\"]')"}
{"type":"log","callId":"call@598","time":80050.854,"message":"  locator resolved to <label for=\"priority-id-6\" class=\"co-rating__label co--bg-green-500\">7</label>"}
{"type":"after","callId":"call@598","endTime":80050.905,"result":{"value":"co-rating__label co--bg-green-500"},"afterSnapshot":"after@call@598"}
{"type":"before","callId":"call@600","startTime":80060.442,"apiName":"locator.getAttribute","class":"Frame","method":"getAttribute","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> label[for=\"priority-id-7\"]","strict":true,"name":"class"},"stepId":"pw:api@64","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@600"}
{"type":"log","callId":"call@600","time":80067.378,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('label[for=\"priority-id-7\"]')"}
{"type":"log","callId":"call@600","time":80069.914,"message":"  locator resolved to <label for=\"priority-id-7\" class=\"co-rating__label co--bg-green-500\">8</label>"}
{"type":"after","callId":"call@600","endTime":80069.963,"result":{"value":"co-rating__label co--bg-green-500"},"afterSnapshot":"after@call@600"}
{"type":"before","callId":"call@602","startTime":80081.21,"apiName":"locator.getAttribute","class":"Frame","method":"getAttribute","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> label[for=\"priority-id-8\"]","strict":true,"name":"class"},"stepId":"pw:api@66","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@602"}
{"type":"log","callId":"call@602","time":80087.863,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('label[for=\"priority-id-8\"]')"}
{"type":"log","callId":"call@602","time":80093.13,"message":"  locator resolved to <label for=\"priority-id-8\" class=\"co-rating__label co--bg-green-500\">9</label>"}
{"type":"after","callId":"call@602","endTime":80093.183,"result":{"value":"co-rating__label co--bg-green-500"},"afterSnapshot":"after@call@602"}
{"type":"before","callId":"call@604","startTime":80127.595,"apiName":"page.screenshot","class":"Page","method":"screenshot","params":{},"stepId":"pw:api@68","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@604"}
{"type":"log","callId":"call@604","time":80134.274,"message":"taking page screenshot"}
{"type":"log","callId":"call@604","time":80138.418,"message":"waiting for fonts to load..."}
{"type":"log","callId":"call@604","time":80138.865,"message":"fonts loaded"}
{"type":"after","callId":"call@604","endTime":80217.593,"result":{"binary":"<Buffer>"},"afterSnapshot":"after@call@604"}
{"type":"before","callId":"call@606","startTime":80234.325,"apiName":"locator.fill","class":"Frame","method":"fill","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> #max-dialing-ratio-id-input","strict":true,"value":"2"},"stepId":"pw:api@72","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@606"}
{"type":"log","callId":"call@606","time":80240.921,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('#max-dialing-ratio-id-input')"}
{"type":"log","callId":"call@606","time":80244.485,"message":"  locator resolved to <input min=\"1\" max=\"10\" value=\"\" type=\"number\" autocomplete=\"off\" id=\"max-dialing-ratio-id-input\"/>"}
{"type":"log","callId":"call@606","time":80245.159,"message":"  fill(\"2\")"}
{"type":"log","callId":"call@606","time":80245.167,"message":"attempting fill action"}
{"type":"log","callId":"call@606","time":80251.545,"message":"  waiting for element to be visible, enabled and editable"}
{"type":"after","callId":"call@606","endTime":80266.744,"afterSnapshot":"after@call@606"}
{"type":"before","callId":"call@608","startTime":80375.998,"apiName":"page.waitForTimeout","class":"Frame","method":"waitForTimeout","params":{"timeout":1000},"stepId":"pw:api@73","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@608"}
{"type":"after","callId":"call@608","endTime":81382.402,"afterSnapshot":"after@call@608"}
{"type":"before","callId":"call@610","startTime":81390.869,"apiName":"expect.toBeHidden","class":"Frame","method":"expect","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div[data-pendo-campaignform-modetab-maxdialingratioinput]>div.co-form__feedback","expression":"to.be.hidden","expectedValue":{"value":{"v":"undefined"},"handles":[]},"isNot":false,"timeout":6000},"stepId":"expect@74","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@610"}
{"type":"log","callId":"call@610","time":81397.149,"message":"expect.toBeHidden with timeout 6000ms"}
{"type":"log","callId":"call@610","time":81397.158,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div[data-pendo-campaignform-modetab-maxdialingratioinput]>div.co-form__feedback')"}
{"type":"after","callId":"call@610","endTime":81399.928,"result":{"matches":true},"afterSnapshot":"after@call@610"}
{"type":"before","callId":"call@612","startTime":81406.331,"apiName":"page.screenshot","class":"Page","method":"screenshot","params":{},"stepId":"pw:api@75","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@612"}
{"type":"log","callId":"call@612","time":81411.903,"message":"taking page screenshot"}
{"type":"log","callId":"call@612","time":81417.055,"message":"waiting for fonts to load..."}
{"type":"log","callId":"call@612","time":81417.396,"message":"fonts loaded"}
{"type":"after","callId":"call@612","endTime":81499.753,"result":{"binary":"<Buffer>"},"afterSnapshot":"after@call@612"}
{"type":"before","callId":"call@614","startTime":81510.503,"apiName":"locator.fill","class":"Frame","method":"fill","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> #max-abandonment-rate-id-input","strict":true,"value":"10.55"},"stepId":"pw:api@79","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@614"}
{"type":"log","callId":"call@614","time":81516.825,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('#max-abandonment-rate-id-input')"}
{"type":"log","callId":"call@614","time":81521.092,"message":"  locator resolved to <input min=\"1\" value=\"\" max=\"100\" type=\"number\" autocomplete=\"off\" id=\"max-abandonment-rate-id-input\"/>"}
{"type":"log","callId":"call@614","time":81521.594,"message":"  fill(\"10.55\")"}
{"type":"log","callId":"call@614","time":81521.601,"message":"attempting fill action"}
{"type":"log","callId":"call@614","time":81528.031,"message":"  waiting for element to be visible, enabled and editable"}
{"type":"after","callId":"call@614","endTime":81547.385,"afterSnapshot":"after@call@614"}
{"type":"before","callId":"call@616","startTime":81656.089,"apiName":"page.waitForTimeout","class":"Frame","method":"waitForTimeout","params":{"timeout":1000},"stepId":"pw:api@80","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@616"}
{"type":"after","callId":"call@616","endTime":82662.821,"afterSnapshot":"after@call@616"}
{"type":"before","callId":"call@618","startTime":82671.721,"apiName":"expect.not.toHaveClass","class":"Frame","method":"expect","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div[data-pendo-campaignform-modetab-maxabandrateinput]","expression":"to.have.class","expectedText":[{"regexSource":"co--error","regexFlags":""}],"expectedValue":{"value":{"v":"undefined"},"handles":[]},"isNot":true,"timeout":6000},"stepId":"expect@81","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@618"}
{"type":"log","callId":"call@618","time":82677.569,"message":"expect.not.toHaveClass with timeout 6000ms"}
{"type":"log","callId":"call@618","time":82677.575,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div[data-pendo-campaignform-modetab-maxabandrateinput]')"}
{"type":"log","callId":"call@618","time":82680.702,"message":"  locator resolved to <div class=\"co-form__field\" data-pendo-campaignform-modetab-maxabandrateinput=\"true\">…</div>"}
{"type":"after","callId":"call@618","endTime":82680.776,"result":{"matches":false,"received":{"s":"co-form__field"}},"afterSnapshot":"after@call@618"}
{"type":"before","callId":"call@620","startTime":82687.555,"apiName":"page.screenshot","class":"Page","method":"screenshot","params":{},"stepId":"pw:api@82","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@620"}
{"type":"log","callId":"call@620","time":82692.955,"message":"taking page screenshot"}
{"type":"log","callId":"call@620","time":82697.632,"message":"waiting for fonts to load..."}
{"type":"log","callId":"call@620","time":82697.986,"message":"fonts loaded"}
{"type":"after","callId":"call@620","endTime":82783.753,"result":{"binary":"<Buffer>"},"afterSnapshot":"after@call@620"}
{"type":"before","callId":"call@622","startTime":82796.033,"apiName":"locator.fill","class":"Frame","method":"fill","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> #abandonment-timeout-id-input","strict":true,"value":"10"},"stepId":"pw:api@86","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@622"}
{"type":"log","callId":"call@622","time":82803.446,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('#abandonment-timeout-id-input')"}
{"type":"log","callId":"call@622","time":82808.384,"message":"  locator resolved to <input min=\"2\" max=\"30\" value=\"\" type=\"number\" autocomplete=\"off\" id=\"abandonment-timeout-id-input\"/>"}
{"type":"log","callId":"call@622","time":82809.101,"message":"  fill(\"10\")"}
{"type":"log","callId":"call@622","time":82809.11,"message":"attempting fill action"}
{"type":"log","callId":"call@622","time":82815.559,"message":"  waiting for element to be visible, enabled and editable"}
{"type":"after","callId":"call@622","endTime":82830.428,"afterSnapshot":"after@call@622"}
{"type":"before","callId":"call@624","startTime":82940.958,"apiName":"page.waitForTimeout","class":"Frame","method":"waitForTimeout","params":{"timeout":1000},"stepId":"pw:api@87","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@624"}
{"type":"after","callId":"call@624","endTime":83948.752,"afterSnapshot":"after@call@624"}
{"type":"before","callId":"call@626","startTime":83957.75,"apiName":"expect.not.toHaveClass","class":"Frame","method":"expect","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div[data-pendo-campaignform-modetab-abandtimeoutinput]","expression":"to.have.class","expectedText":[{"regexSource":"co--error","regexFlags":""}],"expectedValue":{"value":{"v":"undefined"},"handles":[]},"isNot":true,"timeout":6000},"stepId":"expect@88","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@626"}
{"type":"log","callId":"call@626","time":83964.393,"message":"expect.not.toHaveClass with timeout 6000ms"}
{"type":"log","callId":"call@626","time":83964.399,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div[data-pendo-campaignform-modetab-abandtimeoutinput]')"}
{"type":"log","callId":"call@626","time":83967.524,"message":"  locator resolved to <div class=\"co-form__field\" data-pendo-campaignform-modetab-abandtimeoutinput=\"true\">…</div>"}
{"type":"after","callId":"call@626","endTime":83967.613,"result":{"matches":false,"received":{"s":"co-form__field"}},"afterSnapshot":"after@call@626"}
{"type":"before","callId":"call@628","startTime":83976.922,"apiName":"page.screenshot","class":"Page","method":"screenshot","params":{},"stepId":"pw:api@89","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@628"}
{"type":"log","callId":"call@628","time":83983.66,"message":"taking page screenshot"}
{"type":"log","callId":"call@628","time":83989.345,"message":"waiting for fonts to load..."}
{"type":"log","callId":"call@628","time":83989.953,"message":"fonts loaded"}
{"type":"after","callId":"call@628","endTime":84071.045,"result":{"binary":"<Buffer>"},"afterSnapshot":"after@call@628"}
{"type":"before","callId":"call@630","startTime":84084.702,"apiName":"locator.fill","class":"Frame","method":"fill","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> #max-ring-time-id-input","strict":true,"value":"100"},"stepId":"pw:api@93","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@630"}
{"type":"log","callId":"call@630","time":84093.36,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('#max-ring-time-id-input')"}
{"type":"log","callId":"call@630","time":84097.859,"message":"  locator resolved to <input min=\"6\" value=\"\" max=\"120\" type=\"number\" autocomplete=\"off\" id=\"max-ring-time-id-input\"/>"}
{"type":"log","callId":"call@630","time":84098.453,"message":"  fill(\"100\")"}
{"type":"log","callId":"call@630","time":84098.459,"message":"attempting fill action"}
{"type":"log","callId":"call@630","time":84105.636,"message":"  waiting for element to be visible, enabled and editable"}
{"type":"after","callId":"call@630","endTime":84120.331,"afterSnapshot":"after@call@630"}
{"type":"before","callId":"call@632","startTime":84228.742,"apiName":"page.waitForTimeout","class":"Frame","method":"waitForTimeout","params":{"timeout":1000},"stepId":"pw:api@94","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@632"}
{"type":"after","callId":"call@632","endTime":85236.097,"afterSnapshot":"after@call@632"}
{"type":"before","callId":"call@634","startTime":85245.042,"apiName":"expect.not.toHaveClass","class":"Frame","method":"expect","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div[data-pendo-campaignform-modetab-maxringtimeinput]","expression":"to.have.class","expectedText":[{"regexSource":"co--error","regexFlags":""}],"expectedValue":{"value":{"v":"undefined"},"handles":[]},"isNot":true,"timeout":6000},"stepId":"expect@95","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@634"}
{"type":"log","callId":"call@634","time":85251.781,"message":"expect.not.toHaveClass with timeout 6000ms"}
{"type":"log","callId":"call@634","time":85251.794,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div[data-pendo-campaignform-modetab-maxringtimeinput]')"}
{"type":"log","callId":"call@634","time":85255.404,"message":"  locator resolved to <div class=\"co-form__field\" data-pendo-campaignform-modetab-maxringtimeinput=\"true\">…</div>"}
{"type":"after","callId":"call@634","endTime":85255.479,"result":{"matches":false,"received":{"s":"co-form__field"}},"afterSnapshot":"after@call@634"}
{"type":"before","callId":"call@636","startTime":85263.839,"apiName":"page.screenshot","class":"Page","method":"screenshot","params":{},"stepId":"pw:api@96","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@636"}
{"type":"log","callId":"call@636","time":85270.417,"message":"taking page screenshot"}
{"type":"log","callId":"call@636","time":85275.608,"message":"waiting for fonts to load..."}
{"type":"log","callId":"call@636","time":85276.072,"message":"fonts loaded"}
{"type":"after","callId":"call@636","endTime":85370.2,"result":{"binary":"<Buffer>"},"afterSnapshot":"after@call@636"}
{"type":"before","callId":"call@638","startTime":85381.55,"apiName":"locator.click","class":"Frame","method":"click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> [data-testid=\"nextButton\"]","strict":true},"stepId":"pw:api@100","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@638"}
{"type":"log","callId":"call@638","time":85388.332,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('[data-testid=\"nextButton\"]')"}
{"type":"log","callId":"call@638","time":85393.778,"message":"  locator resolved to <button type=\"button\" data-testid=\"nextButton\" class=\"co-button co--primary\" data-campaign-header-nextbutton=\"true\">…</button>"}
{"type":"log","callId":"call@638","time":85394.445,"message":"attempting click action"}
{"type":"log","callId":"call@638","time":85395.006,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@638","time":85425.055,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@638","time":85425.068,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@638","time":85425.338,"message":"  done scrolling"}
{"type":"log","callId":"call@638","time":85437.432,"message":"  performing click action"}
{"type":"log","callId":"call@638","time":85513.39,"message":"  click action done"}
{"type":"log","callId":"call@638","time":85513.405,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@638","time":85515.016,"message":"  navigations have finished"}
{"type":"after","callId":"call@638","endTime":85515.143,"point":{"x":1225.42,"y":84},"afterSnapshot":"after@call@638"}
{"type":"before","callId":"call@640","startTime":85632.345,"apiName":"locator.textContent","class":"Frame","method":"textContent","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-grid__column.co-grid__column--min>h2.co-heading","strict":true},"stepId":"pw:api@101","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@640"}
{"type":"log","callId":"call@640","time":85642.124,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div.co-grid__column.co-grid__column--min>h2.co-heading')"}
{"type":"log","callId":"call@640","time":85697.8,"message":"  locator resolved to <h2 class=\"co-heading\">Dialing strategy</h2>"}
{"type":"after","callId":"call@640","endTime":85697.849,"result":{"value":"Dialing strategy"},"afterSnapshot":"after@call@640"}
{"type":"before","callId":"call@642","startTime":85713.19,"apiName":"page.screenshot","class":"Page","method":"screenshot","params":{},"stepId":"pw:api@103","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@642"}
{"type":"log","callId":"call@642","time":85722.206,"message":"taking page screenshot"}
{"type":"log","callId":"call@642","time":85725.911,"message":"waiting for fonts to load..."}
{"type":"log","callId":"call@642","time":85731.135,"message":"fonts loaded"}
{"type":"after","callId":"call@642","endTime":85817.872,"result":{"binary":"<Buffer>"},"afterSnapshot":"after@call@642"}
{"type":"before","callId":"call@644","startTime":85836.502,"apiName":"page.waitForResponse","class":"Page","method":"waitForEventInfo","params":{"info":{"waitId":"d27bda6f291f0b96770f64196962c141","phase":"before","event":"response"}},"stepId":"pw:api@107","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@644"}
{"type":"before","callId":"call@648","startTime":85838.925,"apiName":"locator.click","class":"Frame","method":"click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-section__content:has(label[for=\"caller-id-chip-group\"]) a[href=\"#\"]>span","strict":true},"stepId":"pw:api@108","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@648"}
{"type":"log","callId":"call@648","time":85855.268,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div.co-section__content:has(label[for=\"caller-id-chip-group\"]) a[href=\"#\"]>span')"}
{"type":"log","callId":"call@648","time":85862.918,"message":"  locator resolved to <span class=\"co-label-icon\">…</span>"}
{"type":"log","callId":"call@648","time":85863.531,"message":"attempting click action"}
{"type":"log","callId":"call@648","time":85863.799,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@648","time":85892.867,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@648","time":85892.88,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@648","time":85893.626,"message":"  done scrolling"}
{"type":"log","callId":"call@648","time":85907.161,"message":"  performing click action"}
{"type":"log","callId":"call@648","time":85980.921,"message":"  click action done"}
{"type":"log","callId":"call@648","time":85980.938,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@648","time":85987.428,"message":"  navigations have finished"}
{"type":"after","callId":"call@648","endTime":85987.572,"point":{"x":353.03,"y":388},"afterSnapshot":"after@call@648"}
{"type":"after","callId":"call@644","endTime":86256.631,"afterSnapshot":"after@call@644"}
{"type":"before","callId":"call@656","startTime":86284.913,"apiName":"response.json","class":"Response","method":"body","params":{},"stepId":"pw:api@109","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@656"}
{"type":"after","callId":"call@656","endTime":86290.636,"result":{"binary":"<Buffer>"},"afterSnapshot":"after@call@656"}
{"type":"before","callId":"call@658","startTime":86292.315,"apiName":"locator.waitFor","class":"Frame","method":"waitForSelector","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> nth=0","strict":true,"timeout":60000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@110","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@658"}
{"type":"log","callId":"call@658","time":86336.584,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div#panels-layout-content-id').locator('table').first() to be visible"}
{"type":"log","callId":"call@658","time":86373.486,"message":"  locator resolved to visible <table class=\"co-table\">…</table>"}
{"type":"after","callId":"call@658","endTime":86373.585,"result":{},"afterSnapshot":"after@call@658"}
{"type":"before","callId":"call@660","startTime":86385.413,"apiName":"locator.waitFor","class":"Frame","method":"waitForSelector","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> nth=0","strict":true,"timeout":60000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@111","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@660"}
{"type":"log","callId":"call@660","time":86394.096,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div#panels-layout-content-id').locator('table').first() to be visible"}
{"type":"log","callId":"call@660","time":86398.377,"message":"  locator resolved to visible <table class=\"co-table\">…</table>"}
{"type":"after","callId":"call@660","endTime":86398.424,"result":{},"afterSnapshot":"after@call@660"}
{"type":"before","callId":"call@662","startTime":86407.256,"apiName":"locator.waitFor","class":"Frame","method":"waitForSelector","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> //tbody/tr[1]/td[2] >> div.co-placeholder--animated","strict":true,"timeout":10000,"state":"hidden","omitReturnValue":true},"stepId":"pw:api@112","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@662"}
{"type":"log","callId":"call@662","time":86418.307,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div#panels-layout-content-id').locator('table').locator('//tbody/tr[1]/td[2]').locator('div.co-placeholder--animated') to be hidden"}
{"type":"log","callId":"call@662","time":86434.549,"message":"  locator resolved to visible <div class=\"co-placeholder co-placeholder--animated\"></div>"}
{"type":"log","callId":"call@662","time":86438.755,"message":"  locator resolved to visible <div class=\"co-placeholder co-placeholder--animated\"></div>"}
{"type":"log","callId":"call@662","time":86464.934,"message":"  locator resolved to visible <div class=\"co-placeholder co-placeholder--animated\"></div>"}
{"type":"log","callId":"call@662","time":86534.792,"message":"  locator resolved to visible <div class=\"co-placeholder co-placeholder--animated\"></div>"}
{"type":"after","callId":"call@662","endTime":86640.431,"result":{},"afterSnapshot":"after@call@662"}
{"type":"before","callId":"call@664","startTime":86657.357,"apiName":"page.waitForTimeout","class":"Frame","method":"waitForTimeout","params":{"timeout":1000},"stepId":"pw:api@113","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@664"}
{"type":"after","callId":"call@664","endTime":87674.137,"afterSnapshot":"after@call@664"}
{"type":"before","callId":"call@666","startTime":87684.215,"apiName":"page.waitForTimeout","class":"Frame","method":"waitForTimeout","params":{"timeout":3000},"stepId":"pw:api@114","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@666"}
{"type":"after","callId":"call@666","endTime":90690.888,"afterSnapshot":"after@call@666"}
{"type":"before","callId":"call@668","startTime":90702.04,"apiName":"locator.waitFor","class":"Frame","method":"waitForSelector","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> nth=0","strict":true,"timeout":60000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@115","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@668"}
{"type":"log","callId":"call@668","time":90711.336,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div#panels-layout-content-id').locator('table').first() to be visible"}
{"type":"log","callId":"call@668","time":90714.823,"message":"  locator resolved to visible <table class=\"co-table\">…</table>"}
{"type":"after","callId":"call@668","endTime":90714.861,"result":{},"afterSnapshot":"after@call@668"}
{"type":"before","callId":"call@670","startTime":90723.554,"apiName":"locator.waitFor","class":"Frame","method":"waitForSelector","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> thead >> nth=0","strict":true,"timeout":60000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@116","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@670"}
{"type":"log","callId":"call@670","time":90731.924,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div#panels-layout-content-id').locator('table').locator('thead').first() to be visible"}
{"type":"log","callId":"call@670","time":90735.449,"message":"  locator resolved to visible <thead class=\"co-table__head\">…</thead>"}
{"type":"after","callId":"call@670","endTime":90735.488,"result":{},"afterSnapshot":"after@call@670"}
{"type":"before","callId":"call@672","startTime":90744.765,"apiName":"locator.waitFor","class":"Frame","method":"waitForSelector","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> //tbody/tr[1] >> //td[1] >> div>input >> nth=0","strict":true,"timeout":60000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@117","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@672"}
{"type":"log","callId":"call@672","time":90752.817,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div#panels-layout-content-id').locator('table').locator('//tbody/tr[1]').locator('//td[1]').locator('div>input').first() to be visible"}
{"type":"log","callId":"call@672","time":90756.438,"message":"  locator resolved to visible <input id=\"co-369-102\" type=\"checkbox\" value=\"+15617821431\" data-pendo-campaignform-dialingstrategy-selectcallerids-calleridcheckbox=\"true\"/>"}
{"type":"after","callId":"call@672","endTime":90756.476,"result":{},"afterSnapshot":"after@call@672"}
{"type":"before","callId":"call@674","startTime":90765.481,"apiName":"locator.click","class":"Frame","method":"click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> //tbody/tr[1] >> //td[1] >> div>input","strict":true},"stepId":"pw:api@118","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@674"}
{"type":"log","callId":"call@674","time":90774.38,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div#panels-layout-content-id').locator('table').locator('//tbody/tr[1]').locator('//td[1]').locator('div>input')"}
{"type":"log","callId":"call@674","time":90777.583,"message":"  locator resolved to <input id=\"co-369-102\" type=\"checkbox\" value=\"+15617821431\" data-pendo-campaignform-dialingstrategy-selectcallerids-calleridcheckbox=\"true\"/>"}
{"type":"log","callId":"call@674","time":90778.094,"message":"attempting click action"}
{"type":"log","callId":"call@674","time":90778.337,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@674","time":90808.383,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@674","time":90808.394,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@674","time":90808.717,"message":"  done scrolling"}
{"type":"log","callId":"call@674","time":90822.482,"message":"  performing click action"}
{"type":"log","callId":"call@674","time":90846.679,"message":"  click action done"}
{"type":"log","callId":"call@674","time":90846.691,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@674","time":90846.891,"message":"  navigations have finished"}
{"type":"after","callId":"call@674","endTime":90846.971,"point":{"x":714,"y":338.5},"afterSnapshot":"after@call@674"}
{"type":"before","callId":"call@676","startTime":90960.22,"apiName":"locator.isVisible","class":"Frame","method":"isVisible","params":{"selector":"div[data-testid=\"toaster\"] div[data-co-name=\"Message\"]:nth-child(1) div[data-testid]","strict":true},"stepId":"pw:api@119","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@676"}
{"type":"log","callId":"call@676","time":90969.841,"message":"  checking visibility of locator('div[data-testid=\"toaster\"] div[data-co-name=\"Message\"]:nth-child(1) div[data-testid]')"}
{"type":"after","callId":"call@676","endTime":90971.585,"result":{"value":false},"afterSnapshot":"after@call@676"}
{"type":"before","callId":"call@678","startTime":90987.96,"apiName":"locator.click","class":"Frame","method":"click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> button>*:text(\"Select\")","strict":true},"stepId":"pw:api@120","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@678"}
{"type":"log","callId":"call@678","time":90998.768,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div#panels-layout-content-id').locator('button>*:text(\"Select\")')"}
{"type":"log","callId":"call@678","time":91007.314,"message":"  locator resolved to <p data-co-name=\"Text\" data-co-version=\"1.5.0\" data-co-project=\"cobalt\" class=\"react-typography_1-5-0_co-text\">Select</p>"}
{"type":"log","callId":"call@678","time":91008.152,"message":"attempting click action"}
{"type":"log","callId":"call@678","time":91009.635,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@678","time":91042.066,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@678","time":91042.081,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@678","time":91042.799,"message":"  done scrolling"}
{"type":"log","callId":"call@678","time":91060.086,"message":"  performing click action"}
{"type":"log","callId":"call@678","time":91121.574,"message":"  click action done"}
{"type":"log","callId":"call@678","time":91121.589,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@678","time":91122.634,"message":"  navigations have finished"}
{"type":"after","callId":"call@678","endTime":91122.723,"point":{"x":1218.47,"y":688},"afterSnapshot":"after@call@678"}
{"type":"before","callId":"call@680","startTime":91235.976,"apiName":"page.screenshot","class":"Page","method":"screenshot","params":{},"stepId":"pw:api@121","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@680"}
{"type":"log","callId":"call@680","time":91243.593,"message":"taking page screenshot"}
{"type":"log","callId":"call@680","time":91258.146,"message":"waiting for fonts to load..."}
{"type":"log","callId":"call@680","time":91260.972,"message":"fonts loaded"}
{"type":"after","callId":"call@680","endTime":91353.856,"result":{"binary":"<Buffer>"},"afterSnapshot":"after@call@680"}
{"type":"before","callId":"call@682","startTime":91368.618,"apiName":"locator.click","class":"Frame","method":"click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> :text-is('MON')","strict":true},"stepId":"pw:api@125","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@682"}
{"type":"log","callId":"call@682","time":91376.312,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator(':text-is(\\'MON\\')')"}
{"type":"log","callId":"call@682","time":91385.154,"message":"  locator resolved to <label class=\"co-selection-group__label\" for=\"calling-hours-id-_3oy7m-MONDAY-0\">MON</label>"}
{"type":"log","callId":"call@682","time":91385.857,"message":"attempting click action"}
{"type":"log","callId":"call@682","time":91386.142,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@682","time":91409.002,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@682","time":91409.012,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@682","time":91410.223,"message":"  done scrolling"}
{"type":"log","callId":"call@682","time":91424.344,"message":"  <div class=\"co-panels-layout__overlay\"></div> intercepts pointer events"}
{"type":"log","callId":"call@682","time":91424.353,"message":"retrying click action"}
{"type":"log","callId":"call@682","time":91425.241,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@682","time":91458.311,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@682","time":91458.327,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@682","time":91459.023,"message":"  done scrolling"}
{"type":"log","callId":"call@682","time":91472.903,"message":"  <div class=\"co-panels-layout__overlay\"></div> intercepts pointer events"}
{"type":"log","callId":"call@682","time":91472.912,"message":"retrying click action"}
{"type":"log","callId":"call@682","time":91472.916,"message":"  waiting 20ms"}
{"type":"log","callId":"call@682","time":91494.451,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@682","time":91525.071,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@682","time":91525.092,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@682","time":91525.875,"message":"  done scrolling"}
{"type":"log","callId":"call@682","time":91540.17,"message":"  <div class=\"co-panels-layout__overlay\"></div> intercepts pointer events"}
{"type":"log","callId":"call@682","time":91540.18,"message":"retrying click action"}
{"type":"log","callId":"call@682","time":91540.183,"message":"  waiting 100ms"}
{"type":"log","callId":"call@682","time":91642.737,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@682","time":91675.943,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@682","time":91675.96,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@682","time":91676.781,"message":"  done scrolling"}
{"type":"log","callId":"call@682","time":91692.988,"message":"  performing click action"}
{"type":"log","callId":"call@682","time":91724.255,"message":"  click action done"}
{"type":"log","callId":"call@682","time":91724.27,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@682","time":91725.523,"message":"  navigations have finished"}
{"type":"after","callId":"call@682","endTime":91725.642,"point":{"x":296.61,"y":60},"afterSnapshot":"after@call@682"}
{"type":"before","callId":"call@684","startTime":91838.267,"apiName":"locator.click","class":"Frame","method":"click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> :text-is('THU')","strict":true},"stepId":"pw:api@126","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@684"}
{"type":"log","callId":"call@684","time":91847.028,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator(':text-is(\\'THU\\')')"}
{"type":"log","callId":"call@684","time":91853.814,"message":"  locator resolved to <label class=\"co-selection-group__label\" for=\"calling-hours-id-_3oy7m-THURSDAY-3\">THU</label>"}
{"type":"log","callId":"call@684","time":91854.52,"message":"attempting click action"}
{"type":"log","callId":"call@684","time":91855.285,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@684","time":91875.378,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@684","time":91875.391,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@684","time":91876.417,"message":"  done scrolling"}
{"type":"log","callId":"call@684","time":91893.579,"message":"  performing click action"}
{"type":"log","callId":"call@684","time":91930.248,"message":"  click action done"}
{"type":"log","callId":"call@684","time":91930.261,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@684","time":91930.446,"message":"  navigations have finished"}
{"type":"after","callId":"call@684","endTime":91930.522,"point":{"x":483.32,"y":60},"afterSnapshot":"after@call@684"}
{"type":"before","callId":"call@686","startTime":92042.901,"apiName":"page.screenshot","class":"Page","method":"screenshot","params":{},"stepId":"pw:api@127","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@686"}
{"type":"log","callId":"call@686","time":92052.898,"message":"taking page screenshot"}
{"type":"log","callId":"call@686","time":92056.672,"message":"waiting for fonts to load..."}
{"type":"log","callId":"call@686","time":92059.479,"message":"fonts loaded"}
{"type":"after","callId":"call@686","endTime":92149.436,"result":{"binary":"<Buffer>"},"afterSnapshot":"after@call@686"}
{"type":"before","callId":"call@688","startTime":92168.299,"apiName":"locator.click","class":"Frame","method":"click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.sc-bdVaJa.cxEcVj input","strict":true},"stepId":"pw:api@131","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@688"}
{"type":"log","callId":"call@688","time":92178.021,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div.sc-bdVaJa.cxEcVj input')"}
{"type":"log","callId":"call@688","time":92182.184,"message":"  locator resolved to <input value=\"\" type=\"text\" data-co-name=\"Input\" data-co-version=\"1.3.8\" data-co-project=\"cobalt\" class=\"react-input_1-3-8_co-input react-input_1-3-8_co-input--unstyled react-input_1-3-8_co-input--medium react-input_1-3-8_co-input--align-left\"/>"}
{"type":"log","callId":"call@688","time":92182.984,"message":"attempting click action"}
{"type":"log","callId":"call@688","time":92183.268,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@688","time":92209.11,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@688","time":92209.122,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@688","time":92209.456,"message":"  done scrolling"}
{"type":"log","callId":"call@688","time":92226.017,"message":"  performing click action"}
{"type":"log","callId":"call@688","time":92241.054,"message":"  click action done"}
{"type":"log","callId":"call@688","time":92241.065,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@688","time":92242.945,"message":"  navigations have finished"}
{"type":"after","callId":"call@688","endTime":92243.05,"point":{"x":392.87,"y":124},"afterSnapshot":"after@call@688"}
{"type":"before","callId":"call@690","startTime":92354.353,"apiName":"locator.fill","class":"Frame","method":"fill","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> #timepicker-in-popup-id-From-hours input","strict":true,"value":"07"},"stepId":"pw:api@132","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@690"}
{"type":"log","callId":"call@690","time":92363.456,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('#timepicker-in-popup-id-From-hours input')"}
{"type":"log","callId":"call@690","time":92367.583,"message":"  locator resolved to <input min=\"1\" max=\"12\" value=\"12\" type=\"number\"/>"}
{"type":"log","callId":"call@690","time":92368.283,"message":"  fill(\"07\")"}
{"type":"log","callId":"call@690","time":92368.292,"message":"attempting fill action"}
{"type":"log","callId":"call@690","time":92378.694,"message":"  waiting for element to be visible, enabled and editable"}
{"type":"after","callId":"call@690","endTime":92407.26,"afterSnapshot":"after@call@690"}
{"type":"before","callId":"call@692","startTime":92517.536,"apiName":"locator.fill","class":"Frame","method":"fill","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> #timepicker-in-popup-id-From-minutes input","strict":true,"value":"30"},"stepId":"pw:api@133","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@692"}
{"type":"log","callId":"call@692","time":92526.86,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('#timepicker-in-popup-id-From-minutes input')"}
{"type":"log","callId":"call@692","time":92530.885,"message":"  locator resolved to <input min=\"0\" max=\"59\" value=\"00\" type=\"number\"/>"}
{"type":"log","callId":"call@692","time":92531.676,"message":"  fill(\"30\")"}
{"type":"log","callId":"call@692","time":92531.685,"message":"attempting fill action"}
{"type":"log","callId":"call@692","time":92538.976,"message":"  waiting for element to be visible, enabled and editable"}
{"type":"after","callId":"call@692","endTime":92564.664,"afterSnapshot":"after@call@692"}
{"type":"before","callId":"call@694","startTime":92675.895,"apiName":"locator.textContent","class":"Frame","method":"textContent","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> [for=\"timepicker-in-popup-id-From-meridian\"]","strict":true},"stepId":"pw:api@134","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@694"}
{"type":"log","callId":"call@694","time":92683.465,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('[for=\"timepicker-in-popup-id-From-meridian\"]')"}
{"type":"log","callId":"call@694","time":92686.413,"message":"  locator resolved to <label for=\"timepicker-in-popup-id-From-meridian\">AM</label>"}
{"type":"after","callId":"call@694","endTime":92686.445,"result":{"value":"AM"},"afterSnapshot":"after@call@694"}
{"type":"before","callId":"call@696","startTime":92695.557,"apiName":"locator.click","class":"Frame","method":"click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div>label:not([for]):has(abbr)","strict":true},"stepId":"pw:api@135","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@696"}
{"type":"log","callId":"call@696","time":92703.241,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div>label:not([for]):has(abbr)')"}
{"type":"log","callId":"call@696","time":92707.569,"message":"  locator resolved to <label class=\"co-label\">…</label>"}
{"type":"log","callId":"call@696","time":92708.683,"message":"attempting click action"}
{"type":"log","callId":"call@696","time":92709.037,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@696","time":92742.159,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@696","time":92742.176,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@696","time":92742.849,"message":"  done scrolling"}
{"type":"log","callId":"call@696","time":92759.532,"message":"  performing click action"}
{"type":"log","callId":"call@696","time":92765.537,"message":"  click action done"}
{"type":"log","callId":"call@696","time":92765.544,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@696","time":92765.689,"message":"  navigations have finished"}
{"type":"after","callId":"call@696","endTime":92765.752,"point":{"x":749,"y":380},"afterSnapshot":"after@call@696"}
{"type":"before","callId":"call@698","startTime":92876.753,"apiName":"locator.click","class":"Frame","method":"click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.sc-bdVaJa.gpIkRm input","strict":true},"stepId":"pw:api@136","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@698"}
{"type":"log","callId":"call@698","time":92885.362,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div.sc-bdVaJa.gpIkRm input')"}
{"type":"log","callId":"call@698","time":92890.715,"message":"  locator resolved to <input value=\"\" type=\"text\" data-co-name=\"Input\" data-co-version=\"1.3.8\" data-co-project=\"cobalt\" class=\"react-input_1-3-8_co-input react-input_1-3-8_co-input--unstyled react-input_1-3-8_co-input--medium react-input_1-3-8_co-input--align-left\"/>"}
{"type":"log","callId":"call@698","time":92893.24,"message":"attempting click action"}
{"type":"log","callId":"call@698","time":92893.53,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@698","time":92925.307,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@698","time":92925.32,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@698","time":92925.826,"message":"  done scrolling"}
{"type":"log","callId":"call@698","time":92952.376,"message":"  performing click action"}
{"type":"log","callId":"call@698","time":92973.771,"message":"  click action done"}
{"type":"log","callId":"call@698","time":92973.786,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@698","time":92974.711,"message":"  navigations have finished"}
{"type":"after","callId":"call@698","endTime":92974.916,"point":{"x":615.01,"y":498},"afterSnapshot":"after@call@698"}
{"type":"before","callId":"call@700","startTime":93088.695,"apiName":"locator.fill","class":"Frame","method":"fill","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> #timepicker-in-popup-id-To-hours input","strict":true,"value":"11"},"stepId":"pw:api@137","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@700"}
{"type":"log","callId":"call@700","time":93136.305,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('#timepicker-in-popup-id-To-hours input')"}
{"type":"log","callId":"call@700","time":93142.338,"message":"  locator resolved to <input min=\"1\" max=\"12\" value=\"12\" type=\"number\"/>"}
{"type":"log","callId":"call@700","time":93143.103,"message":"  fill(\"11\")"}
{"type":"log","callId":"call@700","time":93143.112,"message":"attempting fill action"}
{"type":"log","callId":"call@700","time":93150.936,"message":"  waiting for element to be visible, enabled and editable"}
{"type":"after","callId":"call@700","endTime":93187.424,"afterSnapshot":"after@call@700"}
{"type":"before","callId":"call@702","startTime":93299.122,"apiName":"locator.fill","class":"Frame","method":"fill","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> #timepicker-in-popup-id-To-minutes input","strict":true,"value":"00"},"stepId":"pw:api@138","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@702"}
{"type":"log","callId":"call@702","time":93309.308,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('#timepicker-in-popup-id-To-minutes input')"}
{"type":"log","callId":"call@702","time":93313.371,"message":"  locator resolved to <input min=\"0\" max=\"59\" value=\"00\" type=\"number\"/>"}
{"type":"log","callId":"call@702","time":93314.004,"message":"  fill(\"00\")"}
{"type":"log","callId":"call@702","time":93314.012,"message":"attempting fill action"}
{"type":"log","callId":"call@702","time":93324.572,"message":"  waiting for element to be visible, enabled and editable"}
{"type":"after","callId":"call@702","endTime":93329.955,"afterSnapshot":"after@call@702"}
{"type":"before","callId":"call@704","startTime":93439.806,"apiName":"locator.textContent","class":"Frame","method":"textContent","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> [for=\"timepicker-in-popup-id-To-meridian\"]","strict":true},"stepId":"pw:api@139","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@704"}
{"type":"log","callId":"call@704","time":93447.962,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('[for=\"timepicker-in-popup-id-To-meridian\"]')"}
{"type":"log","callId":"call@704","time":93450.312,"message":"  locator resolved to <label for=\"timepicker-in-popup-id-To-meridian\">AM</label>"}
{"type":"after","callId":"call@704","endTime":93450.343,"result":{"value":"AM"},"afterSnapshot":"after@call@704"}
{"type":"before","callId":"call@706","startTime":93458.38,"apiName":"locator.click","class":"Frame","method":"click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div>label:not([for]):has(abbr)","strict":true},"stepId":"pw:api@140","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@706"}
{"type":"log","callId":"call@706","time":93467.741,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div>label:not([for]):has(abbr)')"}
{"type":"log","callId":"call@706","time":93471.542,"message":"  locator resolved to <label class=\"co-label\">…</label>"}
{"type":"log","callId":"call@706","time":93472.013,"message":"attempting click action"}
{"type":"log","callId":"call@706","time":93472.22,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@706","time":93491.392,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@706","time":93491.401,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@706","time":93491.884,"message":"  done scrolling"}
{"type":"log","callId":"call@706","time":93504.035,"message":"  performing click action"}
{"type":"log","callId":"call@706","time":93512.146,"message":"  click action done"}
{"type":"log","callId":"call@706","time":93512.156,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@706","time":93512.342,"message":"  navigations have finished"}
{"type":"after","callId":"call@706","endTime":93512.435,"point":{"x":749,"y":380},"afterSnapshot":"after@call@706"}
{"type":"before","callId":"call@708","startTime":93621.831,"apiName":"page.screenshot","class":"Page","method":"screenshot","params":{},"stepId":"pw:api@141","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@708"}
{"type":"log","callId":"call@708","time":93630.326,"message":"taking page screenshot"}
{"type":"log","callId":"call@708","time":93633.969,"message":"waiting for fonts to load..."}
{"type":"log","callId":"call@708","time":93634.374,"message":"fonts loaded"}
{"type":"after","callId":"call@708","endTime":93716.616,"result":{"binary":"<Buffer>"},"afterSnapshot":"after@call@708"}
{"type":"before","callId":"call@710","startTime":93731.893,"apiName":"locator.fill","class":"Frame","method":"fill","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> #max-attempts-per-record-id-input","strict":true,"value":"3"},"stepId":"pw:api@145","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@710"}
{"type":"log","callId":"call@710","time":93739.821,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('#max-attempts-per-record-id-input')"}
{"type":"log","callId":"call@710","time":93743.783,"message":"  locator resolved to <input min=\"1\" value=\"\" max=\"100\" type=\"number\" autocomplete=\"off\" id=\"max-attempts-per-record-id-input\"/>"}
{"type":"log","callId":"call@710","time":93744.36,"message":"  fill(\"3\")"}
{"type":"log","callId":"call@710","time":93744.366,"message":"attempting fill action"}
{"type":"log","callId":"call@710","time":93751.613,"message":"  waiting for element to be visible, enabled and editable"}
{"type":"after","callId":"call@710","endTime":93773.606,"afterSnapshot":"after@call@710"}
{"type":"before","callId":"call@712","startTime":93886.167,"apiName":"page.waitForTimeout","class":"Frame","method":"waitForTimeout","params":{"timeout":1000},"stepId":"pw:api@146","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@712"}
{"type":"after","callId":"call@712","endTime":94895.855,"afterSnapshot":"after@call@712"}
{"type":"before","callId":"call@714","startTime":94907.393,"apiName":"expect.toBeHidden","class":"Frame","method":"expect","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> table:has(#max-attempts-per-record-id-group):nth-child(1) >> div.co-form__feedback","expression":"to.be.hidden","expectedValue":{"value":{"v":"undefined"},"handles":[]},"isNot":false,"timeout":6000},"stepId":"expect@147","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@714"}
{"type":"log","callId":"call@714","time":94915.489,"message":"expect.toBeHidden with timeout 6000ms"}
{"type":"log","callId":"call@714","time":94915.497,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('table:has(#max-attempts-per-record-id-group):nth-child(1)').locator('div.co-form__feedback')"}
{"type":"after","callId":"call@714","endTime":94919.113,"result":{"matches":true},"afterSnapshot":"after@call@714"}
{"type":"before","callId":"call@716","startTime":94929.298,"apiName":"page.screenshot","class":"Page","method":"screenshot","params":{},"stepId":"pw:api@148","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@716"}
{"type":"log","callId":"call@716","time":94936.722,"message":"taking page screenshot"}
{"type":"log","callId":"call@716","time":94939.791,"message":"waiting for fonts to load..."}
{"type":"log","callId":"call@716","time":94940.157,"message":"fonts loaded"}
{"type":"after","callId":"call@716","endTime":95035.409,"result":{"binary":"<Buffer>"},"afterSnapshot":"after@call@716"}
{"type":"before","callId":"call@718","startTime":95047.595,"apiName":"locator.click","class":"Frame","method":"click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> #retry-period-id-dropdown","strict":true},"stepId":"pw:api@152","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@718"}
{"type":"log","callId":"call@718","time":95055.659,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('#retry-period-id-dropdown')"}
{"type":"log","callId":"call@718","time":95059.36,"message":"  locator resolved to <div data-co-name=\"Dropdown\" data-co-version=\"1.5.2\" data-co-project=\"cobalt\" id=\"retry-period-id-dropdown\" class=\"react-dropdown_1-5-2_co-dropdown\">…</div>"}
{"type":"log","callId":"call@718","time":95059.953,"message":"attempting click action"}
{"type":"log","callId":"call@718","time":95060.301,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@718","time":95091.819,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@718","time":95091.828,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@718","time":95092.17,"message":"  done scrolling"}
{"type":"log","callId":"call@718","time":95104.258,"message":"  performing click action"}
{"type":"log","callId":"call@718","time":95115.922,"message":"  click action done"}
{"type":"log","callId":"call@718","time":95115.933,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@718","time":95116.208,"message":"  navigations have finished"}
{"type":"after","callId":"call@718","endTime":95116.292,"point":{"x":629.95,"y":380},"afterSnapshot":"after@call@718"}
{"type":"before","callId":"call@720","startTime":95228.045,"apiName":"locator.click","class":"Frame","method":"click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div[data-transition=\"entered\"][data-co-name=\"Popup\"] ul >> li *:text(\"minute(s)\")","strict":true},"stepId":"pw:api@153","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@720"}
{"type":"log","callId":"call@720","time":95236.076,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div[data-transition=\"entered\"][data-co-name=\"Popup\"] ul').locator('li *:text(\"minute(s)\")')"}
{"type":"log","callId":"call@720","time":95243.038,"message":"  locator resolved to <p data-co-name=\"Text\" data-co-version=\"1.5.0\" data-co-project=\"cobalt\" class=\"react-typography_1-5-0_co-text react-typography_1-5-0_co-text--truncated\">minute(s)</p>"}
{"type":"log","callId":"call@720","time":95243.819,"message":"attempting click action"}
{"type":"log","callId":"call@720","time":95244.114,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@720","time":95275.144,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@720","time":95275.154,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@720","time":95275.774,"message":"  done scrolling"}
{"type":"log","callId":"call@720","time":95294.353,"message":"  performing click action"}
{"type":"log","callId":"call@720","time":95313.781,"message":"  click action done"}
{"type":"log","callId":"call@720","time":95313.795,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@720","time":95313.943,"message":"  navigations have finished"}
{"type":"after","callId":"call@720","endTime":95314.031,"point":{"x":638.99,"y":470},"afterSnapshot":"after@call@720"}
{"type":"before","callId":"call@722","startTime":95424.811,"apiName":"locator.fill","class":"Frame","method":"fill","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> #retry-period-id-input","strict":true,"value":"1"},"stepId":"pw:api@154","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@722"}
{"type":"log","callId":"call@722","time":95432.484,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('#retry-period-id-input')"}
{"type":"log","callId":"call@722","time":95436.843,"message":"  locator resolved to <input min=\"1\" value=\"\" max=\"43200\" type=\"number\" data-co-name=\"Input\" data-co-version=\"1.3.8\" data-co-project=\"cobalt\" id=\"retry-period-id-input\" class=\"react-input_1-3-8_co-input react-input_1-3-8_co-input--medium react-input_1-3-8_co-input--align-left\"/>"}
{"type":"log","callId":"call@722","time":95437.5,"message":"  fill(\"1\")"}
{"type":"log","callId":"call@722","time":95437.508,"message":"attempting fill action"}
{"type":"log","callId":"call@722","time":95447.324,"message":"  waiting for element to be visible, enabled and editable"}
{"type":"after","callId":"call@722","endTime":95470.872,"afterSnapshot":"after@call@722"}
{"type":"before","callId":"call@724","startTime":95581.941,"apiName":"page.waitForTimeout","class":"Frame","method":"waitForTimeout","params":{"timeout":1000},"stepId":"pw:api@155","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@724"}
{"type":"after","callId":"call@724","endTime":96591.192,"afterSnapshot":"after@call@724"}
{"type":"before","callId":"call@726","startTime":96600.05,"apiName":"expect.toBeHidden","class":"Frame","method":"expect","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> table:has(#retry-period-id-group):nth-child(1) >> div.co-form__feedback","expression":"to.be.hidden","expectedValue":{"value":{"v":"undefined"},"handles":[]},"isNot":false,"timeout":6000},"stepId":"expect@156","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@726"}
{"type":"log","callId":"call@726","time":96608.128,"message":"expect.toBeHidden with timeout 6000ms"}
{"type":"log","callId":"call@726","time":96608.135,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('table:has(#retry-period-id-group):nth-child(1)').locator('div.co-form__feedback')"}
{"type":"after","callId":"call@726","endTime":96611.864,"result":{"matches":true},"afterSnapshot":"after@call@726"}
{"type":"before","callId":"call@728","startTime":96621.064,"apiName":"page.screenshot","class":"Page","method":"screenshot","params":{},"stepId":"pw:api@157","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@728"}
{"type":"log","callId":"call@728","time":96629.238,"message":"taking page screenshot"}
{"type":"log","callId":"call@728","time":96633.817,"message":"waiting for fonts to load..."}
{"type":"log","callId":"call@728","time":96634.233,"message":"fonts loaded"}
{"type":"after","callId":"call@728","endTime":96712.3,"result":{"binary":"<Buffer>"},"afterSnapshot":"after@call@728"}
{"type":"before","callId":"call@730","startTime":96723.619,"apiName":"page.screenshot","class":"Page","method":"screenshot","params":{},"stepId":"pw:api@161","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@730"}
{"type":"log","callId":"call@730","time":96731.973,"message":"taking page screenshot"}
{"type":"log","callId":"call@730","time":96734.841,"message":"waiting for fonts to load..."}
{"type":"log","callId":"call@730","time":96735.241,"message":"fonts loaded"}
{"type":"after","callId":"call@730","endTime":96813.412,"result":{"binary":"<Buffer>"},"afterSnapshot":"after@call@730"}
{"type":"before","callId":"call@732","startTime":96825.872,"apiName":"page.waitForResponse","class":"Page","method":"waitForEventInfo","params":{"info":{"waitId":"c866a367f3fb82b5ede4b1978037eaa5","phase":"before","event":"response"}},"stepId":"pw:api@165","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@732"}
{"type":"before","callId":"call@736","startTime":96827.468,"apiName":"locator.click","class":"Frame","method":"click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> [data-testid=\"nextButton\"]","strict":true},"stepId":"pw:api@166","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@736"}
{"type":"log","callId":"call@736","time":96839.07,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('[data-testid=\"nextButton\"]')"}
{"type":"log","callId":"call@736","time":96842.66,"message":"  locator resolved to <button type=\"button\" data-testid=\"nextButton\" class=\"co-button co--primary\" data-campaign-header-nextbutton=\"true\">…</button>"}
{"type":"log","callId":"call@736","time":96843.266,"message":"attempting click action"}
{"type":"log","callId":"call@736","time":96843.541,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@736","time":96874.678,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@736","time":96874.686,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@736","time":96875.13,"message":"  done scrolling"}
{"type":"log","callId":"call@736","time":96889.988,"message":"  performing click action"}
{"type":"log","callId":"call@736","time":96936.59,"message":"  click action done"}
{"type":"log","callId":"call@736","time":96936.604,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@736","time":96938.308,"message":"  navigations have finished"}
{"type":"after","callId":"call@736","endTime":96938.424,"point":{"x":1201.42,"y":84},"afterSnapshot":"after@call@736"}
{"type":"after","callId":"call@732","endTime":97121.458,"afterSnapshot":"after@call@732"}
{"type":"before","callId":"call@744","startTime":97123.607,"apiName":"locator.textContent","class":"Frame","method":"textContent","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-grid__column.co-grid__column--min>h2.co-heading","strict":true},"stepId":"pw:api@167","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@744"}
{"type":"log","callId":"call@744","time":97164.61,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div.co-grid__column.co-grid__column--min>h2.co-heading')"}
{"type":"log","callId":"call@744","time":97169.879,"message":"  locator resolved to <h2 class=\"co-heading\">Agents</h2>"}
{"type":"after","callId":"call@744","endTime":97169.922,"result":{"value":"Agents"},"afterSnapshot":"after@call@744"}
{"type":"before","callId":"call@746","startTime":97179.46,"apiName":"page.screenshot","class":"Page","method":"screenshot","params":{},"stepId":"pw:api@169","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@746"}
{"type":"log","callId":"call@746","time":97185.749,"message":"taking page screenshot"}
{"type":"log","callId":"call@746","time":97188.896,"message":"waiting for fonts to load..."}
{"type":"log","callId":"call@746","time":97189.347,"message":"fonts loaded"}
{"type":"after","callId":"call@746","endTime":97265.404,"result":{"binary":"<Buffer>"},"afterSnapshot":"after@call@746"}
{"type":"before","callId":"call@748","startTime":97282.083,"apiName":"page.waitForResponse","class":"Page","method":"waitForEventInfo","params":{"info":{"waitId":"55b4a38264266a9de44c1f604aa47bd5","phase":"before","event":"response"}},"stepId":"pw:api@173","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@748"}
{"type":"before","callId":"call@752","startTime":97284.053,"apiName":"locator.click","class":"Frame","method":"click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> //*[contains(text(),\"Filters\")]","strict":true},"stepId":"pw:api@174","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@752"}
{"type":"log","callId":"call@752","time":97296.875,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('//*[contains(text(),\"Filters\")]')"}
{"type":"log","callId":"call@752","time":97300.537,"message":"  locator resolved to <span>Filters</span>"}
{"type":"log","callId":"call@752","time":97301.202,"message":"attempting click action"}
{"type":"log","callId":"call@752","time":97301.483,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@752","time":97324.932,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@752","time":97324.945,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@752","time":97325.372,"message":"  done scrolling"}
{"type":"log","callId":"call@752","time":97343.422,"message":"  performing click action"}
{"type":"log","callId":"call@752","time":97382.43,"message":"  click action done"}
{"type":"log","callId":"call@752","time":97382.444,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@752","time":97383.431,"message":"  navigations have finished"}
{"type":"after","callId":"call@752","endTime":97383.547,"point":{"x":1207.21,"y":319},"afterSnapshot":"after@call@752"}
{"type":"after","callId":"call@748","endTime":97612.368,"afterSnapshot":"after@call@748"}
{"type":"before","callId":"call@760","startTime":97613.837,"apiName":"page.waitForTimeout","class":"Frame","method":"waitForTimeout","params":{"timeout":2000},"stepId":"pw:api@175","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@760"}
{"type":"after","callId":"call@760","endTime":99626.551,"afterSnapshot":"after@call@760"}
{"type":"before","callId":"call@762","startTime":99635.301,"apiName":"locator.click","class":"Frame","method":"click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div[data-pendo-campaignform-agents-filters-ringgroupsmulti-select]","strict":true},"stepId":"pw:api@176","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@762"}
{"type":"log","callId":"call@762","time":99641.49,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div[data-pendo-campaignform-agents-filters-ringgroupsmulti-select]')"}
{"type":"log","callId":"call@762","time":99644.306,"message":"  locator resolved to <div tabindex=\"0\" class=\"co-dropdown\" id=\"multi-dropdown-label\" data-pendo-campaignform-agents-filters-ringgroupsmulti-select=\"true\">…</div>"}
{"type":"log","callId":"call@762","time":99644.766,"message":"attempting click action"}
{"type":"log","callId":"call@762","time":99644.973,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@762","time":99674.366,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@762","time":99674.375,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@762","time":99674.672,"message":"  done scrolling"}
{"type":"log","callId":"call@762","time":99684.846,"message":"  performing click action"}
{"type":"log","callId":"call@762","time":99696.938,"message":"  click action done"}
{"type":"log","callId":"call@762","time":99696.951,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@762","time":99697.126,"message":"  navigations have finished"}
{"type":"after","callId":"call@762","endTime":99697.216,"point":{"x":1055,"y":172},"afterSnapshot":"after@call@762"}
{"type":"before","callId":"call@764","startTime":99806.76,"apiName":"page.waitForResponse","class":"Page","method":"waitForEventInfo","params":{"info":{"waitId":"72231bc4ad87aa7a512426c8a4836865","phase":"before","event":"response"}},"stepId":"pw:api@177","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@764"}
{"type":"before","callId":"call@768","startTime":99808.392,"apiName":"locator.fill","class":"Frame","method":"fill","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div[data-pendo-campaignform-agents-filters-ringgroupsmulti-select] >> input[type=\"search\"]","strict":true,"value":"agents"},"stepId":"pw:api@178","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@768"}
{"type":"log","callId":"call@768","time":99821.498,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div[data-pendo-campaignform-agents-filters-ringgroupsmulti-select]').locator('input[type=\"search\"]')"}
{"type":"log","callId":"call@768","time":99827.37,"message":"  locator resolved to <input value=\"\" type=\"search\" placeholder=\"\"/>"}
{"type":"log","callId":"call@768","time":99828.054,"message":"  fill(\"agents\")"}
{"type":"log","callId":"call@768","time":99828.064,"message":"attempting fill action"}
{"type":"log","callId":"call@768","time":99837.31,"message":"  waiting for element to be visible, enabled and editable"}
{"type":"after","callId":"call@768","endTime":99840.73,"afterSnapshot":"after@call@768"}
{"type":"after","callId":"call@764","endTime":100356.537,"afterSnapshot":"after@call@764"}
{"type":"before","callId":"call@776","startTime":100357.911,"apiName":"response.json","class":"Response","method":"body","params":{},"stepId":"pw:api@179","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@776"}
{"type":"before","callId":"call@778","startTime":100358.159,"apiName":"locator.click","class":"Frame","method":"click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div[data-pendo-campaignform-agents-filters-ringgroupsmulti-select] >> div.co-dropdown__menu ul.co-list >> //*[text()=\"agents\"]","strict":true},"stepId":"pw:api@180","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@778"}
{"type":"after","callId":"call@776","endTime":100359.637,"result":{"binary":"<Buffer>"},"afterSnapshot":"after@call@776"}
{"type":"log","callId":"call@778","time":100372.781,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div[data-pendo-campaignform-agents-filters-ringgroupsmulti-select]').locator('div.co-dropdown__menu ul.co-list').locator('//*[text()=\"agents\"]')"}
{"type":"log","callId":"call@778","time":100378.75,"message":"  locator resolved to <label for=\"multi-dropdown-label-0\">agents</label>"}
{"type":"log","callId":"call@778","time":100379.318,"message":"attempting click action"}
{"type":"log","callId":"call@778","time":100379.665,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@778","time":100408.297,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@778","time":100408.308,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@778","time":100410.01,"message":"  done scrolling"}
{"type":"log","callId":"call@778","time":100428.165,"message":"  performing click action"}
{"type":"log","callId":"call@778","time":100444.79,"message":"  click action done"}
{"type":"log","callId":"call@778","time":100444.799,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@778","time":100445.061,"message":"  navigations have finished"}
{"type":"after","callId":"call@778","endTime":100445.148,"point":{"x":1069,"y":313.79},"afterSnapshot":"after@call@778"}
{"type":"before","callId":"call@780","startTime":100555.471,"apiName":"page.waitForRequest","class":"Page","method":"waitForEventInfo","params":{"info":{"waitId":"5fa7d72fb146e40f1b1c7187103b1404","phase":"before","event":"request"}},"stepId":"pw:api@181","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@780"}
{"type":"before","callId":"call@784","startTime":100557.055,"apiName":"locator.click","class":"Frame","method":"click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> button[data-testid=\"applyButton\"]","strict":true},"stepId":"pw:api@182","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@784"}
{"type":"log","callId":"call@784","time":100570.232,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div#panels-layout-content-id').locator('button[data-testid=\"applyButton\"]')"}
{"type":"log","callId":"call@784","time":100575.485,"message":"  locator resolved to <button data-co-name=\"Button\" data-co-version=\"1.4.0\" data-co-project=\"cobalt\" data-testid=\"applyButton\" data-pendo-campaignform-agents-filters-apply-button=\"true\" class=\"react-button_1-4-0_co-button react-button_1-4-0_co-button--primary react-button_1-4-0_co-button--medium\">…</button>"}
{"type":"log","callId":"call@784","time":100576.676,"message":"attempting click action"}
{"type":"log","callId":"call@784","time":100576.934,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@784","time":100608.581,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@784","time":100608.592,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@784","time":100610.095,"message":"  done scrolling"}
{"type":"log","callId":"call@784","time":100622.77,"message":"  performing click action"}
{"type":"log","callId":"call@784","time":100669.141,"message":"  click action done"}
{"type":"log","callId":"call@784","time":100669.159,"message":"  waiting for scheduled navigations to finish"}
{"type":"after","callId":"call@780","endTime":100672.246,"afterSnapshot":"after@call@780"}
{"type":"log","callId":"call@784","time":100673.485,"message":"  navigations have finished"}
{"type":"after","callId":"call@784","endTime":100673.563,"point":{"x":1210.42,"y":688},"afterSnapshot":"after@call@784"}
{"type":"before","callId":"call@792","startTime":100784.218,"apiName":"locator.click","class":"Frame","method":"click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-toolbar+div.co-toolbar >> button>span.co--truncate","strict":true},"stepId":"pw:api@184","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@792"}
{"type":"log","callId":"call@792","time":100792.015,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div.co-toolbar+div.co-toolbar').locator('button>span.co--truncate')"}
{"type":"log","callId":"call@792","time":100796.963,"message":"  locator resolved to <span class=\"co--truncate\">Clear all</span>"}
{"type":"log","callId":"call@792","time":100797.438,"message":"attempting click action"}
{"type":"log","callId":"call@792","time":100797.666,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@792","time":100882.907,"message":"  element is not stable"}
{"type":"log","callId":"call@792","time":100882.921,"message":"retrying click action"}
{"type":"log","callId":"call@792","time":100885.76,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@792","time":100907.936,"message":"element was detached from the DOM, retrying"}
{"type":"log","callId":"call@792","time":100912.309,"message":"  locator resolved to <span class=\"co--truncate\">Clear all</span>"}
{"type":"log","callId":"call@792","time":100912.826,"message":"attempting click action"}
{"type":"log","callId":"call@792","time":100913.058,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@792","time":100941.242,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@792","time":100941.252,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@792","time":100941.797,"message":"  done scrolling"}
{"type":"log","callId":"call@792","time":100963.075,"message":"  <div tabindex=\"-1\" id=\"panels-layout-content-id\" class=\"co-panels-layout__panel co-panels-layout__panel--over co-panels-layout__panel--exiting co--large\"></div> intercepts pointer events"}
{"type":"log","callId":"call@792","time":100963.085,"message":"retrying click action"}
{"type":"log","callId":"call@792","time":100963.571,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@792","time":100991.328,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@792","time":100991.34,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@792","time":100992.145,"message":"  done scrolling"}
{"type":"log","callId":"call@792","time":101005.516,"message":"  <div class=\"co-panels-layout__overlay\"></div> intercepts pointer events"}
{"type":"log","callId":"call@792","time":101005.523,"message":"retrying click action"}
{"type":"log","callId":"call@792","time":101005.525,"message":"  waiting 20ms"}
{"type":"log","callId":"call@792","time":101026.851,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@792","time":101058.155,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@792","time":101058.169,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@792","time":101059.118,"message":"  done scrolling"}
{"type":"log","callId":"call@792","time":101074.729,"message":"  <div class=\"co-panels-layout__overlay\"></div> intercepts pointer events"}
{"type":"log","callId":"call@792","time":101074.741,"message":"retrying click action"}
{"type":"log","callId":"call@792","time":101074.745,"message":"  waiting 100ms"}
{"type":"log","callId":"call@792","time":101177.102,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@792","time":101207.786,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@792","time":101207.798,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@792","time":101208.985,"message":"  done scrolling"}
{"type":"log","callId":"call@792","time":101225.768,"message":"  performing click action"}
{"type":"log","callId":"call@792","time":101259.731,"message":"  click action done"}
{"type":"log","callId":"call@792","time":101259.746,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@792","time":101260.867,"message":"  navigations have finished"}
{"type":"after","callId":"call@792","endTime":101260.989,"point":{"x":618.16,"y":80},"afterSnapshot":"after@call@792"}
{"type":"before","callId":"call@794","startTime":101373.001,"apiName":"page.waitForResponse","class":"Page","method":"waitForEventInfo","params":{"info":{"waitId":"1fd17b6132f78f00669d28915e53b17f","phase":"before","event":"response"}},"stepId":"pw:api@185","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@794"}
{"type":"before","callId":"call@798","startTime":101375.536,"apiName":"locator.click","class":"Frame","method":"click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> //*[contains(text(),\"Filters\")]","strict":true},"stepId":"pw:api@186","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@798"}
{"type":"log","callId":"call@798","time":101388.327,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('//*[contains(text(),\"Filters\")]')"}
{"type":"log","callId":"call@798","time":101393.46,"message":"  locator resolved to <span>Filters</span>"}
{"type":"log","callId":"call@798","time":101393.996,"message":"attempting click action"}
{"type":"log","callId":"call@798","time":101394.225,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@798","time":101464.679,"message":"  element is not stable"}
{"type":"log","callId":"call@798","time":101464.694,"message":"retrying click action"}
{"type":"log","callId":"call@798","time":101467.492,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@798","time":101491.588,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@798","time":101491.602,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@798","time":101492.706,"message":"  done scrolling"}
{"type":"log","callId":"call@798","time":101506.058,"message":"  performing click action"}
{"type":"log","callId":"call@798","time":101538.083,"message":"  click action done"}
{"type":"log","callId":"call@798","time":101538.096,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@798","time":101538.832,"message":"  navigations have finished"}
{"type":"after","callId":"call@798","endTime":101538.927,"point":{"x":1231.21,"y":319},"afterSnapshot":"after@call@798"}
{"type":"after","callId":"call@794","endTime":101737.393,"afterSnapshot":"after@call@794"}
{"type":"before","callId":"call@806","startTime":101739.694,"apiName":"page.waitForTimeout","class":"Frame","method":"waitForTimeout","params":{"timeout":2000},"stepId":"pw:api@187","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@806"}
{"type":"after","callId":"call@806","endTime":103756.63,"afterSnapshot":"after@call@806"}
{"type":"before","callId":"call@808","startTime":103765.846,"apiName":"locator.click","class":"Frame","method":"click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div[data-pendo-campaignform-agents-filters-ringgroupsmulti-select]","strict":true},"stepId":"pw:api@188","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@808"}
{"type":"log","callId":"call@808","time":103772.114,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div[data-pendo-campaignform-agents-filters-ringgroupsmulti-select]')"}
{"type":"log","callId":"call@808","time":103776.073,"message":"  locator resolved to <div tabindex=\"0\" class=\"co-dropdown\" id=\"multi-dropdown-label\" data-pendo-campaignform-agents-filters-ringgroupsmulti-select=\"true\">…</div>"}
{"type":"log","callId":"call@808","time":103776.62,"message":"attempting click action"}
{"type":"log","callId":"call@808","time":103776.865,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@808","time":103807.618,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@808","time":103807.631,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@808","time":103807.987,"message":"  done scrolling"}
{"type":"log","callId":"call@808","time":103818.763,"message":"  performing click action"}
{"type":"log","callId":"call@808","time":103829.94,"message":"  click action done"}
{"type":"log","callId":"call@808","time":103829.949,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@808","time":103830.105,"message":"  navigations have finished"}
{"type":"after","callId":"call@808","endTime":103830.187,"point":{"x":1055,"y":172},"afterSnapshot":"after@call@808"}
{"type":"before","callId":"call@810","startTime":103939.866,"apiName":"page.waitForResponse","class":"Page","method":"waitForEventInfo","params":{"info":{"waitId":"b92e7d408f68acdabdb93e41cdc88eaf","phase":"before","event":"response"}},"stepId":"pw:api@189","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@810"}
{"type":"before","callId":"call@814","startTime":103941.564,"apiName":"locator.fill","class":"Frame","method":"fill","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div[data-pendo-campaignform-agents-filters-ringgroupsmulti-select] >> input[type=\"search\"]","strict":true,"value":"agents"},"stepId":"pw:api@190","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@814"}
{"type":"log","callId":"call@814","time":103954.847,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div[data-pendo-campaignform-agents-filters-ringgroupsmulti-select]').locator('input[type=\"search\"]')"}
{"type":"log","callId":"call@814","time":103959.826,"message":"  locator resolved to <input value=\"\" type=\"search\" placeholder=\"\"/>"}
{"type":"log","callId":"call@814","time":103960.36,"message":"  fill(\"agents\")"}
{"type":"log","callId":"call@814","time":103960.367,"message":"attempting fill action"}
{"type":"log","callId":"call@814","time":103968.21,"message":"  waiting for element to be visible, enabled and editable"}
{"type":"after","callId":"call@814","endTime":103972.012,"afterSnapshot":"after@call@814"}
{"type":"after","callId":"call@810","endTime":104469.546,"afterSnapshot":"after@call@810"}
{"type":"before","callId":"call@822","startTime":104471.145,"apiName":"response.json","class":"Response","method":"body","params":{},"stepId":"pw:api@191","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@822"}
{"type":"before","callId":"call@824","startTime":104471.44,"apiName":"locator.click","class":"Frame","method":"click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div[data-pendo-campaignform-agents-filters-ringgroupsmulti-select] >> div.co-dropdown__menu ul.co-list >> //*[text()=\"agents\"]","strict":true},"stepId":"pw:api@192","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@824"}
{"type":"after","callId":"call@822","endTime":104472.743,"result":{"binary":"<Buffer>"},"afterSnapshot":"after@call@822"}
{"type":"log","callId":"call@824","time":104484.607,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div[data-pendo-campaignform-agents-filters-ringgroupsmulti-select]').locator('div.co-dropdown__menu ul.co-list').locator('//*[text()=\"agents\"]')"}
{"type":"log","callId":"call@824","time":104489.945,"message":"  locator resolved to <label for=\"multi-dropdown-label-0\">agents</label>"}
{"type":"log","callId":"call@824","time":104490.588,"message":"attempting click action"}
{"type":"log","callId":"call@824","time":104490.871,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@824","time":104510.087,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@824","time":104510.099,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@824","time":104511.283,"message":"  done scrolling"}
{"type":"log","callId":"call@824","time":104526.407,"message":"  performing click action"}
{"type":"log","callId":"call@824","time":104539.904,"message":"  click action done"}
{"type":"log","callId":"call@824","time":104539.915,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@824","time":104540.275,"message":"  navigations have finished"}
{"type":"after","callId":"call@824","endTime":104540.393,"point":{"x":1069,"y":313.79},"afterSnapshot":"after@call@824"}
{"type":"before","callId":"call@826","startTime":104651.542,"apiName":"page.waitForResponse","class":"Page","method":"waitForEventInfo","params":{"info":{"waitId":"8f131d3c7c97c961c1d59fac19399bc6","phase":"before","event":"response"}},"stepId":"pw:api@193","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@826"}
{"type":"before","callId":"call@830","startTime":104653.105,"apiName":"locator.fill","class":"Frame","method":"fill","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div[data-pendo-campaignform-agents-filters-ringgroupsmulti-select] >> input[type=\"search\"]","strict":true,"value":"test"},"stepId":"pw:api@194","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@830"}
{"type":"log","callId":"call@830","time":104665.278,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div[data-pendo-campaignform-agents-filters-ringgroupsmulti-select]').locator('input[type=\"search\"]')"}
{"type":"log","callId":"call@830","time":104669.174,"message":"  locator resolved to <input type=\"search\" placeholder=\"\" value=\"agents\"/>"}
{"type":"log","callId":"call@830","time":104669.911,"message":"  fill(\"test\")"}
{"type":"log","callId":"call@830","time":104669.92,"message":"attempting fill action"}
{"type":"log","callId":"call@830","time":104677.607,"message":"  waiting for element to be visible, enabled and editable"}
{"type":"after","callId":"call@830","endTime":104684.193,"afterSnapshot":"after@call@830"}
{"type":"after","callId":"call@826","endTime":105113.4,"afterSnapshot":"after@call@826"}
{"type":"before","callId":"call@838","startTime":105115.117,"apiName":"response.json","class":"Response","method":"body","params":{},"stepId":"pw:api@195","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@838"}
{"type":"before","callId":"call@840","startTime":105115.36,"apiName":"locator.click","class":"Frame","method":"click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div[data-pendo-campaignform-agents-filters-ringgroupsmulti-select] >> div.co-dropdown__menu ul.co-list >> //*[text()=\"test\"]","strict":true},"stepId":"pw:api@196","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@840"}
{"type":"after","callId":"call@838","endTime":105116.833,"result":{"binary":"<Buffer>"},"afterSnapshot":"after@call@838"}
{"type":"log","callId":"call@840","time":105128.187,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div[data-pendo-campaignform-agents-filters-ringgroupsmulti-select]').locator('div.co-dropdown__menu ul.co-list').locator('//*[text()=\"test\"]')"}
{"type":"log","callId":"call@840","time":105132.158,"message":"  locator resolved to <label for=\"multi-dropdown-label-0\">test</label>"}
{"type":"log","callId":"call@840","time":105132.879,"message":"attempting click action"}
{"type":"log","callId":"call@840","time":105133.171,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@840","time":105157.885,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@840","time":105157.894,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@840","time":105158.516,"message":"  done scrolling"}
{"type":"log","callId":"call@840","time":105171.645,"message":"  performing click action"}
{"type":"log","callId":"call@840","time":105185.291,"message":"  click action done"}
{"type":"log","callId":"call@840","time":105185.303,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@840","time":105185.624,"message":"  navigations have finished"}
{"type":"after","callId":"call@840","endTime":105185.729,"point":{"x":1069,"y":313.79},"afterSnapshot":"after@call@840"}
{"type":"before","callId":"call@842","startTime":105296.316,"apiName":"page.waitForRequest","class":"Page","method":"waitForEventInfo","params":{"info":{"waitId":"ba78d458325cc4a9ef552fffbbab62e3","phase":"before","event":"request"}},"stepId":"pw:api@197","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@842"}
{"type":"before","callId":"call@846","startTime":105297.99,"apiName":"locator.click","class":"Frame","method":"click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> button[data-testid=\"applyButton\"]","strict":true},"stepId":"pw:api@198","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@846"}
{"type":"log","callId":"call@846","time":105309.003,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div#panels-layout-content-id').locator('button[data-testid=\"applyButton\"]')"}
{"type":"log","callId":"call@846","time":105312.475,"message":"  locator resolved to <button data-co-name=\"Button\" data-co-version=\"1.4.0\" data-co-project=\"cobalt\" data-testid=\"applyButton\" data-pendo-campaignform-agents-filters-apply-button=\"true\" class=\"react-button_1-4-0_co-button react-button_1-4-0_co-button--primary react-button_1-4-0_co-button--medium\">…</button>"}
{"type":"log","callId":"call@846","time":105313.183,"message":"attempting click action"}
{"type":"log","callId":"call@846","time":105313.426,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@846","time":105341.163,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@846","time":105341.173,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@846","time":105342.072,"message":"  done scrolling"}
{"type":"log","callId":"call@846","time":105355.727,"message":"  performing click action"}
{"type":"log","callId":"call@846","time":105401.097,"message":"  click action done"}
{"type":"log","callId":"call@846","time":105401.111,"message":"  waiting for scheduled navigations to finish"}
{"type":"after","callId":"call@842","endTime":105404.527,"afterSnapshot":"after@call@842"}
{"type":"log","callId":"call@846","time":105406.708,"message":"  navigations have finished"}
{"type":"after","callId":"call@846","endTime":105406.794,"point":{"x":1210.42,"y":688},"afterSnapshot":"after@call@846"}
{"type":"before","callId":"call@854","startTime":105518.46,"apiName":"locator.click","class":"Frame","method":"click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-toolbar+div.co-toolbar >> button>span.co--truncate","strict":true},"stepId":"pw:api@201","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@854"}
{"type":"log","callId":"call@854","time":105525.691,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div.co-toolbar+div.co-toolbar').locator('button>span.co--truncate')"}
{"type":"log","callId":"call@854","time":105530.469,"message":"  locator resolved to <span class=\"co--truncate\">Clear all</span>"}
{"type":"log","callId":"call@854","time":105531.123,"message":"attempting click action"}
{"type":"log","callId":"call@854","time":105531.445,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@854","time":105558.083,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@854","time":105558.097,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@854","time":105571.683,"message":"element was detached from the DOM, retrying"}
{"type":"log","callId":"call@854","time":105578.256,"message":"  locator resolved to <span class=\"co--truncate\">Clear all</span>"}
{"type":"log","callId":"call@854","time":105578.956,"message":"attempting click action"}
{"type":"log","callId":"call@854","time":105579.195,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@854","time":105607.903,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@854","time":105607.914,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@854","time":105608.329,"message":"  done scrolling"}
{"type":"log","callId":"call@854","time":105620.788,"message":"  <div tabindex=\"-1\" id=\"panels-layout-content-id\" class=\"co-panels-layout__panel co-panels-layout__panel--over co-panels-layout__panel--exiting co--large\"></div> intercepts pointer events"}
{"type":"log","callId":"call@854","time":105620.797,"message":"retrying click action"}
{"type":"log","callId":"call@854","time":105621.172,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@854","time":105640.914,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@854","time":105640.927,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@854","time":105641.604,"message":"  done scrolling"}
{"type":"log","callId":"call@854","time":105654.65,"message":"  <div tabindex=\"-1\" id=\"panels-layout-content-id\" class=\"co-panels-layout__panel co-panels-layout__panel--over co-panels-layout__panel--exiting co--large\"></div> intercepts pointer events"}
{"type":"log","callId":"call@854","time":105654.658,"message":"retrying click action"}
{"type":"log","callId":"call@854","time":105654.66,"message":"  waiting 20ms"}
{"type":"log","callId":"call@854","time":105676.216,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@854","time":105707.522,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@854","time":105707.535,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@854","time":105708.475,"message":"  done scrolling"}
{"type":"log","callId":"call@854","time":105719.395,"message":"  <div class=\"co-panels-layout__overlay\"></div> intercepts pointer events"}
{"type":"log","callId":"call@854","time":105719.403,"message":"retrying click action"}
{"type":"log","callId":"call@854","time":105719.405,"message":"  waiting 100ms"}
{"type":"log","callId":"call@854","time":105821.647,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@854","time":105840.943,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@854","time":105840.958,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@854","time":105841.642,"message":"  done scrolling"}
{"type":"log","callId":"call@854","time":105852.409,"message":"  <div class=\"co-panels-layout__overlay\"></div> intercepts pointer events"}
{"type":"log","callId":"call@854","time":105852.417,"message":"retrying click action"}
{"type":"log","callId":"call@854","time":105852.42,"message":"  waiting 100ms"}
{"type":"log","callId":"call@854","time":105953.416,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@854","time":105974.695,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@854","time":105974.711,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@854","time":105975.484,"message":"  done scrolling"}
{"type":"log","callId":"call@854","time":105988.554,"message":"  performing click action"}
{"type":"log","callId":"call@854","time":106018.428,"message":"  click action done"}
{"type":"log","callId":"call@854","time":106018.441,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@854","time":106019.937,"message":"  navigations have finished"}
{"type":"after","callId":"call@854","endTime":106020.045,"point":{"x":621.16,"y":371},"afterSnapshot":"after@call@854"}
{"type":"before","callId":"call@856","startTime":106129.581,"apiName":"page.waitForResponse","class":"Page","method":"waitForEventInfo","params":{"info":{"waitId":"ee10906d97ef320ff63b727ec22a2869","phase":"before","event":"response"}},"stepId":"pw:api@202","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@856"}
{"type":"before","callId":"call@860","startTime":106131.356,"apiName":"locator.click","class":"Frame","method":"click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> //*[contains(text(),\"Filters\")]","strict":true},"stepId":"pw:api@203","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@860"}
{"type":"log","callId":"call@860","time":106141.26,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('//*[contains(text(),\"Filters\")]')"}
{"type":"log","callId":"call@860","time":106145.223,"message":"  locator resolved to <span>Filters</span>"}
{"type":"log","callId":"call@860","time":106145.703,"message":"attempting click action"}
{"type":"log","callId":"call@860","time":106145.928,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@860","time":106220.838,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@860","time":106220.851,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@860","time":106222.895,"message":"  done scrolling"}
{"type":"log","callId":"call@860","time":106239.293,"message":"  performing click action"}
{"type":"log","callId":"call@860","time":106275.319,"message":"  click action done"}
{"type":"log","callId":"call@860","time":106275.333,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@860","time":106276.249,"message":"  navigations have finished"}
{"type":"after","callId":"call@860","endTime":106276.338,"point":{"x":634.21,"y":319},"afterSnapshot":"after@call@860"}
{"type":"after","callId":"call@856","endTime":106471.253,"afterSnapshot":"after@call@856"}
{"type":"before","callId":"call@868","startTime":106472.697,"apiName":"page.waitForTimeout","class":"Frame","method":"waitForTimeout","params":{"timeout":2000},"stepId":"pw:api@204","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@868"}
{"type":"after","callId":"call@868","endTime":108489.802,"afterSnapshot":"after@call@868"}
{"type":"before","callId":"call@870","startTime":108499.461,"apiName":"locator.click","class":"Frame","method":"click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div[data-pendo-campaignform-agents-filters-ringgroupsmulti-select]","strict":true},"stepId":"pw:api@205","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@870"}
{"type":"log","callId":"call@870","time":108505.916,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div[data-pendo-campaignform-agents-filters-ringgroupsmulti-select]')"}
{"type":"log","callId":"call@870","time":108509.539,"message":"  locator resolved to <div tabindex=\"0\" class=\"co-dropdown\" id=\"multi-dropdown-label\" data-pendo-campaignform-agents-filters-ringgroupsmulti-select=\"true\">…</div>"}
{"type":"log","callId":"call@870","time":108510.075,"message":"attempting click action"}
{"type":"log","callId":"call@870","time":108510.326,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@870","time":108540.923,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@870","time":108540.934,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@870","time":108541.294,"message":"  done scrolling"}
{"type":"log","callId":"call@870","time":108553.711,"message":"  performing click action"}
{"type":"log","callId":"call@870","time":108565.692,"message":"  click action done"}
{"type":"log","callId":"call@870","time":108565.706,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@870","time":108565.842,"message":"  navigations have finished"}
{"type":"after","callId":"call@870","endTime":108565.938,"point":{"x":1055,"y":172},"afterSnapshot":"after@call@870"}
{"type":"before","callId":"call@872","startTime":108677.164,"apiName":"locator.click","class":"Frame","method":"click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div[data-pendo-campaignform-agents-filters-ringgroupsmulti-select] >> div.co-dropdown__menu ul.co-list >> //*[text()=\"Select All\"]","strict":true},"stepId":"pw:api@206","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@872"}
{"type":"log","callId":"call@872","time":108685.515,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div[data-pendo-campaignform-agents-filters-ringgroupsmulti-select]').locator('div.co-dropdown__menu ul.co-list').locator('//*[text()=\"Select All\"]')"}
{"type":"log","callId":"call@872","time":108690.261,"message":"  locator resolved to <label for=\"multi-dropdown-label-all\">Select All</label>"}
{"type":"log","callId":"call@872","time":108692.566,"message":"attempting click action"}
{"type":"log","callId":"call@872","time":108692.893,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@872","time":108724.633,"message":"  element is not stable"}
{"type":"log","callId":"call@872","time":108724.645,"message":"retrying click action"}
{"type":"log","callId":"call@872","time":108725.944,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@872","time":108758.046,"message":"  element is not stable"}
{"type":"log","callId":"call@872","time":108758.058,"message":"retrying click action"}
{"type":"log","callId":"call@872","time":108758.061,"message":"  waiting 20ms"}
{"type":"log","callId":"call@872","time":108781.257,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@872","time":108807.781,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@872","time":108807.797,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@872","time":108808.557,"message":"  done scrolling"}
{"type":"log","callId":"call@872","time":108821.327,"message":"  performing click action"}
{"type":"log","callId":"call@872","time":108842.504,"message":"  click action done"}
{"type":"log","callId":"call@872","time":108842.515,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@872","time":108842.725,"message":"  navigations have finished"}
{"type":"after","callId":"call@872","endTime":108842.803,"point":{"x":1069,"y":260.79},"afterSnapshot":"after@call@872"}
{"type":"before","callId":"call@874","startTime":108952.194,"apiName":"locator.click","class":"Frame","method":"click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> button[data-testid=\"applyButton\"]","strict":true},"stepId":"pw:api@207","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@874"}
{"type":"log","callId":"call@874","time":108959.312,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div#panels-layout-content-id').locator('button[data-testid=\"applyButton\"]')"}
{"type":"log","callId":"call@874","time":108963.266,"message":"  locator resolved to <button data-co-name=\"Button\" data-co-version=\"1.4.0\" data-co-project=\"cobalt\" data-testid=\"applyButton\" data-pendo-campaignform-agents-filters-apply-button=\"true\" class=\"react-button_1-4-0_co-button react-button_1-4-0_co-button--primary react-button_1-4-0_co-button--medium\">…</button>"}
{"type":"log","callId":"call@874","time":108963.747,"message":"attempting click action"}
{"type":"log","callId":"call@874","time":108963.965,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@874","time":108991.534,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@874","time":108991.546,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@874","time":108992.786,"message":"  done scrolling"}
{"type":"log","callId":"call@874","time":109007.812,"message":"  performing click action"}
{"type":"log","callId":"call@874","time":109061.402,"message":"  click action done"}
{"type":"log","callId":"call@874","time":109061.415,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@874","time":109062.848,"message":"  navigations have finished"}
{"type":"after","callId":"call@874","endTime":109062.925,"point":{"x":1210.42,"y":688},"afterSnapshot":"after@call@874"}
{"type":"before","callId":"call@876","startTime":109172.979,"apiName":"locator.click","class":"Frame","method":"click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-toolbar+div.co-toolbar >> button>span.co--truncate","strict":true},"stepId":"pw:api@208","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@876"}
{"type":"log","callId":"call@876","time":109180.674,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div.co-toolbar+div.co-toolbar').locator('button>span.co--truncate')"}
{"type":"log","callId":"call@876","time":109185.125,"message":"  locator resolved to <span class=\"co--truncate\">Clear all</span>"}
{"type":"log","callId":"call@876","time":109185.613,"message":"attempting click action"}
{"type":"log","callId":"call@876","time":109185.836,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@876","time":109207.765,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@876","time":109207.775,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@876","time":109208.466,"message":"  done scrolling"}
{"type":"log","callId":"call@876","time":109237.808,"message":"element was detached from the DOM, retrying"}
{"type":"log","callId":"call@876","time":109242.19,"message":"  locator resolved to <span class=\"co--truncate\">Clear all</span>"}
{"type":"log","callId":"call@876","time":109242.782,"message":"attempting click action"}
{"type":"log","callId":"call@876","time":109243.018,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@876","time":109274.422,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@876","time":109274.436,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@876","time":109274.823,"message":"  done scrolling"}
{"type":"log","callId":"call@876","time":109287.841,"message":"  <div tabindex=\"-1\" id=\"panels-layout-content-id\" class=\"co-panels-layout__panel co-panels-layout__panel--over co-panels-layout__panel--exiting co--large\"></div> intercepts pointer events"}
{"type":"log","callId":"call@876","time":109287.85,"message":"retrying click action"}
{"type":"log","callId":"call@876","time":109288.562,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@876","time":109307.619,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@876","time":109307.634,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@876","time":109308.258,"message":"  done scrolling"}
{"type":"log","callId":"call@876","time":109320.065,"message":"  <div tabindex=\"-1\" id=\"panels-layout-content-id\" class=\"co-panels-layout__panel co-panels-layout__panel--over co-panels-layout__panel--exiting co--large\"></div> intercepts pointer events"}
{"type":"log","callId":"call@876","time":109320.075,"message":"retrying click action"}
{"type":"log","callId":"call@876","time":109320.077,"message":"  waiting 20ms"}
{"type":"log","callId":"call@876","time":109342.063,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@876","time":109374.373,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@876","time":109374.387,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@876","time":109375.3,"message":"  done scrolling"}
{"type":"log","callId":"call@876","time":109387.839,"message":"  <div class=\"co-panels-layout__overlay\"></div> intercepts pointer events"}
{"type":"log","callId":"call@876","time":109387.845,"message":"retrying click action"}
{"type":"log","callId":"call@876","time":109387.848,"message":"  waiting 100ms"}
{"type":"log","callId":"call@876","time":109489.134,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@876","time":109507.394,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@876","time":109507.413,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@876","time":109508.083,"message":"  done scrolling"}
{"type":"log","callId":"call@876","time":109519.669,"message":"  <div class=\"co-panels-layout__overlay\"></div> intercepts pointer events"}
{"type":"log","callId":"call@876","time":109519.677,"message":"retrying click action"}
{"type":"log","callId":"call@876","time":109519.68,"message":"  waiting 100ms"}
{"type":"log","callId":"call@876","time":109620.828,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@876","time":109640.916,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@876","time":109640.928,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@876","time":109641.452,"message":"  done scrolling"}
{"type":"log","callId":"call@876","time":109652.491,"message":"  performing click action"}
{"type":"log","callId":"call@876","time":109681.28,"message":"  click action done"}
{"type":"log","callId":"call@876","time":109681.292,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@876","time":109682.504,"message":"  navigations have finished"}
{"type":"after","callId":"call@876","endTime":109682.608,"point":{"x":621.16,"y":304},"afterSnapshot":"after@call@876"}
{"type":"before","callId":"call@878","startTime":109792.291,"apiName":"locator.click","class":"Frame","method":"click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> //*[contains(text(),\"Filters\")]","strict":true},"stepId":"pw:api@209","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@878"}
{"type":"log","callId":"call@878","time":109798.335,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('//*[contains(text(),\"Filters\")]')"}
{"type":"log","callId":"call@878","time":109802.437,"message":"  locator resolved to <span>Filters</span>"}
{"type":"log","callId":"call@878","time":109802.995,"message":"attempting click action"}
{"type":"log","callId":"call@878","time":109803.348,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@878","time":109824.383,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@878","time":109824.397,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@878","time":109825.168,"message":"  done scrolling"}
{"type":"log","callId":"call@878","time":109892.511,"message":"  performing click action"}
{"type":"log","callId":"call@878","time":109928.961,"message":"  click action done"}
{"type":"log","callId":"call@878","time":109928.973,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@878","time":109929.786,"message":"  navigations have finished"}
{"type":"after","callId":"call@878","endTime":109929.875,"point":{"x":634.21,"y":252},"afterSnapshot":"after@call@878"}
{"type":"before","callId":"call@880","startTime":110041.316,"apiName":"locator.click","class":"Frame","method":"click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> button[data-testid=\"cancelButton\"]","strict":true},"stepId":"pw:api@210","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@880"}
{"type":"log","callId":"call@880","time":110051.999,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div#panels-layout-content-id').locator('button[data-testid=\"cancelButton\"]')"}
{"type":"log","callId":"call@880","time":110056.186,"message":"  locator resolved to <button data-co-name=\"Button\" data-co-version=\"1.4.0\" data-co-project=\"cobalt\" data-testid=\"cancelButton\" data-pendo-campaignform-agents-filters-cancel-button=\"true\" class=\"react-button_1-4-0_co-button react-button_1-4-0_co-button--secondary react-button_1-4-0_co-button--medium\">Cancel⇆</button>"}
{"type":"log","callId":"call@880","time":110056.821,"message":"attempting click action"}
{"type":"log","callId":"call@880","time":110062.795,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@880","time":110094.002,"message":"  element is not stable"}
{"type":"log","callId":"call@880","time":110094.016,"message":"retrying click action"}
{"type":"log","callId":"call@880","time":110095.401,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@880","time":110124.583,"message":"  element is not stable"}
{"type":"log","callId":"call@880","time":110124.595,"message":"retrying click action"}
{"type":"log","callId":"call@880","time":110124.598,"message":"  waiting 20ms"}
{"type":"log","callId":"call@880","time":110148.462,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@880","time":110174.441,"message":"  element is not stable"}
{"type":"log","callId":"call@880","time":110174.455,"message":"retrying click action"}
{"type":"log","callId":"call@880","time":110174.458,"message":"  waiting 100ms"}
{"type":"log","callId":"call@880","time":110279.565,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@880","time":110309.53,"message":"  element is not stable"}
{"type":"log","callId":"call@880","time":110309.545,"message":"retrying click action"}
{"type":"log","callId":"call@880","time":110309.548,"message":"  waiting 100ms"}
{"type":"log","callId":"call@880","time":110411.551,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@880","time":110440.773,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@880","time":110440.785,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@880","time":110441.238,"message":"  done scrolling"}
{"type":"log","callId":"call@880","time":110454.657,"message":"  performing click action"}
{"type":"log","callId":"call@880","time":110487.382,"message":"  click action done"}
{"type":"log","callId":"call@880","time":110487.395,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@880","time":110487.594,"message":"  navigations have finished"}
{"type":"after","callId":"call@880","endTime":110487.68,"point":{"x":1113.38,"y":688},"afterSnapshot":"after@call@880"}
{"type":"before","callId":"call@882","startTime":110597.336,"apiName":"expect.toBeHidden","class":"Frame","method":"expect","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div[data-pendo-campaignform-agents-filters-ringgroupsmulti-select]","expression":"to.be.hidden","expectedValue":{"value":{"v":"undefined"},"handles":[]},"isNot":false,"timeout":6000},"stepId":"expect@211","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@882"}
{"type":"log","callId":"call@882","time":110605.723,"message":"expect.toBeHidden with timeout 6000ms"}
{"type":"log","callId":"call@882","time":110605.731,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div[data-pendo-campaignform-agents-filters-ringgroupsmulti-select]')"}
{"type":"after","callId":"call@882","endTime":110611.302,"result":{"matches":true},"afterSnapshot":"after@call@882"}
{"type":"before","callId":"call@884","startTime":110618.958,"apiName":"page.screenshot","class":"Page","method":"screenshot","params":{},"stepId":"pw:api@212","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@884"}
{"type":"log","callId":"call@884","time":110627.85,"message":"taking page screenshot"}
{"type":"log","callId":"call@884","time":110631.655,"message":"waiting for fonts to load..."}
{"type":"log","callId":"call@884","time":110632.031,"message":"fonts loaded"}
{"type":"after","callId":"call@884","endTime":110719.148,"result":{"binary":"<Buffer>"},"afterSnapshot":"after@call@884"}
{"type":"before","callId":"call@886","startTime":110732.326,"apiName":"page.waitForResponse","class":"Page","method":"waitForEventInfo","params":{"info":{"waitId":"adbbd69879ba21d4ecc651cfb64dbbe6","phase":"before","event":"response"}},"stepId":"pw:api@216","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@886"}
{"type":"before","callId":"call@890","startTime":110734.794,"apiName":"locator.fill","class":"Frame","method":"fill","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> input[data-co-name=\"Input\"]","strict":true,"value":"Yuxiao Huang"},"stepId":"pw:api@217","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@890"}
{"type":"log","callId":"call@890","time":110745.431,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('input[data-co-name=\"Input\"]')"}
{"type":"log","callId":"call@890","time":110749.403,"message":"  locator resolved to <input value=\"\" type=\"text\" data-co-name=\"Input\" data-co-version=\"1.3.8\" data-co-project=\"cobalt\" placeholder=\"Search by agent name\" class=\"react-input_1-3-8_co-input react-input_1-3-8_co-input--unstyled react-input_1-3-8_co-input--small react-input_1-3-8_co-input--align-left\"/>"}
{"type":"log","callId":"call@890","time":110749.908,"message":"  fill(\"Yuxiao Huang\")"}
{"type":"log","callId":"call@890","time":110749.917,"message":"attempting fill action"}
{"type":"log","callId":"call@890","time":110756.371,"message":"  waiting for element to be visible, enabled and editable"}
{"type":"after","callId":"call@890","endTime":110765.596,"afterSnapshot":"after@call@890"}
{"type":"after","callId":"call@886","endTime":111260.751,"afterSnapshot":"after@call@886"}
{"type":"before","callId":"call@898","startTime":111262.243,"apiName":"response.json","class":"Response","method":"body","params":{},"stepId":"pw:api@218","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@898"}
{"type":"after","callId":"call@898","endTime":111262.657,"result":{"binary":"<Buffer>"},"afterSnapshot":"after@call@898"}
{"type":"before","callId":"call@900","startTime":111264.29,"apiName":"page.waitForTimeout","class":"Frame","method":"waitForTimeout","params":{"timeout":2000},"stepId":"pw:api@219","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@900"}
{"type":"after","callId":"call@900","endTime":113274.454,"afterSnapshot":"after@call@900"}
{"type":"before","callId":"call@902","startTime":113286.345,"apiName":"locator.waitFor","class":"Frame","method":"waitForSelector","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> table >> nth=0","strict":true,"timeout":60000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@220","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@902"}
{"type":"log","callId":"call@902","time":113295.793,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('table').first() to be visible"}
{"type":"log","callId":"call@902","time":113299.79,"message":"  locator resolved to visible <table class=\"co-table co-table--selectable co-table--sortable\">…</table>"}
{"type":"after","callId":"call@902","endTime":113299.837,"result":{},"afterSnapshot":"after@call@902"}
{"type":"before","callId":"call@904","startTime":113330.053,"apiName":"locator.waitFor","class":"Frame","method":"waitForSelector","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> table >> thead >> nth=0","strict":true,"timeout":60000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@221","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@904"}
{"type":"log","callId":"call@904","time":113338.286,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('table').locator('thead').first() to be visible"}
{"type":"log","callId":"call@904","time":113343.173,"message":"  locator resolved to visible <thead class=\"co-table__head\">…</thead>"}
{"type":"after","callId":"call@904","endTime":113343.218,"result":{},"afterSnapshot":"after@call@904"}
{"type":"before","callId":"call@906","startTime":113351.258,"apiName":"locator.waitFor","class":"Frame","method":"waitForSelector","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> table >> //tbody/tr[1] >> //td[1] >> nth=0","strict":true,"timeout":60000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@222","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@906"}
{"type":"log","callId":"call@906","time":113360.159,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('table').locator('//tbody/tr[1]').locator('//td[1]').first() to be visible"}
{"type":"log","callId":"call@906","time":113363.93,"message":"  locator resolved to visible <td class=\"co-table--5 co-table__cell\">…</td>"}
{"type":"after","callId":"call@906","endTime":113363.973,"result":{},"afterSnapshot":"after@call@906"}
{"type":"before","callId":"call@908","startTime":113373.142,"apiName":"locator.click","class":"Frame","method":"click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> table >> //tbody/tr[1] >> //td[1]","strict":true},"stepId":"pw:api@223","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@908"}
{"type":"log","callId":"call@908","time":113382.097,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('table').locator('//tbody/tr[1]').locator('//td[1]')"}
{"type":"log","callId":"call@908","time":113430.328,"message":"  locator resolved to <td class=\"co-table--5 co-table__cell\">…</td>"}
{"type":"log","callId":"call@908","time":113431.078,"message":"attempting click action"}
{"type":"log","callId":"call@908","time":113431.954,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@908","time":113457.692,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@908","time":113457.703,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@908","time":113458.099,"message":"  done scrolling"}
{"type":"log","callId":"call@908","time":113471.487,"message":"  performing click action"}
{"type":"log","callId":"call@908","time":113496.84,"message":"  click action done"}
{"type":"log","callId":"call@908","time":113496.852,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@908","time":113496.987,"message":"  navigations have finished"}
{"type":"after","callId":"call@908","endTime":113497.075,"point":{"x":294,"y":417.75},"afterSnapshot":"after@call@908"}
{"type":"before","callId":"call@910","startTime":113605.791,"apiName":"page.waitForTimeout","class":"Frame","method":"waitForTimeout","params":{"timeout":1000},"stepId":"pw:api@224","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@910"}
{"type":"after","callId":"call@910","endTime":114612.405,"afterSnapshot":"after@call@910"}
{"type":"before","callId":"call@912","startTime":114621.009,"apiName":"page.waitForResponse","class":"Page","method":"waitForEventInfo","params":{"info":{"waitId":"1562d41fc9f7e8c8887c0e720f35efd7","phase":"before","event":"response"}},"stepId":"pw:api@225","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@912"}
{"type":"before","callId":"call@916","startTime":114622.843,"apiName":"locator.fill","class":"Frame","method":"fill","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> input[data-co-name=\"Input\"]","strict":true,"value":"Damon"},"stepId":"pw:api@226","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@916"}
{"type":"log","callId":"call@916","time":114632.044,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('input[data-co-name=\"Input\"]')"}
{"type":"log","callId":"call@916","time":114635.002,"message":"  locator resolved to <input type=\"text\" data-co-name=\"Input\" value=\"Yuxiao Huang\" data-co-version=\"1.3.8\" data-co-project=\"cobalt\" placeholder=\"Search by agent name\" class=\"react-input_1-3-8_co-input react-input_1-3-8_co-input--unstyled react-input_1-3-8_co-input--small react-input_1-3-8_co-input--align-left\"/>"}
{"type":"log","callId":"call@916","time":114635.595,"message":"  fill(\"Damon\")"}
{"type":"log","callId":"call@916","time":114635.604,"message":"attempting fill action"}
{"type":"log","callId":"call@916","time":114640.708,"message":"  waiting for element to be visible, enabled and editable"}
{"type":"after","callId":"call@916","endTime":114644.804,"afterSnapshot":"after@call@916"}
{"type":"after","callId":"call@912","endTime":115131.169,"afterSnapshot":"after@call@912"}
{"type":"before","callId":"call@924","startTime":115133.664,"apiName":"response.json","class":"Response","method":"body","params":{},"stepId":"pw:api@227","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@924"}
{"type":"after","callId":"call@924","endTime":115134.291,"result":{"binary":"<Buffer>"},"afterSnapshot":"after@call@924"}
{"type":"before","callId":"call@926","startTime":115136.309,"apiName":"page.waitForTimeout","class":"Frame","method":"waitForTimeout","params":{"timeout":2000},"stepId":"pw:api@228","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@926"}
{"type":"after","callId":"call@926","endTime":117144.298,"afterSnapshot":"after@call@926"}
{"type":"before","callId":"call@928","startTime":117151.99,"apiName":"locator.waitFor","class":"Frame","method":"waitForSelector","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> table >> nth=0","strict":true,"timeout":60000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@229","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@928"}
{"type":"log","callId":"call@928","time":117157.789,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('table').first() to be visible"}
{"type":"log","callId":"call@928","time":117161.006,"message":"  locator resolved to visible <table class=\"co-table co-table--selectable co-table--sortable\">…</table>"}
{"type":"after","callId":"call@928","endTime":117161.048,"result":{},"afterSnapshot":"after@call@928"}
{"type":"before","callId":"call@930","startTime":117167.55,"apiName":"locator.waitFor","class":"Frame","method":"waitForSelector","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> table >> thead >> nth=0","strict":true,"timeout":60000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@230","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@930"}
{"type":"log","callId":"call@930","time":117172.881,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('table').locator('thead').first() to be visible"}
{"type":"log","callId":"call@930","time":117176.276,"message":"  locator resolved to visible <thead class=\"co-table__head\">…</thead>"}
{"type":"after","callId":"call@930","endTime":117176.311,"result":{},"afterSnapshot":"after@call@930"}
{"type":"before","callId":"call@932","startTime":117182.709,"apiName":"locator.waitFor","class":"Frame","method":"waitForSelector","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> table >> //tbody/tr[1] >> //td[1] >> nth=0","strict":true,"timeout":60000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@231","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@932"}
{"type":"log","callId":"call@932","time":117188.487,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('table').locator('//tbody/tr[1]').locator('//td[1]').first() to be visible"}
{"type":"log","callId":"call@932","time":117192.492,"message":"  locator resolved to visible <td class=\"co-table--5 co-table__cell\">…</td>"}
{"type":"after","callId":"call@932","endTime":117192.529,"result":{},"afterSnapshot":"after@call@932"}
{"type":"before","callId":"call@934","startTime":117198.803,"apiName":"locator.click","class":"Frame","method":"click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> table >> //tbody/tr[1] >> //td[1]","strict":true},"stepId":"pw:api@232","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@934"}
{"type":"log","callId":"call@934","time":117204.037,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('table').locator('//tbody/tr[1]').locator('//td[1]')"}
{"type":"log","callId":"call@934","time":117207.376,"message":"  locator resolved to <td class=\"co-table--5 co-table__cell\">…</td>"}
{"type":"log","callId":"call@934","time":117207.89,"message":"attempting click action"}
{"type":"log","callId":"call@934","time":117208.112,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@934","time":117240.54,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@934","time":117240.553,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@934","time":117240.805,"message":"  done scrolling"}
{"type":"log","callId":"call@934","time":117251.383,"message":"  performing click action"}
{"type":"log","callId":"call@934","time":117265.277,"message":"  click action done"}
{"type":"log","callId":"call@934","time":117265.285,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@934","time":117265.523,"message":"  navigations have finished"}
{"type":"after","callId":"call@934","endTime":117265.595,"point":{"x":294,"y":417.75},"afterSnapshot":"after@call@934"}
{"type":"before","callId":"call@936","startTime":117373.999,"apiName":"page.waitForTimeout","class":"Frame","method":"waitForTimeout","params":{"timeout":1000},"stepId":"pw:api@233","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@936"}
{"type":"after","callId":"call@936","endTime":118379.302,"afterSnapshot":"after@call@936"}
{"type":"before","callId":"call@938","startTime":118386.728,"apiName":"page.screenshot","class":"Page","method":"screenshot","params":{},"stepId":"pw:api@234","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@938"}
{"type":"log","callId":"call@938","time":118392.021,"message":"taking page screenshot"}
{"type":"log","callId":"call@938","time":118395.409,"message":"waiting for fonts to load..."}
{"type":"log","callId":"call@938","time":118395.726,"message":"fonts loaded"}
{"type":"after","callId":"call@938","endTime":118477.814,"result":{"binary":"<Buffer>"},"afterSnapshot":"after@call@938"}
{"type":"before","callId":"call@940","startTime":118487.52,"apiName":"locator.waitFor","class":"Frame","method":"waitForSelector","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> table >> nth=0","strict":true,"timeout":60000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@238","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@940"}
{"type":"log","callId":"call@940","time":118493.309,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('table').first() to be visible"}
{"type":"log","callId":"call@940","time":118496.305,"message":"  locator resolved to visible <table class=\"co-table co-table--selectable co-table--sortable\">…</table>"}
{"type":"after","callId":"call@940","endTime":118496.336,"result":{},"afterSnapshot":"after@call@940"}
{"type":"before","callId":"call@942","startTime":118502.628,"apiName":"locator.click","class":"Frame","method":"click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> table >> //tbody/tr[1] >> button","strict":true},"stepId":"pw:api@239","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@942"}
{"type":"log","callId":"call@942","time":118508.691,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('table').locator('//tbody/tr[1]').locator('button')"}
{"type":"log","callId":"call@942","time":118511.976,"message":"  locator resolved to <button type=\"button\" aria-label=\"error\" class=\"co-link co-button--icon co--invert co--small\">…</button>"}
{"type":"log","callId":"call@942","time":118512.583,"message":"attempting click action"}
{"type":"log","callId":"call@942","time":118512.816,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@942","time":118540.479,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@942","time":118540.49,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@942","time":118540.785,"message":"  done scrolling"}
{"type":"log","callId":"call@942","time":118550.38,"message":"  performing click action"}
{"type":"log","callId":"call@942","time":118585.154,"message":"  click action done"}
{"type":"log","callId":"call@942","time":118585.166,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@942","time":118586.515,"message":"  navigations have finished"}
{"type":"after","callId":"call@942","endTime":118586.63,"point":{"x":1240,"y":418},"afterSnapshot":"after@call@942"}
{"type":"before","callId":"call@944","startTime":118696.698,"apiName":"locator.click","class":"Frame","method":"click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> [data-testid=\"closeButton\"]","strict":true},"stepId":"pw:api@240","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@944"}
{"type":"log","callId":"call@944","time":118703.282,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div#panels-layout-content-id').locator('[data-testid=\"closeButton\"]')"}
{"type":"log","callId":"call@944","time":118708.527,"message":"  locator resolved to <button data-co-name=\"Button\" data-co-version=\"1.4.0\" data-co-project=\"cobalt\" data-testid=\"closeButton\" class=\"react-button_1-4-0_co-button react-button_1-4-0_co-button--secondary react-button_1-4-0_co-button--medium react-button_1-4-0_co-button--transparent\">…</button>"}
{"type":"log","callId":"call@944","time":118709.19,"message":"attempting click action"}
{"type":"log","callId":"call@944","time":118709.444,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@944","time":118741.223,"message":"  element is not stable"}
{"type":"log","callId":"call@944","time":118741.238,"message":"retrying click action"}
{"type":"log","callId":"call@944","time":118742.363,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@944","time":118774.141,"message":"  element is not stable"}
{"type":"log","callId":"call@944","time":118774.152,"message":"retrying click action"}
{"type":"log","callId":"call@944","time":118774.154,"message":"  waiting 20ms"}
{"type":"log","callId":"call@944","time":118795.956,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@944","time":118833.586,"message":"  element is not stable"}
{"type":"log","callId":"call@944","time":118833.6,"message":"retrying click action"}
{"type":"log","callId":"call@944","time":118833.602,"message":"  waiting 100ms"}
{"type":"log","callId":"call@944","time":118936.14,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@944","time":118958.434,"message":"  element is not stable"}
{"type":"log","callId":"call@944","time":118958.446,"message":"retrying click action"}
{"type":"log","callId":"call@944","time":118958.449,"message":"  waiting 100ms"}
{"type":"log","callId":"call@944","time":119060.12,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@944","time":119090.801,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@944","time":119090.814,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@944","time":119091.58,"message":"  done scrolling"}
{"type":"log","callId":"call@944","time":119108.583,"message":"  performing click action"}
{"type":"log","callId":"call@944","time":119131.522,"message":"  click action done"}
{"type":"log","callId":"call@944","time":119131.533,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@944","time":119131.689,"message":"  navigations have finished"}
{"type":"after","callId":"call@944","endTime":119131.755,"point":{"x":1236,"y":84},"afterSnapshot":"after@call@944"}
{"type":"before","callId":"call@946","startTime":119240.333,"apiName":"locator.count","class":"Frame","method":"queryCount","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> table >> tbody>tr"},"stepId":"pw:api@241","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@946"}
{"type":"after","callId":"call@946","endTime":119243.962,"result":{"value":1},"afterSnapshot":"after@call@946"}
{"type":"before","callId":"call@948","startTime":119246.146,"apiName":"locator.click","class":"Frame","method":"click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> [data-testid=\"nextButton\"]","strict":true},"stepId":"pw:api@243","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@948"}
{"type":"log","callId":"call@948","time":119252.271,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('[data-testid=\"nextButton\"]')"}
{"type":"log","callId":"call@948","time":119256.616,"message":"  locator resolved to <button type=\"button\" data-testid=\"nextButton\" class=\"co-button co--primary\" data-campaign-header-nextbutton=\"true\">…</button>"}
{"type":"log","callId":"call@948","time":119258.005,"message":"attempting click action"}
{"type":"log","callId":"call@948","time":119258.23,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@948","time":119290.723,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@948","time":119290.733,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@948","time":119291.218,"message":"  done scrolling"}
{"type":"log","callId":"call@948","time":119302.549,"message":"  <div tabindex=\"-1\" id=\"panels-layout-content-id\" class=\"co-panels-layout__panel co-panels-layout__panel--over co-panels-layout__panel--exiting co--large\"></div> intercepts pointer events"}
{"type":"log","callId":"call@948","time":119302.581,"message":"retrying click action"}
{"type":"log","callId":"call@948","time":119303.029,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@948","time":119324.192,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@948","time":119324.207,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@948","time":119325.164,"message":"  done scrolling"}
{"type":"log","callId":"call@948","time":119337.842,"message":"  <div tabindex=\"-1\" id=\"panels-layout-content-id\" class=\"co-panels-layout__panel co-panels-layout__panel--over co-panels-layout__panel--exiting co--large\"></div> intercepts pointer events"}
{"type":"log","callId":"call@948","time":119337.852,"message":"retrying click action"}
{"type":"log","callId":"call@948","time":119337.854,"message":"  waiting 20ms"}
{"type":"log","callId":"call@948","time":119359.154,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@948","time":119390.834,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@948","time":119390.85,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@948","time":119391.757,"message":"  done scrolling"}
{"type":"log","callId":"call@948","time":119401.303,"message":"  <div tabindex=\"-1\" id=\"panels-layout-content-id\" class=\"co-panels-layout__panel co-panels-layout__panel--over co-panels-layout__panel--exiting co--large\"></div> intercepts pointer events"}
{"type":"log","callId":"call@948","time":119401.311,"message":"retrying click action"}
{"type":"log","callId":"call@948","time":119401.313,"message":"  waiting 100ms"}
{"type":"log","callId":"call@948","time":119503.677,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@948","time":119524.062,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@948","time":119524.079,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@948","time":119524.878,"message":"  done scrolling"}
{"type":"log","callId":"call@948","time":119536.875,"message":"  <div class=\"co-panels-layout__overlay\"></div> intercepts pointer events"}
{"type":"log","callId":"call@948","time":119536.886,"message":"retrying click action"}
{"type":"log","callId":"call@948","time":119536.888,"message":"  waiting 100ms"}
{"type":"log","callId":"call@948","time":119638.186,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@948","time":119657.359,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@948","time":119657.376,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@948","time":119658.01,"message":"  done scrolling"}
{"type":"log","callId":"call@948","time":119668.845,"message":"  performing click action"}
{"type":"log","callId":"call@948","time":119691.832,"message":"  click action done"}
{"type":"log","callId":"call@948","time":119691.844,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@948","time":119692.794,"message":"  navigations have finished"}
{"type":"after","callId":"call@948","endTime":119692.868,"point":{"x":640.42,"y":84},"afterSnapshot":"after@call@948"}
{"type":"before","callId":"call@950","startTime":119805.055,"apiName":"expect.toBeVisible","class":"Frame","method":"expect","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> [data-testid=\"submitButton\"]","expression":"to.be.visible","expectedValue":{"value":{"v":"undefined"},"handles":[]},"isNot":false,"timeout":6000},"stepId":"expect@244","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@950"}
{"type":"log","callId":"call@950","time":119810.982,"message":"expect.toBeVisible with timeout 6000ms"}
{"type":"log","callId":"call@950","time":119810.991,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('[data-testid=\"submitButton\"]')"}
{"type":"log","callId":"call@950","time":119813.765,"message":"  locator resolved to <button type=\"button\" data-testid=\"submitButton\" class=\"co-button co--primary\" data-pendo-campaignform-finalbutton=\"create\">…</button>"}
{"type":"after","callId":"call@950","endTime":119813.842,"result":{"matches":true,"received":{"b":true}},"afterSnapshot":"after@call@950"}
{"type":"before","callId":"call@952","startTime":119821.179,"apiName":"page.screenshot","class":"Page","method":"screenshot","params":{},"stepId":"pw:api@245","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@952"}
{"type":"log","callId":"call@952","time":119827.531,"message":"taking page screenshot"}
{"type":"log","callId":"call@952","time":119830.468,"message":"waiting for fonts to load..."}
{"type":"log","callId":"call@952","time":119830.858,"message":"fonts loaded"}
{"type":"after","callId":"call@952","endTime":119914.01,"result":{"binary":"<Buffer>"},"afterSnapshot":"after@call@952"}
{"type":"before","callId":"call@954","startTime":119924.089,"apiName":"page.waitForResponse","class":"Page","method":"waitForEventInfo","params":{"info":{"waitId":"d1accc8f5e6717c4c392220fa1f7484d","phase":"before","event":"response"}},"stepId":"pw:api@249","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@954"}
{"type":"before","callId":"call@958","startTime":119925.641,"apiName":"locator.click","class":"Frame","method":"click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-section__content:has(h5:text(\"Record lists\")) button>span:text(\"Add record lists\")","strict":true},"stepId":"pw:api@250","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@958"}
{"type":"log","callId":"call@958","time":119937.112,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div.co-section__content:has(h5:text(\"Record lists\")) button>span:text(\"Add record lists\")')"}
{"type":"log","callId":"call@958","time":119942.639,"message":"  locator resolved to <span>Add record lists</span>"}
{"type":"log","callId":"call@958","time":119943.207,"message":"attempting click action"}
{"type":"log","callId":"call@958","time":119943.466,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@958","time":119973.836,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@958","time":119973.847,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@958","time":119974.36,"message":"  done scrolling"}
{"type":"log","callId":"call@958","time":119986.87,"message":"  performing click action"}
{"type":"log","callId":"call@958","time":120023.544,"message":"  click action done"}
{"type":"log","callId":"call@958","time":120023.555,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@958","time":120025.472,"message":"  navigations have finished"}
{"type":"after","callId":"call@954","endTime":120061.395,"afterSnapshot":"after@call@954"}
{"type":"after","callId":"call@958","endTime":120025.554,"point":{"x":372.28,"y":378},"afterSnapshot":"after@call@958"}
{"type":"before","callId":"call@966","startTime":120135.243,"apiName":"response.json","class":"Response","method":"body","params":{},"stepId":"pw:api@251","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@966"}
{"type":"after","callId":"call@966","endTime":120135.363,"result":{"binary":"<Buffer>"},"afterSnapshot":"after@call@966"}
{"type":"before","callId":"call@968","startTime":120136.701,"apiName":"locator.waitFor","class":"Frame","method":"waitForSelector","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> nth=0","strict":true,"timeout":60000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@252","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@968"}
{"type":"log","callId":"call@968","time":120144.8,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div#panels-layout-content-id').locator('table').first() to be visible"}
{"type":"log","callId":"call@968","time":120151.256,"message":"  locator resolved to visible <table class=\"co-table\">…</table>"}
{"type":"after","callId":"call@968","endTime":120151.305,"result":{},"afterSnapshot":"after@call@968"}
{"type":"before","callId":"call@970","startTime":120160.675,"apiName":"locator.waitFor","class":"Frame","method":"waitForSelector","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> nth=0","strict":true,"timeout":60000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@253","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@970"}
{"type":"log","callId":"call@970","time":120168.352,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div#panels-layout-content-id').locator('table').first() to be visible"}
{"type":"log","callId":"call@970","time":120171.902,"message":"  locator resolved to visible <table class=\"co-table\">…</table>"}
{"type":"after","callId":"call@970","endTime":120171.939,"result":{},"afterSnapshot":"after@call@970"}
{"type":"before","callId":"call@972","startTime":120179.462,"apiName":"locator.waitFor","class":"Frame","method":"waitForSelector","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> //tbody/tr[1]/td[1] >> div.co-placeholder--animated","strict":true,"timeout":10000,"state":"hidden","omitReturnValue":true},"stepId":"pw:api@254","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@972"}
{"type":"log","callId":"call@972","time":120193.305,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div#panels-layout-content-id').locator('table').locator('//tbody/tr[1]/td[1]').locator('div.co-placeholder--animated') to be hidden"}
{"type":"log","callId":"call@972","time":120202.948,"message":"  locator resolved to visible <div class=\"co-placeholder co-placeholder--animated\"></div>"}
{"type":"log","callId":"call@972","time":120206.432,"message":"  locator resolved to visible <div class=\"co-placeholder co-placeholder--animated\"></div>"}
{"type":"log","callId":"call@972","time":120229.602,"message":"  locator resolved to visible <div class=\"co-placeholder co-placeholder--animated\"></div>"}
{"type":"log","callId":"call@972","time":120282.433,"message":"  locator resolved to visible <div class=\"co-placeholder co-placeholder--animated\"></div>"}
{"type":"after","callId":"call@972","endTime":120415.814,"result":{},"afterSnapshot":"after@call@972"}
{"type":"before","callId":"call@974","startTime":120429.465,"apiName":"page.waitForTimeout","class":"Frame","method":"waitForTimeout","params":{"timeout":1000},"stepId":"pw:api@255","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@974"}
{"type":"after","callId":"call@974","endTime":121436.628,"afterSnapshot":"after@call@974"}
{"type":"before","callId":"call@976","startTime":121445.617,"apiName":"expect.toBeVisible","class":"Frame","method":"expect","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id","expression":"to.be.visible","expectedValue":{"value":{"v":"undefined"},"handles":[]},"isNot":false,"timeout":6000},"stepId":"expect@256","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@976"}
{"type":"log","callId":"call@976","time":121451.731,"message":"expect.toBeVisible with timeout 6000ms"}
{"type":"log","callId":"call@976","time":121451.737,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div#panels-layout-content-id')"}
{"type":"log","callId":"call@976","time":121454.253,"message":"  locator resolved to <div tabindex=\"-1\" id=\"panels-layout-content-id\" class=\"co-panels-layout__panel co-panels-layout__panel--over co--active co--large\">…</div>"}
{"type":"after","callId":"call@976","endTime":121454.302,"result":{"matches":true,"received":{"b":true}},"afterSnapshot":"after@call@976"}
{"type":"before","callId":"call@978","startTime":121461.79,"apiName":"locator.waitFor","class":"Frame","method":"waitForSelector","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> nth=0","strict":true,"timeout":60000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@257","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@978"}
{"type":"log","callId":"call@978","time":121467.747,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div#panels-layout-content-id').locator('table').first() to be visible"}
{"type":"log","callId":"call@978","time":121470.906,"message":"  locator resolved to visible <table class=\"co-table co-table--selectable\">…</table>"}
{"type":"after","callId":"call@978","endTime":121470.933,"result":{},"afterSnapshot":"after@call@978"}
{"type":"before","callId":"call@980","startTime":121477.947,"apiName":"locator.waitFor","class":"Frame","method":"waitForSelector","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> thead >> nth=0","strict":true,"timeout":60000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@258","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@980"}
{"type":"log","callId":"call@980","time":121483.725,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div#panels-layout-content-id').locator('table').locator('thead').first() to be visible"}
{"type":"log","callId":"call@980","time":121486.296,"message":"  locator resolved to visible <thead class=\"co-table__head\">…</thead>"}
{"type":"after","callId":"call@980","endTime":121486.325,"result":{},"afterSnapshot":"after@call@980"}
{"type":"before","callId":"call@982","startTime":121493.527,"apiName":"locator.waitFor","class":"Frame","method":"waitForSelector","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> //tbody/tr[1] >> //td[1] >> input >> nth=0","strict":true,"timeout":60000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@259","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@982"}
{"type":"log","callId":"call@982","time":121499.468,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div#panels-layout-content-id').locator('table').locator('//tbody/tr[1]').locator('//td[1]').locator('input').first() to be visible"}
{"type":"log","callId":"call@982","time":121503.166,"message":"  locator resolved to visible <input type=\"checkbox\" value=\"6fd5a56b-bc85-4fa1-9d61-830113f6bc84\" id=\"available-lists-checkbox-id-6fd5a56b-bc85-4fa1-9d61-830113f6bc84\" data-testid=\"available-lists-checkbox-id-6fd5a56b-bc85-4fa1-9d61-830113f6bc84\"/>"}
{"type":"after","callId":"call@982","endTime":121503.196,"result":{},"afterSnapshot":"after@call@982"}
{"type":"before","callId":"call@984","startTime":121510.373,"apiName":"locator.click","class":"Frame","method":"click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> //tbody/tr[1] >> //td[1] >> input","strict":true},"stepId":"pw:api@260","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@984"}
{"type":"log","callId":"call@984","time":121516.16,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div#panels-layout-content-id').locator('table').locator('//tbody/tr[1]').locator('//td[1]').locator('input')"}
{"type":"log","callId":"call@984","time":121518.849,"message":"  locator resolved to <input type=\"checkbox\" value=\"6fd5a56b-bc85-4fa1-9d61-830113f6bc84\" id=\"available-lists-checkbox-id-6fd5a56b-bc85-4fa1-9d61-830113f6bc84\" data-testid=\"available-lists-checkbox-id-6fd5a56b-bc85-4fa1-9d61-830113f6bc84\"/>"}
{"type":"log","callId":"call@984","time":121519.315,"message":"attempting click action"}
{"type":"log","callId":"call@984","time":121519.539,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@984","time":121523.344,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@984","time":121523.35,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@984","time":121523.583,"message":"  done scrolling"}
{"type":"log","callId":"call@984","time":121533.002,"message":"  performing click action"}
{"type":"log","callId":"call@984","time":121549.146,"message":"  click action done"}
{"type":"log","callId":"call@984","time":121549.156,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@984","time":121549.285,"message":"  navigations have finished"}
{"type":"after","callId":"call@984","endTime":121549.351,"point":{"x":714,"y":247},"afterSnapshot":"after@call@984"}
{"type":"before","callId":"call@986","startTime":121658.281,"apiName":"locator.waitFor","class":"Frame","method":"waitForSelector","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> nth=0","strict":true,"timeout":60000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@261","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@986"}
{"type":"log","callId":"call@986","time":121664.754,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div#panels-layout-content-id').locator('table').first() to be visible"}
{"type":"log","callId":"call@986","time":121667.881,"message":"  locator resolved to visible <table class=\"co-table co-table--selectable\">…</table>"}
{"type":"after","callId":"call@986","endTime":121667.917,"result":{},"afterSnapshot":"after@call@986"}
{"type":"before","callId":"call@988","startTime":121676.369,"apiName":"locator.waitFor","class":"Frame","method":"waitForSelector","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> thead >> nth=0","strict":true,"timeout":60000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@262","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@988"}
{"type":"log","callId":"call@988","time":121683.062,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div#panels-layout-content-id').locator('table').locator('thead').first() to be visible"}
{"type":"log","callId":"call@988","time":121686.981,"message":"  locator resolved to visible <thead class=\"co-table__head\">…</thead>"}
{"type":"after","callId":"call@988","endTime":121687.017,"result":{},"afterSnapshot":"after@call@988"}
{"type":"before","callId":"call@990","startTime":121694.913,"apiName":"locator.textContent","class":"Frame","method":"textContent","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> //tbody/tr[1] >> //td[2]","strict":true},"stepId":"pw:api@263","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@990"}
{"type":"log","callId":"call@990","time":121702.225,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div#panels-layout-content-id').locator('table').locator('//tbody/tr[1]').locator('//td[2]')"}
{"type":"log","callId":"call@990","time":121705.133,"message":"  locator resolved to <td class=\"co-table__cell co--truncate-cell\">…</td>"}
{"type":"after","callId":"call@990","endTime":121705.185,"result":{"value":"metoto_search_list_bwva"},"afterSnapshot":"after@call@990"}
{"type":"before","callId":"call@992","startTime":121716.027,"apiName":"locator.waitFor","class":"Frame","method":"waitForSelector","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> nth=0","strict":true,"timeout":60000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@264","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@992"}
{"type":"log","callId":"call@992","time":121722.871,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div#panels-layout-content-id').locator('table').first() to be visible"}
{"type":"log","callId":"call@992","time":121727.818,"message":"  locator resolved to visible <table class=\"co-table co-table--selectable\">…</table>"}
{"type":"after","callId":"call@992","endTime":121727.867,"result":{},"afterSnapshot":"after@call@992"}
{"type":"before","callId":"call@994","startTime":121735.468,"apiName":"locator.waitFor","class":"Frame","method":"waitForSelector","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> thead >> nth=0","strict":true,"timeout":60000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@265","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@994"}
{"type":"log","callId":"call@994","time":121743.071,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div#panels-layout-content-id').locator('table').locator('thead').first() to be visible"}
{"type":"log","callId":"call@994","time":121747.374,"message":"  locator resolved to visible <thead class=\"co-table__head\">…</thead>"}
{"type":"after","callId":"call@994","endTime":121747.418,"result":{},"afterSnapshot":"after@call@994"}
{"type":"before","callId":"call@996","startTime":121755.202,"apiName":"locator.waitFor","class":"Frame","method":"waitForSelector","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> //tbody/tr[2] >> //td[1] >> input >> nth=0","strict":true,"timeout":60000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@266","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@996"}
{"type":"log","callId":"call@996","time":121762.518,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div#panels-layout-content-id').locator('table').locator('//tbody/tr[2]').locator('//td[1]').locator('input').first() to be visible"}
{"type":"log","callId":"call@996","time":121766.548,"message":"  locator resolved to visible <input type=\"checkbox\" value=\"3c07ed57-d0f0-470a-91dc-595b7d95868a\" id=\"available-lists-checkbox-id-3c07ed57-d0f0-470a-91dc-595b7d95868a\" data-testid=\"available-lists-checkbox-id-3c07ed57-d0f0-470a-91dc-595b7d95868a\"/>"}
{"type":"after","callId":"call@996","endTime":121766.611,"result":{},"afterSnapshot":"after@call@996"}
{"type":"before","callId":"call@998","startTime":121774.651,"apiName":"locator.click","class":"Frame","method":"click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> //tbody/tr[2] >> //td[1] >> input","strict":true},"stepId":"pw:api@267","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@998"}
{"type":"log","callId":"call@998","time":121782.364,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div#panels-layout-content-id').locator('table').locator('//tbody/tr[2]').locator('//td[1]').locator('input')"}
{"type":"log","callId":"call@998","time":121785.722,"message":"  locator resolved to <input type=\"checkbox\" value=\"3c07ed57-d0f0-470a-91dc-595b7d95868a\" id=\"available-lists-checkbox-id-3c07ed57-d0f0-470a-91dc-595b7d95868a\" data-testid=\"available-lists-checkbox-id-3c07ed57-d0f0-470a-91dc-595b7d95868a\"/>"}
{"type":"log","callId":"call@998","time":121787.736,"message":"attempting click action"}
{"type":"log","callId":"call@998","time":121788.046,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@998","time":121807.581,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@998","time":121807.592,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@998","time":121808.28,"message":"  done scrolling"}
{"type":"log","callId":"call@998","time":121821.018,"message":"  performing click action"}
{"type":"log","callId":"call@998","time":121836.703,"message":"  click action done"}
{"type":"log","callId":"call@998","time":121836.716,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@998","time":121836.877,"message":"  navigations have finished"}
{"type":"after","callId":"call@998","endTime":121836.959,"point":{"x":714,"y":292},"afterSnapshot":"after@call@998"}
{"type":"before","callId":"call@1000","startTime":121947.452,"apiName":"locator.waitFor","class":"Frame","method":"waitForSelector","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> nth=0","strict":true,"timeout":60000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@268","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@1000"}
{"type":"log","callId":"call@1000","time":121953.84,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div#panels-layout-content-id').locator('table').first() to be visible"}
{"type":"log","callId":"call@1000","time":121958.35,"message":"  locator resolved to visible <table class=\"co-table co-table--selectable\">…</table>"}
{"type":"after","callId":"call@1000","endTime":121958.39,"result":{},"afterSnapshot":"after@call@1000"}
{"type":"before","callId":"call@1002","startTime":121965.853,"apiName":"locator.waitFor","class":"Frame","method":"waitForSelector","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> thead >> nth=0","strict":true,"timeout":60000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@269","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@1002"}
{"type":"log","callId":"call@1002","time":121972.14,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div#panels-layout-content-id').locator('table').locator('thead').first() to be visible"}
{"type":"log","callId":"call@1002","time":121976.676,"message":"  locator resolved to visible <thead class=\"co-table__head\">…</thead>"}
{"type":"after","callId":"call@1002","endTime":121976.722,"result":{},"afterSnapshot":"after@call@1002"}
{"type":"before","callId":"call@1004","startTime":121984.229,"apiName":"locator.textContent","class":"Frame","method":"textContent","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> //tbody/tr[2] >> //td[2]","strict":true},"stepId":"pw:api@270","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@1004"}
{"type":"log","callId":"call@1004","time":121991.606,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div#panels-layout-content-id').locator('table').locator('//tbody/tr[2]').locator('//td[2]')"}
{"type":"log","callId":"call@1004","time":121995.052,"message":"  locator resolved to <td class=\"co-table__cell co--truncate-cell\">…</td>"}
{"type":"after","callId":"call@1004","endTime":121995.101,"result":{"value":"metoto_records_list_r08p"},"afterSnapshot":"after@call@1004"}
{"type":"before","callId":"call@1006","startTime":122004.348,"apiName":"locator.waitFor","class":"Frame","method":"waitForSelector","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> nth=0","strict":true,"timeout":60000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@271","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@1006"}
{"type":"log","callId":"call@1006","time":122013.617,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div#panels-layout-content-id').locator('table').first() to be visible"}
{"type":"log","callId":"call@1006","time":122017.486,"message":"  locator resolved to visible <table class=\"co-table co-table--selectable\">…</table>"}
{"type":"after","callId":"call@1006","endTime":122017.536,"result":{},"afterSnapshot":"after@call@1006"}
{"type":"before","callId":"call@1008","startTime":122028.435,"apiName":"locator.waitFor","class":"Frame","method":"waitForSelector","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> thead >> nth=0","strict":true,"timeout":60000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@272","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@1008"}
{"type":"log","callId":"call@1008","time":122038.118,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div#panels-layout-content-id').locator('table').locator('thead').first() to be visible"}
{"type":"log","callId":"call@1008","time":122043.469,"message":"  locator resolved to visible <thead class=\"co-table__head\">…</thead>"}
{"type":"after","callId":"call@1008","endTime":122043.523,"result":{},"afterSnapshot":"after@call@1008"}
{"type":"before","callId":"call@1010","startTime":122052.201,"apiName":"locator.waitFor","class":"Frame","method":"waitForSelector","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> //tbody/tr[3] >> //td[1] >> input >> nth=0","strict":true,"timeout":60000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@273","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@1010"}
{"type":"log","callId":"call@1010","time":122059.791,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div#panels-layout-content-id').locator('table').locator('//tbody/tr[3]').locator('//td[1]').locator('input').first() to be visible"}
{"type":"log","callId":"call@1010","time":122063.694,"message":"  locator resolved to visible <input type=\"checkbox\" value=\"a886c62d-1852-4678-bfbe-179b367366f6\" id=\"available-lists-checkbox-id-a886c62d-1852-4678-bfbe-179b367366f6\" data-testid=\"available-lists-checkbox-id-a886c62d-1852-4678-bfbe-179b367366f6\"/>"}
{"type":"after","callId":"call@1010","endTime":122063.736,"result":{},"afterSnapshot":"after@call@1010"}
{"type":"before","callId":"call@1012","startTime":122071.009,"apiName":"locator.click","class":"Frame","method":"click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> //tbody/tr[3] >> //td[1] >> input","strict":true},"stepId":"pw:api@274","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@1012"}
{"type":"log","callId":"call@1012","time":122078.19,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div#panels-layout-content-id').locator('table').locator('//tbody/tr[3]').locator('//td[1]').locator('input')"}
{"type":"log","callId":"call@1012","time":122082.462,"message":"  locator resolved to <input type=\"checkbox\" value=\"a886c62d-1852-4678-bfbe-179b367366f6\" id=\"available-lists-checkbox-id-a886c62d-1852-4678-bfbe-179b367366f6\" data-testid=\"available-lists-checkbox-id-a886c62d-1852-4678-bfbe-179b367366f6\"/>"}
{"type":"log","callId":"call@1012","time":122083.121,"message":"attempting click action"}
{"type":"log","callId":"call@1012","time":122083.405,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1012","time":122107.409,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1012","time":122107.421,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1012","time":122108.03,"message":"  done scrolling"}
{"type":"log","callId":"call@1012","time":122119.538,"message":"  performing click action"}
{"type":"log","callId":"call@1012","time":122133.634,"message":"  click action done"}
{"type":"log","callId":"call@1012","time":122133.644,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@1012","time":122133.85,"message":"  navigations have finished"}
{"type":"after","callId":"call@1012","endTime":122133.965,"point":{"x":714,"y":337},"afterSnapshot":"after@call@1012"}
{"type":"before","callId":"call@1014","startTime":122242.928,"apiName":"locator.waitFor","class":"Frame","method":"waitForSelector","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> nth=0","strict":true,"timeout":60000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@275","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@1014"}
{"type":"log","callId":"call@1014","time":122249.125,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div#panels-layout-content-id').locator('table').first() to be visible"}
{"type":"log","callId":"call@1014","time":122252.156,"message":"  locator resolved to visible <table class=\"co-table co-table--selectable\">…</table>"}
{"type":"after","callId":"call@1014","endTime":122252.189,"result":{},"afterSnapshot":"after@call@1014"}
{"type":"before","callId":"call@1016","startTime":122260.719,"apiName":"locator.waitFor","class":"Frame","method":"waitForSelector","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> thead >> nth=0","strict":true,"timeout":60000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@276","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@1016"}
{"type":"log","callId":"call@1016","time":122267.097,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div#panels-layout-content-id').locator('table').locator('thead').first() to be visible"}
{"type":"log","callId":"call@1016","time":122269.931,"message":"  locator resolved to visible <thead class=\"co-table__head\">…</thead>"}
{"type":"after","callId":"call@1016","endTime":122269.963,"result":{},"afterSnapshot":"after@call@1016"}
{"type":"before","callId":"call@1018","startTime":122277.148,"apiName":"locator.textContent","class":"Frame","method":"textContent","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> //tbody/tr[3] >> //td[2]","strict":true},"stepId":"pw:api@277","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@1018"}
{"type":"log","callId":"call@1018","time":122283.416,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div#panels-layout-content-id').locator('table').locator('//tbody/tr[3]').locator('//td[2]')"}
{"type":"log","callId":"call@1018","time":122285.876,"message":"  locator resolved to <td class=\"co-table__cell co--truncate-cell\">…</td>"}
{"type":"after","callId":"call@1018","endTime":122285.913,"result":{"value":"metoto_search_list_2vw1"},"afterSnapshot":"after@call@1018"}
{"type":"before","callId":"call@1020","startTime":122293.287,"apiName":"locator.isVisible","class":"Frame","method":"isVisible","params":{"selector":"div[data-testid=\"toaster\"] div[data-co-name=\"Message\"]:nth-child(1) div[data-testid]","strict":true},"stepId":"pw:api@278","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@1020"}
{"type":"log","callId":"call@1020","time":122299.466,"message":"  checking visibility of locator('div[data-testid=\"toaster\"] div[data-co-name=\"Message\"]:nth-child(1) div[data-testid]')"}
{"type":"after","callId":"call@1020","endTime":122300.919,"result":{"value":false},"afterSnapshot":"after@call@1020"}
{"type":"before","callId":"call@1022","startTime":122308.201,"apiName":"locator.isVisible","class":"Frame","method":"isVisible","params":{"selector":"div[data-testid=\"toaster\"] div[data-co-name=\"Message\"]:nth-child(1) div[data-testid]","strict":true},"stepId":"pw:api@279","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@1022"}
{"type":"log","callId":"call@1022","time":122314.209,"message":"  checking visibility of locator('div[data-testid=\"toaster\"] div[data-co-name=\"Message\"]:nth-child(1) div[data-testid]')"}
{"type":"after","callId":"call@1022","endTime":122315.285,"result":{"value":false},"afterSnapshot":"after@call@1022"}
{"type":"before","callId":"call@1024","startTime":122322.351,"apiName":"locator.click","class":"Frame","method":"click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> button>*:text(\"Apply\")","strict":true},"stepId":"pw:api@280","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@1024"}
{"type":"log","callId":"call@1024","time":122329.642,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div#panels-layout-content-id').locator('button>*:text(\"Apply\")')"}
{"type":"log","callId":"call@1024","time":122333.252,"message":"  locator resolved to <p data-co-name=\"Text\" data-co-version=\"1.5.0\" data-co-project=\"cobalt\" class=\"react-typography_1-5-0_co-text\">Apply</p>"}
{"type":"log","callId":"call@1024","time":122333.755,"message":"attempting click action"}
{"type":"log","callId":"call@1024","time":122333.969,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1024","time":122357.507,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1024","time":122357.52,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1024","time":122358.23,"message":"  done scrolling"}
{"type":"log","callId":"call@1024","time":122372.402,"message":"  performing click action"}
{"type":"log","callId":"call@1024","time":122395.719,"message":"  click action done"}
{"type":"log","callId":"call@1024","time":122395.731,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@1024","time":122395.866,"message":"  navigations have finished"}
{"type":"after","callId":"call@1024","endTime":122395.933,"point":{"x":1220.42,"y":688},"afterSnapshot":"after@call@1024"}
{"type":"before","callId":"call@1026","startTime":122505.352,"apiName":"locator.count","class":"Frame","method":"queryCount","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-section__content:has(h5:text(\"Record lists\")) table >> tbody>tr"},"stepId":"pw:api@281","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@1026"}
{"type":"after","callId":"call@1026","endTime":122508.569,"result":{"value":3},"afterSnapshot":"after@call@1026"}
{"type":"before","callId":"call@1028","startTime":122509.749,"apiName":"locator.waitFor","class":"Frame","method":"waitForSelector","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-section__content:has(h5:text(\"Record lists\")) table >> nth=0","strict":true,"timeout":60000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@282","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@1028"}
{"type":"log","callId":"call@1028","time":122515.341,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div.co-section__content:has(h5:text(\"Record lists\")) table').first() to be visible"}
{"type":"log","callId":"call@1028","time":122519.189,"message":"  locator resolved to visible <table class=\"co-table\">…</table>"}
{"type":"after","callId":"call@1028","endTime":122519.229,"result":{},"afterSnapshot":"after@call@1028"}
{"type":"before","callId":"call@1030","startTime":122525.997,"apiName":"locator.waitFor","class":"Frame","method":"waitForSelector","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-section__content:has(h5:text(\"Record lists\")) table >> thead >> nth=0","strict":true,"timeout":60000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@283","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@1030"}
{"type":"log","callId":"call@1030","time":122531.202,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div.co-section__content:has(h5:text(\"Record lists\")) table').locator('thead').first() to be visible"}
{"type":"log","callId":"call@1030","time":122534.058,"message":"  locator resolved to visible <thead class=\"co-table__head\">…</thead>"}
{"type":"after","callId":"call@1030","endTime":122534.085,"result":{},"afterSnapshot":"after@call@1030"}
{"type":"before","callId":"call@1032","startTime":122540.598,"apiName":"locator.textContent","class":"Frame","method":"textContent","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-section__content:has(h5:text(\"Record lists\")) table >> //tbody/tr[1] >> //td[1]","strict":true},"stepId":"pw:api@284","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@1032"}
{"type":"log","callId":"call@1032","time":122545.793,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div.co-section__content:has(h5:text(\"Record lists\")) table').locator('//tbody/tr[1]').locator('//td[1]')"}
{"type":"log","callId":"call@1032","time":122547.99,"message":"  locator resolved to <td class=\"co-table__cell\">…</td>"}
{"type":"after","callId":"call@1032","endTime":122548.017,"result":{"value":"metoto_search_list_bwva"},"afterSnapshot":"after@call@1032"}
{"type":"before","callId":"call@1034","startTime":122555.355,"apiName":"locator.waitFor","class":"Frame","method":"waitForSelector","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-section__content:has(h5:text(\"Record lists\")) table >> nth=0","strict":true,"timeout":60000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@286","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@1034"}
{"type":"log","callId":"call@1034","time":122561.107,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div.co-section__content:has(h5:text(\"Record lists\")) table').first() to be visible"}
{"type":"log","callId":"call@1034","time":122563.681,"message":"  locator resolved to visible <table class=\"co-table\">…</table>"}
{"type":"after","callId":"call@1034","endTime":122563.708,"result":{},"afterSnapshot":"after@call@1034"}
{"type":"before","callId":"call@1036","startTime":122569.669,"apiName":"locator.waitFor","class":"Frame","method":"waitForSelector","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-section__content:has(h5:text(\"Record lists\")) table >> thead >> nth=0","strict":true,"timeout":60000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@287","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@1036"}
{"type":"log","callId":"call@1036","time":122576.061,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div.co-section__content:has(h5:text(\"Record lists\")) table').locator('thead').first() to be visible"}
{"type":"log","callId":"call@1036","time":122578.883,"message":"  locator resolved to visible <thead class=\"co-table__head\">…</thead>"}
{"type":"after","callId":"call@1036","endTime":122578.912,"result":{},"afterSnapshot":"after@call@1036"}
{"type":"before","callId":"call@1038","startTime":122585.162,"apiName":"locator.textContent","class":"Frame","method":"textContent","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-section__content:has(h5:text(\"Record lists\")) table >> //tbody/tr[2] >> //td[1]","strict":true},"stepId":"pw:api@288","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@1038"}
{"type":"log","callId":"call@1038","time":122592.282,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div.co-section__content:has(h5:text(\"Record lists\")) table').locator('//tbody/tr[2]').locator('//td[1]')"}
{"type":"log","callId":"call@1038","time":122594.97,"message":"  locator resolved to <td class=\"co-table__cell\">…</td>"}
{"type":"after","callId":"call@1038","endTime":122595.011,"result":{"value":"metoto_records_list_r08p"},"afterSnapshot":"after@call@1038"}
{"type":"before","callId":"call@1040","startTime":122602.247,"apiName":"locator.waitFor","class":"Frame","method":"waitForSelector","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-section__content:has(h5:text(\"Record lists\")) table >> nth=0","strict":true,"timeout":60000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@290","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@1040"}
{"type":"log","callId":"call@1040","time":122608.233,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div.co-section__content:has(h5:text(\"Record lists\")) table').first() to be visible"}
{"type":"log","callId":"call@1040","time":122610.976,"message":"  locator resolved to visible <table class=\"co-table\">…</table>"}
{"type":"after","callId":"call@1040","endTime":122611.007,"result":{},"afterSnapshot":"after@call@1040"}
{"type":"before","callId":"call@1042","startTime":122617.884,"apiName":"locator.waitFor","class":"Frame","method":"waitForSelector","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-section__content:has(h5:text(\"Record lists\")) table >> thead >> nth=0","strict":true,"timeout":60000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@291","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@1042"}
{"type":"log","callId":"call@1042","time":122624.048,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div.co-section__content:has(h5:text(\"Record lists\")) table').locator('thead').first() to be visible"}
{"type":"log","callId":"call@1042","time":122627.192,"message":"  locator resolved to visible <thead class=\"co-table__head\">…</thead>"}
{"type":"after","callId":"call@1042","endTime":122627.226,"result":{},"afterSnapshot":"after@call@1042"}
{"type":"before","callId":"call@1044","startTime":122633.698,"apiName":"locator.textContent","class":"Frame","method":"textContent","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-section__content:has(h5:text(\"Record lists\")) table >> //tbody/tr[3] >> //td[1]","strict":true},"stepId":"pw:api@292","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@1044"}
{"type":"log","callId":"call@1044","time":122640.011,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div.co-section__content:has(h5:text(\"Record lists\")) table').locator('//tbody/tr[3]').locator('//td[1]')"}
{"type":"log","callId":"call@1044","time":122643.584,"message":"  locator resolved to <td class=\"co-table__cell\">…</td>"}
{"type":"after","callId":"call@1044","endTime":122643.618,"result":{"value":"metoto_search_list_2vw1"},"afterSnapshot":"after@call@1044"}
{"type":"before","callId":"call@1046","startTime":122651.01,"apiName":"page.screenshot","class":"Page","method":"screenshot","params":{},"stepId":"pw:api@294","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@1046"}
{"type":"log","callId":"call@1046","time":122656.566,"message":"taking page screenshot"}
{"type":"log","callId":"call@1046","time":122659.168,"message":"waiting for fonts to load..."}
{"type":"log","callId":"call@1046","time":122659.624,"message":"fonts loaded"}
{"type":"after","callId":"call@1046","endTime":122747.586,"result":{"binary":"<Buffer>"},"afterSnapshot":"after@call@1046"}
{"type":"before","callId":"call@1048","startTime":122757.157,"apiName":"locator.waitFor","class":"Frame","method":"waitForSelector","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-section__content:has(h5:text(\"Record lists\")) table >> nth=0","strict":true,"timeout":60000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@298","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@1048"}
{"type":"log","callId":"call@1048","time":122763.162,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div.co-section__content:has(h5:text(\"Record lists\")) table').first() to be visible"}
{"type":"log","callId":"call@1048","time":122766.499,"message":"  locator resolved to visible <table class=\"co-table\">…</table>"}
{"type":"after","callId":"call@1048","endTime":122766.54,"result":{},"afterSnapshot":"after@call@1048"}
{"type":"before","callId":"call@1050","startTime":122773.149,"apiName":"locator.waitFor","class":"Frame","method":"waitForSelector","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-section__content:has(h5:text(\"Record lists\")) table >> thead >> nth=0","strict":true,"timeout":60000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@299","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@1050"}
{"type":"log","callId":"call@1050","time":122779.119,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div.co-section__content:has(h5:text(\"Record lists\")) table').locator('thead').first() to be visible"}
{"type":"log","callId":"call@1050","time":122783.58,"message":"  locator resolved to visible <thead class=\"co-table__head\">…</thead>"}
{"type":"after","callId":"call@1050","endTime":122783.618,"result":{},"afterSnapshot":"after@call@1050"}
{"type":"before","callId":"call@1052","startTime":122791.506,"apiName":"locator.textContent","class":"Frame","method":"textContent","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-section__content:has(h5:text(\"Record lists\")) table >> //tbody/tr[3] >> //td[1]","strict":true},"stepId":"pw:api@300","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@1052"}
{"type":"log","callId":"call@1052","time":122800.229,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div.co-section__content:has(h5:text(\"Record lists\")) table').locator('//tbody/tr[3]').locator('//td[1]')"}
{"type":"log","callId":"call@1052","time":122804.565,"message":"  locator resolved to <td class=\"co-table__cell\">…</td>"}
{"type":"after","callId":"call@1052","endTime":122804.601,"result":{"value":"metoto_search_list_2vw1"},"afterSnapshot":"after@call@1052"}
{"type":"before","callId":"call@1054","startTime":122811.414,"apiName":"locator.click","class":"Frame","method":"click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-section__content:has(h5:text(\"Record lists\")) table >> //tbody/tr[3] >> td>button[aria-label=\"trash\"]","strict":true},"stepId":"pw:api@301","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@1054"}
{"type":"log","callId":"call@1054","time":122819.628,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div.co-section__content:has(h5:text(\"Record lists\")) table').locator('//tbody/tr[3]').locator('td>button[aria-label=\"trash\"]')"}
{"type":"log","callId":"call@1054","time":122824.825,"message":"  locator resolved to <button type=\"button\" aria-label=\"trash\" class=\"co-link co-button--icon\" data-pendo-campaignform-lists-removebutton=\"campaignWizard:listsConfiguration.recordLists\">…</button>"}
{"type":"log","callId":"call@1054","time":122825.378,"message":"attempting click action"}
{"type":"log","callId":"call@1054","time":122825.841,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1054","time":122857.183,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1054","time":122857.194,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1054","time":122857.51,"message":"  done scrolling"}
{"type":"log","callId":"call@1054","time":122868.373,"message":"  <div class=\"co-panels-layout__overlay\"></div> intercepts pointer events"}
{"type":"log","callId":"call@1054","time":122868.381,"message":"retrying click action"}
{"type":"log","callId":"call@1054","time":122868.94,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1054","time":122890.97,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1054","time":122890.983,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1054","time":122891.593,"message":"  done scrolling"}
{"type":"log","callId":"call@1054","time":122902.519,"message":"  <div class=\"co-panels-layout__overlay\"></div> intercepts pointer events"}
{"type":"log","callId":"call@1054","time":122902.526,"message":"retrying click action"}
{"type":"log","callId":"call@1054","time":122902.529,"message":"  waiting 20ms"}
{"type":"log","callId":"call@1054","time":122924.671,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1054","time":122956.998,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1054","time":122957.01,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1054","time":122958.143,"message":"  done scrolling"}
{"type":"log","callId":"call@1054","time":122969.616,"message":"  performing click action"}
{"type":"log","callId":"call@1054","time":122987.862,"message":"  click action done"}
{"type":"log","callId":"call@1054","time":122987.874,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@1054","time":122988.324,"message":"  navigations have finished"}
{"type":"after","callId":"call@1054","endTime":122988.426,"point":{"x":728.98,"y":507},"afterSnapshot":"after@call@1054"}
{"type":"before","callId":"call@1056","startTime":123098.178,"apiName":"locator.click","class":"Frame","method":"click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-modal__dialog >> [data-pendo-campaignform-lists-removerlistbutton-confirm]","strict":true},"stepId":"pw:api@302","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@1056"}
{"type":"log","callId":"call@1056","time":123104.534,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div.co-modal__dialog').locator('[data-pendo-campaignform-lists-removerlistbutton-confirm]')"}
{"type":"log","callId":"call@1056","time":123109.773,"message":"  locator resolved to <button type=\"button\" class=\"co-button co--danger\" data-pendo-campaignform-lists-removerlistbutton-confirm=\"true\">…</button>"}
{"type":"log","callId":"call@1056","time":123110.321,"message":"attempting click action"}
{"type":"log","callId":"call@1056","time":123110.552,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1056","time":123140.604,"message":"  element is not stable"}
{"type":"log","callId":"call@1056","time":123140.614,"message":"retrying click action"}
{"type":"log","callId":"call@1056","time":123141.469,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1056","time":123173.868,"message":"  element is not stable"}
{"type":"log","callId":"call@1056","time":123173.882,"message":"retrying click action"}
{"type":"log","callId":"call@1056","time":123173.884,"message":"  waiting 20ms"}
{"type":"log","callId":"call@1056","time":123195.625,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1056","time":123223.88,"message":"  element is not stable"}
{"type":"log","callId":"call@1056","time":123223.893,"message":"retrying click action"}
{"type":"log","callId":"call@1056","time":123223.895,"message":"  waiting 100ms"}
{"type":"log","callId":"call@1056","time":123325.913,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1056","time":123357.535,"message":"  element is not stable"}
{"type":"log","callId":"call@1056","time":123357.547,"message":"retrying click action"}
{"type":"log","callId":"call@1056","time":123357.55,"message":"  waiting 100ms"}
{"type":"log","callId":"call@1056","time":123459.224,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1056","time":123490.519,"message":"  element is not stable"}
{"type":"log","callId":"call@1056","time":123490.533,"message":"retrying click action"}
{"type":"log","callId":"call@1056","time":123490.535,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1056","time":123992.513,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1056","time":124023.75,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1056","time":124023.764,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1056","time":124024.595,"message":"  done scrolling"}
{"type":"log","callId":"call@1056","time":124035.165,"message":"  performing click action"}
{"type":"log","callId":"call@1056","time":124051.065,"message":"  click action done"}
{"type":"log","callId":"call@1056","time":124051.081,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@1056","time":124051.267,"message":"  navigations have finished"}
{"type":"after","callId":"call@1056","endTime":124051.356,"point":{"x":816.45,"y":486},"afterSnapshot":"after@call@1056"}
{"type":"before","callId":"call@1058","startTime":124159.31,"apiName":"locator.waitFor","class":"Frame","method":"waitForSelector","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-section__content:has(h5:text(\"Record lists\")) table >> nth=0","strict":true,"timeout":60000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@303","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@1058"}
{"type":"log","callId":"call@1058","time":124164.946,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div.co-section__content:has(h5:text(\"Record lists\")) table').first() to be visible"}
{"type":"log","callId":"call@1058","time":124167.777,"message":"  locator resolved to visible <table class=\"co-table\">…</table>"}
{"type":"after","callId":"call@1058","endTime":124167.808,"result":{},"afterSnapshot":"after@call@1058"}
{"type":"before","callId":"call@1060","startTime":124174.217,"apiName":"locator.waitFor","class":"Frame","method":"waitForSelector","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-section__content:has(h5:text(\"Record lists\")) table >> thead >> nth=0","strict":true,"timeout":60000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@304","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@1060"}
{"type":"log","callId":"call@1060","time":124179.97,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div.co-section__content:has(h5:text(\"Record lists\")) table').locator('thead').first() to be visible"}
{"type":"log","callId":"call@1060","time":124183.241,"message":"  locator resolved to visible <thead class=\"co-table__head\">…</thead>"}
{"type":"after","callId":"call@1060","endTime":124183.278,"result":{},"afterSnapshot":"after@call@1060"}
{"type":"before","callId":"call@1062","startTime":124189.407,"apiName":"locator.textContent","class":"Frame","method":"textContent","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-section__content:has(h5:text(\"Record lists\")) table >> //tbody/tr[2] >> //td[1]","strict":true},"stepId":"pw:api@305","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@1062"}
{"type":"log","callId":"call@1062","time":124195.031,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div.co-section__content:has(h5:text(\"Record lists\")) table').locator('//tbody/tr[2]').locator('//td[1]')"}
{"type":"log","callId":"call@1062","time":124197.318,"message":"  locator resolved to <td class=\"co-table__cell\">…</td>"}
{"type":"after","callId":"call@1062","endTime":124197.346,"result":{"value":"metoto_records_list_r08p"},"afterSnapshot":"after@call@1062"}
{"type":"before","callId":"call@1064","startTime":124203.503,"apiName":"locator.click","class":"Frame","method":"click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-section__content:has(h5:text(\"Record lists\")) table >> //tbody/tr[2] >> td>button[aria-label=\"trash\"]","strict":true},"stepId":"pw:api@306","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@1064"}
{"type":"log","callId":"call@1064","time":124208.554,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div.co-section__content:has(h5:text(\"Record lists\")) table').locator('//tbody/tr[2]').locator('td>button[aria-label=\"trash\"]')"}
{"type":"log","callId":"call@1064","time":124211.206,"message":"  locator resolved to <button type=\"button\" aria-label=\"trash\" class=\"co-link co-button--icon\" data-pendo-campaignform-lists-removebutton=\"campaignWizard:listsConfiguration.recordLists\">…</button>"}
{"type":"log","callId":"call@1064","time":124211.665,"message":"attempting click action"}
{"type":"log","callId":"call@1064","time":124211.878,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1064","time":124223.24,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1064","time":124223.247,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1064","time":124223.48,"message":"  done scrolling"}
{"type":"log","callId":"call@1064","time":124232.406,"message":"  performing click action"}
{"type":"log","callId":"call@1064","time":124251.904,"message":"  click action done"}
{"type":"log","callId":"call@1064","time":124251.914,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@1064","time":124252.214,"message":"  navigations have finished"}
{"type":"after","callId":"call@1064","endTime":124252.283,"point":{"x":728.98,"y":507},"afterSnapshot":"after@call@1064"}
{"type":"before","callId":"call@1066","startTime":124360.156,"apiName":"locator.click","class":"Frame","method":"click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-modal__dialog >> [data-pendo-campaignform-lists-removerlistbutton-confirm]","strict":true},"stepId":"pw:api@307","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@1066"}
{"type":"log","callId":"call@1066","time":124365.967,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div.co-modal__dialog').locator('[data-pendo-campaignform-lists-removerlistbutton-confirm]')"}
{"type":"log","callId":"call@1066","time":124368.886,"message":"  locator resolved to <button type=\"button\" class=\"co-button co--danger\" data-pendo-campaignform-lists-removerlistbutton-confirm=\"true\">…</button>"}
{"type":"log","callId":"call@1066","time":124369.345,"message":"attempting click action"}
{"type":"log","callId":"call@1066","time":124369.563,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1066","time":124390.505,"message":"  element is not stable"}
{"type":"log","callId":"call@1066","time":124390.515,"message":"retrying click action"}
{"type":"log","callId":"call@1066","time":124391.502,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1066","time":124423.863,"message":"  element is not stable"}
{"type":"log","callId":"call@1066","time":124423.875,"message":"retrying click action"}
{"type":"log","callId":"call@1066","time":124423.877,"message":"  waiting 20ms"}
{"type":"log","callId":"call@1066","time":124445.473,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1066","time":124473.864,"message":"  element is not stable"}
{"type":"log","callId":"call@1066","time":124473.874,"message":"retrying click action"}
{"type":"log","callId":"call@1066","time":124473.876,"message":"  waiting 100ms"}
{"type":"log","callId":"call@1066","time":124576.602,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1066","time":124637.928,"message":"  element is not stable"}
{"type":"log","callId":"call@1066","time":124637.942,"message":"retrying click action"}
{"type":"log","callId":"call@1066","time":124637.944,"message":"  waiting 100ms"}
{"type":"log","callId":"call@1066","time":124739.334,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1066","time":124773.746,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1066","time":124773.761,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1066","time":124774.408,"message":"  done scrolling"}
{"type":"log","callId":"call@1066","time":124785.872,"message":"  performing click action"}
{"type":"log","callId":"call@1066","time":124800.071,"message":"  click action done"}
{"type":"log","callId":"call@1066","time":124800.086,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@1066","time":124800.242,"message":"  navigations have finished"}
{"type":"after","callId":"call@1066","endTime":124800.333,"point":{"x":816.45,"y":486},"afterSnapshot":"after@call@1066"}
{"type":"before","callId":"call@1068","startTime":124908.094,"apiName":"locator.waitFor","class":"Frame","method":"waitForSelector","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-section__content:has(h5:text(\"Record lists\")) table >> nth=0","strict":true,"timeout":60000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@308","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@1068"}
{"type":"log","callId":"call@1068","time":124913.363,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div.co-section__content:has(h5:text(\"Record lists\")) table').first() to be visible"}
{"type":"log","callId":"call@1068","time":124917.512,"message":"  locator resolved to visible <table class=\"co-table\">…</table>"}
{"type":"after","callId":"call@1068","endTime":124917.543,"result":{},"afterSnapshot":"after@call@1068"}
{"type":"before","callId":"call@1070","startTime":124924.098,"apiName":"locator.waitFor","class":"Frame","method":"waitForSelector","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-section__content:has(h5:text(\"Record lists\")) table >> thead >> nth=0","strict":true,"timeout":60000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@309","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@1070"}
{"type":"log","callId":"call@1070","time":124929.092,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div.co-section__content:has(h5:text(\"Record lists\")) table').locator('thead').first() to be visible"}
{"type":"log","callId":"call@1070","time":124931.827,"message":"  locator resolved to visible <thead class=\"co-table__head\">…</thead>"}
{"type":"after","callId":"call@1070","endTime":124931.854,"result":{},"afterSnapshot":"after@call@1070"}
{"type":"before","callId":"call@1072","startTime":124937.774,"apiName":"locator.textContent","class":"Frame","method":"textContent","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-section__content:has(h5:text(\"Record lists\")) table >> //tbody/tr[1] >> //td[1]","strict":true},"stepId":"pw:api@310","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@1072"}
{"type":"log","callId":"call@1072","time":124942.848,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div.co-section__content:has(h5:text(\"Record lists\")) table').locator('//tbody/tr[1]').locator('//td[1]')"}
{"type":"log","callId":"call@1072","time":124945.142,"message":"  locator resolved to <td class=\"co-table__cell\">…</td>"}
{"type":"after","callId":"call@1072","endTime":124945.17,"result":{"value":"metoto_search_list_bwva"},"afterSnapshot":"after@call@1072"}
{"type":"before","callId":"call@1074","startTime":124951.365,"apiName":"locator.click","class":"Frame","method":"click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-section__content:has(h5:text(\"Record lists\")) table >> //tbody/tr[1] >> td>button[aria-label=\"trash\"]","strict":true},"stepId":"pw:api@311","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@1074"}
{"type":"log","callId":"call@1074","time":124956.513,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div.co-section__content:has(h5:text(\"Record lists\")) table').locator('//tbody/tr[1]').locator('td>button[aria-label=\"trash\"]')"}
{"type":"log","callId":"call@1074","time":124959.556,"message":"  locator resolved to <button type=\"button\" aria-label=\"trash\" class=\"co-link co-button--icon\" data-pendo-campaignform-lists-removebutton=\"campaignWizard:listsConfiguration.recordLists\">…</button>"}
{"type":"log","callId":"call@1074","time":124960.063,"message":"attempting click action"}
{"type":"log","callId":"call@1074","time":124960.292,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1074","time":124973.789,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1074","time":124973.805,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1074","time":124974.24,"message":"  done scrolling"}
{"type":"log","callId":"call@1074","time":124984.44,"message":"  performing click action"}
{"type":"log","callId":"call@1074","time":125002.753,"message":"  click action done"}
{"type":"log","callId":"call@1074","time":125002.768,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@1074","time":125003.081,"message":"  navigations have finished"}
{"type":"after","callId":"call@1074","endTime":125003.159,"point":{"x":728.98,"y":459},"afterSnapshot":"after@call@1074"}
{"type":"before","callId":"call@1076","startTime":125110.863,"apiName":"locator.click","class":"Frame","method":"click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-modal__dialog >> [data-pendo-campaignform-lists-removerlistbutton-confirm]","strict":true},"stepId":"pw:api@312","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@1076"}
{"type":"log","callId":"call@1076","time":125116.515,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div.co-modal__dialog').locator('[data-pendo-campaignform-lists-removerlistbutton-confirm]')"}
{"type":"log","callId":"call@1076","time":125119.563,"message":"  locator resolved to <button type=\"button\" class=\"co-button co--danger\" data-pendo-campaignform-lists-removerlistbutton-confirm=\"true\">…</button>"}
{"type":"log","callId":"call@1076","time":125120.056,"message":"attempting click action"}
{"type":"log","callId":"call@1076","time":125120.266,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1076","time":125140.419,"message":"  element is not stable"}
{"type":"log","callId":"call@1076","time":125140.429,"message":"retrying click action"}
{"type":"log","callId":"call@1076","time":125141.427,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1076","time":125173.886,"message":"  element is not stable"}
{"type":"log","callId":"call@1076","time":125173.897,"message":"retrying click action"}
{"type":"log","callId":"call@1076","time":125173.899,"message":"  waiting 20ms"}
{"type":"log","callId":"call@1076","time":125195.535,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1076","time":125223.696,"message":"  element is not stable"}
{"type":"log","callId":"call@1076","time":125223.707,"message":"retrying click action"}
{"type":"log","callId":"call@1076","time":125223.709,"message":"  waiting 100ms"}
{"type":"log","callId":"call@1076","time":125325.54,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1076","time":125357.02,"message":"  element is not stable"}
{"type":"log","callId":"call@1076","time":125357.034,"message":"retrying click action"}
{"type":"log","callId":"call@1076","time":125357.036,"message":"  waiting 100ms"}
{"type":"log","callId":"call@1076","time":125458.591,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1076","time":125490.68,"message":"  element is not stable"}
{"type":"log","callId":"call@1076","time":125490.695,"message":"retrying click action"}
{"type":"log","callId":"call@1076","time":125490.698,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1076","time":125992.543,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1076","time":126023.66,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1076","time":126023.676,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1076","time":126024.518,"message":"  done scrolling"}
{"type":"log","callId":"call@1076","time":126036.683,"message":"  performing click action"}
{"type":"log","callId":"call@1076","time":126048.857,"message":"  click action done"}
{"type":"log","callId":"call@1076","time":126048.871,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@1076","time":126049.009,"message":"  navigations have finished"}
{"type":"after","callId":"call@1076","endTime":126049.091,"point":{"x":816.45,"y":486},"afterSnapshot":"after@call@1076"}
{"type":"before","callId":"call@1078","startTime":126157.387,"apiName":"expect.toBeHidden","class":"Frame","method":"expect","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-section__content:has(h5:text(\"Record lists\")) table","expression":"to.be.hidden","expectedValue":{"value":{"v":"undefined"},"handles":[]},"isNot":false,"timeout":6000},"stepId":"expect@313","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@1078"}
{"type":"log","callId":"call@1078","time":126162.523,"message":"expect.toBeHidden with timeout 6000ms"}
{"type":"log","callId":"call@1078","time":126162.53,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div.co-section__content:has(h5:text(\"Record lists\")) table')"}
{"type":"after","callId":"call@1078","endTime":126165.552,"result":{"matches":true},"afterSnapshot":"after@call@1078"}
{"type":"before","callId":"call@1080","startTime":126172.527,"apiName":"page.waitForResponse","class":"Page","method":"waitForEventInfo","params":{"info":{"waitId":"d79981fdbaf4f4ea197efeb7a0620050","phase":"before","event":"response"}},"stepId":"pw:api@314","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@1080"}
{"type":"before","callId":"call@1084","startTime":126174.28,"apiName":"locator.click","class":"Frame","method":"click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-section__content:has(h5:text(\"Record lists\")) button>span:text(\"Add record lists\")","strict":true},"stepId":"pw:api@315","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@1084"}
{"type":"log","callId":"call@1084","time":126181.389,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div.co-section__content:has(h5:text(\"Record lists\")) button>span:text(\"Add record lists\")')"}
{"type":"log","callId":"call@1084","time":126184.806,"message":"  locator resolved to <span>Add record lists</span>"}
{"type":"log","callId":"call@1084","time":126185.336,"message":"attempting click action"}
{"type":"log","callId":"call@1084","time":126185.593,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1084","time":126189.773,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1084","time":126189.78,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1084","time":126190.041,"message":"  done scrolling"}
{"type":"log","callId":"call@1084","time":126198.961,"message":"  performing click action"}
{"type":"log","callId":"call@1084","time":126228.126,"message":"  click action done"}
{"type":"log","callId":"call@1084","time":126228.139,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@1084","time":126228.452,"message":"  navigations have finished"}
{"type":"after","callId":"call@1084","endTime":126228.536,"point":{"x":372.28,"y":378},"afterSnapshot":"after@call@1084"}
{"type":"after","callId":"call@1080","endTime":126589.745,"afterSnapshot":"after@call@1080"}
{"type":"before","callId":"call@1092","startTime":126591.428,"apiName":"response.json","class":"Response","method":"body","params":{},"stepId":"pw:api@316","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@1092"}
{"type":"after","callId":"call@1092","endTime":126592.069,"result":{"binary":"<Buffer>"},"afterSnapshot":"after@call@1092"}
{"type":"before","callId":"call@1094","startTime":126593.456,"apiName":"locator.waitFor","class":"Frame","method":"waitForSelector","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> nth=0","strict":true,"timeout":60000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@317","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@1094"}
{"type":"log","callId":"call@1094","time":126611.969,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div#panels-layout-content-id').locator('table').first() to be visible"}
{"type":"log","callId":"call@1094","time":126617.727,"message":"  locator resolved to visible <table class=\"co-table co-table--selectable\">…</table>"}
{"type":"after","callId":"call@1094","endTime":126617.762,"result":{},"afterSnapshot":"after@call@1094"}
{"type":"before","callId":"call@1096","startTime":126628.538,"apiName":"locator.waitFor","class":"Frame","method":"waitForSelector","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> nth=0","strict":true,"timeout":60000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@318","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@1096"}
{"type":"log","callId":"call@1096","time":126639.666,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div#panels-layout-content-id').locator('table').first() to be visible"}
{"type":"log","callId":"call@1096","time":126644.209,"message":"  locator resolved to visible <table class=\"co-table co-table--selectable\">…</table>"}
{"type":"after","callId":"call@1096","endTime":126644.246,"result":{},"afterSnapshot":"after@call@1096"}
{"type":"before","callId":"call@1098","startTime":126654.459,"apiName":"locator.waitFor","class":"Frame","method":"waitForSelector","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> //tbody/tr[1]/td[1] >> div.co-placeholder--animated","strict":true,"timeout":10000,"state":"hidden","omitReturnValue":true},"stepId":"pw:api@319","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@1098"}
{"type":"log","callId":"call@1098","time":126660.797,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div#panels-layout-content-id').locator('table').locator('//tbody/tr[1]/td[1]').locator('div.co-placeholder--animated') to be hidden"}
{"type":"after","callId":"call@1098","endTime":126664.465,"result":{},"afterSnapshot":"after@call@1098"}
{"type":"before","callId":"call@1100","startTime":126671.859,"apiName":"page.waitForTimeout","class":"Frame","method":"waitForTimeout","params":{"timeout":1000},"stepId":"pw:api@320","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@1100"}
{"type":"after","callId":"call@1100","endTime":127683.155,"afterSnapshot":"after@call@1100"}
{"type":"before","callId":"call@1102","startTime":127691.924,"apiName":"expect.toBeVisible","class":"Frame","method":"expect","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id","expression":"to.be.visible","expectedValue":{"value":{"v":"undefined"},"handles":[]},"isNot":false,"timeout":6000},"stepId":"expect@321","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@1102"}
{"type":"log","callId":"call@1102","time":127698.424,"message":"expect.toBeVisible with timeout 6000ms"}
{"type":"log","callId":"call@1102","time":127698.431,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div#panels-layout-content-id')"}
{"type":"log","callId":"call@1102","time":127701.334,"message":"  locator resolved to <div tabindex=\"-1\" id=\"panels-layout-content-id\" class=\"co-panels-layout__panel co-panels-layout__panel--over co--active co--large\">…</div>"}
{"type":"after","callId":"call@1102","endTime":127701.401,"result":{"matches":true,"received":{"b":true}},"afterSnapshot":"after@call@1102"}
{"type":"before","callId":"call@1104","startTime":127710.303,"apiName":"page.waitForResponse","class":"Page","method":"waitForEventInfo","params":{"info":{"waitId":"f34c45ad4db39ce3ce5608fbfffadfc9","phase":"before","event":"response"}},"stepId":"pw:api@322","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@1104"}
{"type":"before","callId":"call@1108","startTime":127711.885,"apiName":"locator.fill","class":"Frame","method":"fill","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> input[placeholder=\"Search by list name\"]","strict":true,"value":"metoto_records_list_b6lb"},"stepId":"pw:api@323","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@1108"}
{"type":"log","callId":"call@1108","time":127721.428,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div#panels-layout-content-id').locator('input[placeholder=\"Search by list name\"]')"}
{"type":"log","callId":"call@1108","time":127724.319,"message":"  locator resolved to <input value=\"\" type=\"text\" data-co-name=\"Input\" data-co-version=\"1.3.8\" data-co-project=\"cobalt\" placeholder=\"Search by list name\" class=\"react-input_1-3-8_co-input react-input_1-3-8_co-input--unstyled react-input_1-3-8_co-input--small react-input_1-3-8_co-input--align-left\"/>"}
{"type":"log","callId":"call@1108","time":127724.779,"message":"  fill(\"metoto_records_list_b6lb\")"}
{"type":"log","callId":"call@1108","time":127724.785,"message":"attempting fill action"}
{"type":"log","callId":"call@1108","time":127730.516,"message":"  waiting for element to be visible, enabled and editable"}
{"type":"after","callId":"call@1108","endTime":127736.778,"afterSnapshot":"after@call@1108"}
{"type":"after","callId":"call@1104","endTime":128385.227,"afterSnapshot":"after@call@1104"}
{"type":"before","callId":"call@1116","startTime":128386.748,"apiName":"response.json","class":"Response","method":"body","params":{},"stepId":"pw:api@324","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@1116"}
{"type":"after","callId":"call@1116","endTime":128387.018,"result":{"binary":"<Buffer>"},"afterSnapshot":"after@call@1116"}
{"type":"before","callId":"call@1118","startTime":128388.436,"apiName":"locator.waitFor","class":"Frame","method":"waitForSelector","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> nth=0","strict":true,"timeout":60000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@325","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@1118"}
{"type":"log","callId":"call@1118","time":128397.395,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div#panels-layout-content-id').locator('table').first() to be visible"}
{"type":"after","callId":"call@1118","endTime":188396.391,"error":{"message":"Timeout 60000ms exceeded.","stack":"TimeoutError: Timeout 60000ms exceeded.\n    at ProgressController.run (/node_modules/playwright-core/lib/server/progress.js:75:26)\n    at Frame.waitForSelector (/node_modules/playwright-core/lib/server/frames.js:629:23)\n    at FrameDispatcher.waitForSelector (/node_modules/playwright-core/lib/server/dispatchers/frameDispatcher.js:106:103)\n    at FrameDispatcher._handleCommand (/node_modules/playwright-core/lib/server/dispatchers/dispatcher.js:94:40)\n    at DispatcherConnection.dispatch (/node_modules/playwright-core/lib/server/dispatchers/dispatcher.js:361:39)","name":"TimeoutError"},"afterSnapshot":"after@call@1118"}
{"type":"before","callId":"call@1120","startTime":188408.399,"apiName":"page.screenshot","class":"Page","method":"screenshot","params":{"timeout":5000,"type":"png","fullPage":true,"caret":"initial"},"stepId":"pw:api@327","pageId":"page@ab6c8088dd9487f369c16d7dd0446acd","beforeSnapshot":"before@call@1120"}
{"type":"log","callId":"call@1120","time":188416.989,"message":"taking page screenshot"}
{"type":"log","callId":"call@1120","time":188419.385,"message":"waiting for fonts to load..."}
{"type":"log","callId":"call@1120","time":188420.06,"message":"fonts loaded"}
{"type":"after","callId":"call@1120","endTime":188495.711,"result":{"binary":"<Buffer>"},"afterSnapshot":"after@call@1120"}
