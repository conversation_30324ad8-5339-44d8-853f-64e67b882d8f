const {Locator, expect, Page} = require("@playwright/test");
const ProgressBar = require('progress');
const { allure } = require("allure-playwright");
const fs = require('fs');

exports.BasePage = class BasePage {

    // contentFrame_1 = page.frameLocator("div.app-canvas-0-0-2[style$=\"flex;\"] iframe")
    // contentFrame_2 = page.frameLocator("div#canvas>div:not([style*=\"display: none\"]) iframe")
    // async getActiveFrame(){
    //     return await this.contentFrame_1.isVisible() ? this.contentFrame_1 : this.contentFrame_2
    // }
    //     // this.contentFrame = process.env.npm_config_env==="qa" ? this.contentFrame_qa:this.contentFrame_prod
    // contentFrame = await getActiveFrame()

    constructor(page) {
        this.page = page

        //common iframe
        // this.contentFrame_1 = page.frameLocator("div.app-canvas-0-0-2[style$=\"flex;\"] iframe")
        // this.contentFrame_2 = page.frameLocator("div#canvas>div:not([style*=\"display: none\"]) iframe")
        this.contentFrame = page.frameLocator("div:not([style*=\"display: none\"])>iframe")
        // this.contentFrame = process.env.npm_config_env==="qa" ? this.contentFrame_qa:this.contentFrame_prod
        // this.contentFrame_active = this.contentFrame_1.isVisible() ? this.contentFrame_1 : this.contentFrame_2


        //common ul list
        this.popList = this.contentFrame.locator("div[data-co-name=\"Popup\"] ul")
        this.dropList = this.contentFrame.locator("div.co-dropdown__menu ul")


        this.titleBar = page.locator("div.title-bar-root-component-module__root")

        // this.agentCallStatus = this.titleBar.locator("button[data-testid=\"current-status\"] ")
        this.agentCallStatus = this.titleBar.locator("p.status-button-module__current-status-name")
        this.agentCallStatusSelectorList = this.page.locator("div[data-co-name=\"Popup\"] ul")

        this.secondAreaButton = this.page.locator("[data-testid=\"secondary-area-btn-toggle\"]")
        this.secondAreaButtonActived = this.page.locator("button.secondary-area-module__active")

        this.notificationButton = this.titleBar.locator("div.notifications-button-module__container>button")
        this.notificationNum = this.titleBar.locator("span[data-testid=\"notification-badge-title-bar\"]")
        this.notificationPanel = page.locator("div[data-testid=\"notification-history-panel\"]")
        this.notificationTodayList = this.notificationPanel.locator("div[data-testid=\"today-notifications-list\"]")

        this.dockerSideBar = this.page.locator("[data-testid=\"dock\"]")
        this.phoneAppEntry = this.dockerSideBar.locator("[data-testid=\"dock-button-submenuuc-voice-home\"]")
        this.conversationAppEntry = this.dockerSideBar.locator("[data-testid=\"dock-button-conversation\"]")


        this.docDrawer = page.locator("[data-testid=\"dock-sidebar\"]")
        this.dialerCampaignsNav = this.docDrawer.locator("ul [data-testid=\"dock-button-outbound-dialer-campaigns\"]")
        this.dialerListsNav = this.docDrawer.locator("ul [data-testid=\"dock-button-outbound-dialer-lists\"]")
        this.dialerConfigurationNav = this.docDrawer.locator("ul [data-testid=\"dock-button-outbound-dialer-configurations\"]")
        this.studioFlowNav = this.docDrawer.locator("[data-testid=\"dock-button-classic-admin-admin-studio\"]")
        this.studioAudioPromptsNav = this.docDrawer.locator("[data-testid=\"dock-button-audio-prompts\"]")


        this.startNewSessionButton = this.contentFrame.locator("div.fullscreen_dialog__Content-gjkYIn button")
        this.commonTable = this.contentFrame.locator("table")
        this.commonTableHeader = this.contentFrame.locator("table thead")
        this.commonTableBody = this.contentFrame.locator("table tbody")
        this.selectorlist = this.contentFrame.locator("div[data-testid=\"content\"]>ul>div>div>li")
        this.selectorCheckFlag = this.contentFrame.locator("div[data-testid=\"content\"]>ul>div>div>li i")
        this.selectorSearchInput = this.contentFrame.locator(
            "div[data-testid=\"content\"]>ul input[data-testid=\"searchInput\"]")
        this.selectorCheckbox = this.selectorlist.locator("div.cobalt-checkbox")
        this.contextPopDialog = this.contentFrame.locator("div.cobalt-dropdown-popup")

        // this.pageToast_startRunning = page.locator("[data-testid=\"toast-76d11599-24ec-47f4-97af-fc631f1c30e9\"]")
        // this.pageToast = page.locator("div[data-testid=\"toaster\"]>div>div:nth-child(1)")
        this.pageToast = page.locator("div[data-testid=\"toaster\"] div[data-co-name=\"Message\"]:nth-child(1) div[data-testid]")
        // this.pageToast = page.locator("div[data-testid=\"toaster\"]>div.co-message.co--active:nth-child(1)")
        // this.timerToaster = page.locator("div[data-testid=\"toaster\"]>div:has(div.co-message__timer)")
        this.pageToastlist = page.locator("div[data-testid=\"toaster\"]>div[data-co-name=\"Message\"]")

        // this.modalDialog = this.contentFrame.locator("div.cobalt-modal-pro-content")
        // this.modalDialogBody = this.modalDialog.locator("div.cobalt-modal-pro-body")
        // this.modalDialogFooter = this.modalDialog.locator("div.cobalt-modal-pro-footer")

        this.modalDialog = this.contentFrame.locator("div[class*=co-modal__dialog]")
        this.dialogFooterCancelButton = this.modalDialog.locator("button[data-testid=\"cancelButton\"]")
        // this.dialogFooterCancelButton = this.modalDialog.locator("button:has-text(\"Cancel	\")")
        this.dialogTopCloseButton = this.modalDialog.locator("button>i:text(\"close\")")

        // this.errorPageTitle = this.page.locator("div.app-canvas-0-0-2[style$=\"flex;\"] h5+h1:text(\"Unexpected error\")").nth(0)
        this.errorPageTitle = this.page.locator("div:not([style*=\"display: none\"])[data-testid=\"app\"] h5+h1").nth(0)
        // this.errorRfreshButton = this.page.locator("div.app-canvas-0-0-2[style$=\"flex;\"] button:text(\"Refresh\")").nth(0)
        this.errorRfreshButton = this.page.locator("div:not([style*=\"display: none\"])[data-testid=\"app\"] button:text(\"Refresh\")").nth(0)
        // this.frameloading = this.page.locator("div.app-canvas-0-0-2[style$=\"flex;\"]>div[style$=\"flex;\"] svg.loader-module__loader").nth(0)
        this.frameloading = this.page.locator("div:not([style*=\"display: none\"])[data-testid=\"app\"] svg.loader-module__loader").nth(0)

        //pendo dialog
        this.recommandAdv = this.page.locator("div._pendo-step-container-size")
        this.closeRecommandAdvButton = this.recommandAdv.locator("button._pendo-close-guide")
        this.pendoDialogDismissButton = this.recommandAdv.locator("button._pendo-button-tertiaryButton")
        this.pendoDialogThanksButton = this.recommandAdv.locator("button._pendo-button-primaryButton")


    }

    async saveImage(imageName) {
        await allure.attachment("capture", await this.page.screenshot(), {
            contentType: "image/png",
        })
    }

    async tdlocator(selector, iframe_data_testid = "") {
        if (iframe_data_testid === "") {
            return this.contentFrame.locator(selector)
        }
        return this.page.frameLocator("iframe[data-testid=\"" + iframe_data_testid + "\"]").locator(selector)
    }

    async revToTimestamp(create_datetime) {
        // create_datetime = "Oct 27, 2022, 10:08 PM"
        //Nov 10, 2022, 12:00 AM
        //Feb 18, 2023, 1:11 PM
        create_datetime = create_datetime.replace("\u202f", " ")
        const date = new Date(create_datetime)

        let month_str = create_datetime.split(",")[0].split(" ")[0]
        let month_num = new Date(Date.parse(month_str + " 1, 2012")).getMonth() + 1
        month_str = month_num >= 10 ? month_num : "0" + month_num.toString()
        let day_str = create_datetime.split(",")[0].split(" ")[1]
        day_str = parseInt(day_str) >= 10 ? day_str : "0" + day_str
        let year_str = create_datetime.split(",")[1].split(" ")[1]

        // let hour_str = create_datetime.split(",")[2].split(" ")[1].split(":")[0]
        // let minute_str = create_datetime.split(",")[2].split(" ")[1].split(":")[1]

        let hours = date.getHours()
        let minutes = date.getMinutes()
        if (create_datetime.indexOf('PM') !== -1 && hours < 12) {
            hours = hours + 12;
        }
        if (create_datetime.indexOf('AM') !== -1 && hours === 12) {
            hours = hours - 12;
        }
        if (hours < 10) {
            hours = '0' + hours;
        }
        if (minutes < 10) {
            minutes = '0' + minutes;
        }


        // let hour_str = create_datetime.split(",")[2].split(" ")[1].split(":")[0]
        // hour_str = hour_str >= 10 ? hour_str : "0" + hour_str
        // let minute_str = create_datetime.split(",")[2].split(" ")[1].split(":")[1]
        // let am_pm_str = create_datetime.split(",")[2].split(" ")[2]
        // hour_str = am_pm_str === "PM" && parseInt(hour_str) < 12 ? eval(parseInt(hour_str) + 12).toString() : hour_str
        // hour_str = am_pm_str === "AM" && parseInt(hour_str) === 12 ? "00" : hour_str
        // if(am_pm_str==="PM"){
        // 	hour_str = eval(parseInt(hour_str)+12).toString()
        // }
        const timeStr = `${year_str}-${month_str}-${day_str}T${hours}:${minutes}:00.000Z`
        return timeStr
        // return year_str + "-" + month_str + "-" + day_str + "T" + hours + ":" + minutes + ":" + "00.000Z"
    }


    async getLocaleTime(delayMinute = 1, timezone = "America/New_York", locales = "en-US") {
        let myDate = new Date()

        let timeString = myDate.toLocaleTimeString('en-US', {timeZone: timezone, hour12: true})
        let hour = timeString.split(":")[0]
        let minute = timeString.split(":")[1]
        const seconds = timeString.split(":")[2].split(" ")[0]
        const meridianTime = timeString.split(":")[2].split(" ")[1]
        if (parseInt(seconds) >= 35 && parseInt(minute) === eval(60 - delayMinute - 1)) {
            minute = "00"
            hour = parseInt(hour) < 9 ? "0" + (parseInt(hour) + 1).toString() : (parseInt(hour) + 1).toString()
        } else if (parseInt(seconds) >= 35 && parseInt(minute) === eval(60 - delayMinute)) {
            minute = "01"
            hour = parseInt(hour) < 9 ? "0" + (parseInt(hour) + 1).toString() : (parseInt(hour) + 1).toString()
        } else if (parseInt(seconds) >= 35) {
            minute = parseInt(minute) < 9 ? "0" + (parseInt(minute) + 2).toString() : (parseInt(minute) + 2).toString()
        } else if (parseInt(seconds) < 35 && parseInt(minute) === eval(60 - delayMinute)) {
            minute = "00"
            hour = parseInt(hour) < 9 ? "0" + (parseInt(hour) + 1).toString() : (parseInt(hour) + 1).toString()
        } else {
            minute = parseInt(minute) < 9 ? "0" + (parseInt(minute) + 1).toString() : (parseInt(minute) + 1).toString()
        }
        console.log(`hour is ${hour}, minute is ${minute}, meridianTime is ${meridianTime}`)
        return {"hour": hour, "minute": minute, "meridianTime": meridianTime}
    }

    async editJsonObj(jsonObj, nodePath, value) {
        /*
        jsonObj: {"name":"metoto","age":18, "level":{"CET4":"pass","CET6":"fail"}}
        path: level.CET4
        value: any value
         */
        let node_arr = nodePath.split('.');
        let tempObj = jsonObj
        let i = 0;
        let node_depth = node_arr.length;

        for (let i = 0; i < node_depth - 1; i++) {
            if (!tempObj.hasOwnProperty(node_arr[i])) {
                tempObj[node_arr[i]] = {};
            }

            tempObj = tempObj[node_arr[i]];
        }

        tempObj[node_arr[node_depth - 1]] = value;
        // for(let node of node_arr){
        // 	if(i>=node_depth-1){
        // 		eval(`tempObj.${node}=${value}`)
        // 		return tempObj;
        // 	}
        // 	if(tempObj.hasOwnProperty(node)){
        // 		tempObj = eval(`jsonObj.${node}`)
        // 	}else{
        // 		eval(`tempObj.${node}={}`)
        // 	}
        // 	i+=1;
        // }
    }

    async retry_goto(appName, retry = 20) {
        if (retry < 0) {
            throw new Error("Failed to navigate to ${appName} after retry 20 times")
        }
        await Promise.all([
            this.page.goto("/atlas/apps/" + appName),
            await this.page.waitForNavigation(),
            // await this.openWSListener(),
            await this.waitForError_new(appName, retry - 1),
            await this.hiddenConflictToast()
            // await this.hiddenMessageToast()
        ]).catch((res, err) => {
            // console.log(res)
            // console.log(err)
            console.log("goto " + appName + " completed.")
        })
    }

    async retry_goto_with_context(appName, retry = 20, context = {"baseURL": "https://email-qa.trytalkdesk.com"}) {
        if (retry < 0) {
            throw new Error("Failed to navigate to ${appName} after retry 20 times")
        }
        const browserContext = this.page.context
        browserContext.baseURL = context.baseURL
        await Promise.all([
            this.page = await browserContext.newPage(),
            this.page.goto("/atlas/apps/" + appName),
            await this.page.waitForNavigation(),
            // await this.openWSListener(),
            await this.waitForError_new(appName, retry - 1),
            // await this.hiddenConflictToast()
            await this.hiddenMessageToast()
        ]).catch((res, err) => {
            // console.log(res)
            // console.log(err)
            console.log("goto " + appName + " completed.")
        })
    }

    async waitForError(appName, retrytimes) {
        try {
            await this.page.waitForURL(/atlas\/error/, {timeout: 3 * 1000}).then(() => {
                console.log("Your network configuration may not stabel, try again.")
                this.retry_goto(appName, retrytimes)
            });
            if (this.frameloading.isVisible()) {
                this.wait_for_timeout(3)
                this.waitForError(appName, retrytimes)
            }
        } catch (err) {
            console.log(err)
        }
    }

    async waitForError_new(appName, retrytimes) {
        await this.wait_for_timeout(3)
        if (this.page.url().includes("/atlas/error")) {
            console.log("Your network configuration may not stabel, try again.")
            await this.retry_goto(appName, retrytimes)
        }
        if (await this.frameloading.isVisible()) {
            await this.wait_for_timeout(3)
            await this.waitForError_new(appName, retrytimes)
        }
    }

    async waitForVisibleLocator(locator_list, timeout = 10) {
        while (timeout >= 0) {
            for (let index in locator_list) {
                if (await locator_list[index].isVisible()) {
                    return locator_list[index]
                }
            }
            await this.wait_for_timeout(1)
            timeout -= 1
        }
        console.error("Unable to find any locator in locator list.")
        return false
    }

    async active() {
        await this.page.bringToFront()
    }

    async wait_for_locator(testLocator, trytimes = 60, elem_state = "visible") {
        if (elem_state === "enable") {
            // console.log(await testLocator.isEnabled())
            while (!await testLocator.nth(0).isEnabled({timeout: 1 * 1000}) && trytimes > 0) {
                // console.log(await testLocator.isEnabled(timeout=1*1000))
                await this.wait_for_timeout(1)
                trytimes -= 1
            }
            if (trytimes <= 0) {
                throw new Error("Failed to wait the locator change to enable states")
            }
        } else {
            await testLocator.nth(0).waitFor({timeout: trytimes * 1000, state: elem_state})
        }


    }


    async wait_for_locator_new(testLocator, trytimes = 60, elem_state = "visible") {

        // const message = `Waiting for locator ${testLocator.toString()}...`;
        const bar = new ProgressBar(`[:bar] :percent :current/${trytimes} ${message}`, {total: trytimes});

        if (elem_state === "enable") {
            while (!await testLocator.isEnabled() && trytimes > 0) {
                await bar.tick();
                await this.wait_for_timeout(1);
                trytimes -= 1;
            }
            if (trytimes <= 0) {
                throw new Error("Failed to wait the locator change to enable states");
            }
        } else {
            const start = Date.now();
            while (Date.now() - start < trytimes * 1000) {
                try {
                    await testLocator.waitFor({timeout: trytimes * 1000, state: elem_state});
                    bar.tick(bar.total);
                    return;
                } catch (e) {
                    await bar.tick();
                }
            }
            throw new Error(`Failed to wait for locator ${testLocator.toString()} to be ${elem_state}`);
        }
    }

    async wait_for_timeout(duration = 1) {
        await this.page.waitForTimeout(duration * 1000)
    }

    async wait_for_toast(toasterLocator = "") {

        if (toasterLocator === "") {
            toasterLocator = this.pageToast
        }
        try {
            await toasterLocator.waitFor({timeout: 20 * 1000, state: "visible"})
            await toasterLocator.waitFor({timeout: 20 * 1000, state: "hidden"})
        } catch (err) {
            console.log(err)
        }

    }

    async check_toast_title(toasterTitle = "", toastType = "check_circle") {
        // await this.pageToast.waitFor({timeout: 20 * 1000, state: "visible"})

        await this.page.locator(`//div[@data-testid='toaster']//i[contains(text(),'${toastType}')]/ancestor::div[@data-co-name='Message']`).nth(0).waitFor({
            timeout: 20 * 1000,
            state: "visible"
        })
        // const toastTitleText = await this.page.locator(`//div[@data-testid='toaster']//i[contains(text(),'${toastType}')]/ancestor::div[@data-co-name='Message']//p[1]`).textContent();
        const toastTitleTexts = await this.page.locator(`//div[@data-testid='toaster']//i[contains(text(),'${toastType}')]/ancestor::div[@data-co-name='Message']//p[1]`).all();
        let title_arr = []
        for (const toastTitle of toastTitleTexts) {
            title_arr.push(await toastTitle.textContent())
        }
        // const toastTitleText = await this.pageToast.locator("p:nth-child(1)").textContent()
        await expect(title_arr).toContain(toasterTitle)
    }

    async wait_for_animated(table_locator = "", columnIndex = "1") {
        if (table_locator !== "") {
            await this.wait_for_locator(table_locator)
            const animation_status = table_locator.locator("//tbody/tr[1]/td[" + columnIndex.toString() + "]").locator("div.co-placeholder--animated")
            try {
                await animation_status.waitFor({timeout: 10 * 1000, state: "hidden"})
                await this.wait_for_timeout(1)
            } catch (err) {
                console.log(err)
            }
        }

    }

    async closeToast(time = 3) {
        if (await this.pageToast.isVisible() && time > 0) {
            await this.pageToast.locator("button>i:text(\"close\")").click()
            time -= 1
            // await this.pageToast.locator("button.co--dismiss").click()
            await this.closeToast(time)
        }
    }

    async closeAllToast() {
        if (await this.pageToast.isVisible()) {
            let count = await this.pageToastlist.count()
            for (let index = 0; index < count; index += 1) {
                if (await this.pageToastlist.nth(index).locator("button.co--dismiss").isVisible()) {
                    await this.pageToastlist.nth(index).locator("button.co--dismiss").click()
                }
            }
        }
    }

    async openWSListener() {
        this.page.on('websocket', ws => {
            console.log(`===WS START: ${ws.url()} ======`)
            ws.on('framesent', event => console.log(`FRAME_SENT:${event.payload}`));
            ws.on('framereceived', event => console.log(`FRAME_RECEIVED:${event.payload}`));
            ws.on('close', () => console.log(`===WS CLOSED: ${ws.url()} ======`))
        });
    }

    async hiddenMessageToast() {
       // Listener added for a 'DOMNodeInserted' mutation event. 
       // This event type is deprecated, and will be removed from this browser very soon. 
       // Usage of this event listener will cause performance issues today, and represents a large risk of future site breakage. Consider using MutationObserver instead. See https://chromestatus.com/feature/5083947249172480 for more information
        await this.page.addScriptTag({url: 'https://code.jquery.com/jquery-3.6.0.min.js'});
        await this.page.evaluate(() => {
            console.log("========START EXECUTE JQUERY=============");
            $(document).on("DOMNodeInserted", "div[data-testid=\"toaster\"]>div", async function () {
                console.log("=======A new child node has been added to the target element.===========");
                // const toastRoot = document.querySelector('div[data-testid="toaster"]');
                const toast = document.querySelector('div[data-testid="toaster"] div[data-co-name="Message"] div[data-testid]');
                const toastTitleList = document.querySelectorAll('div[data-testid="toaster"] div[data-co-name="Message"] div[data-testid] p:nth-child(1)');
                for (let index = 0; index < toastTitleList.length; index++) {
                    let toast_title_content = toastTitleList[index].textContent;
                    if (toast_title_content.includes('Conversations is open in another tab or device')) {
                        console.log("=====find the conversation conflict toast======");
                        const toast_node = document.querySelector(`div[data-testid="toaster"]>div>div:nth-child(${index + 1})`);
                        toast_node.style.display = 'none';
                        // toast_node.remove();
                    } else if (toast_title_content.includes('The application needs to have microphone access')) {
                        console.log("=====find the require microphone access toast======");
                        const toast_node = document.querySelector(`div[data-testid="toaster"]>div>div:nth-child(${index + 1})`);
                        toast_node.style.display = 'none';
                    } else {
                        console.log("=======No need to fix this toast=======")
                    }
                }
                // const toast_content = document.querySelector('div[data-testid="toaster"] div[data-co-name="Message"] div[data-testid] p').textContent;
                // if (toast_content.includes('Conversations is open in another tab or device')) {
                //     console.log("=====find the conversation conflict toast======");
                //     // await this.page.pause()
                //     // toast.style.display = 'none';
                //     const toast_node = document.querySelector('div[data-testid="toaster"]>div>div');
                //     // document.querySelector('div[data-testid="toaster"] div[data-co-name="Message"]').remove();
                //     // document.querySelector('div[data-testid="toaster"] div[data-co-name="Message"] div[data-testid] button').click();
                //     // document.querySelector('div[data-testid="toaster"] div[data-co-name="Message"] div[data-testid] button>i').click();
                //     toast_node.remove();
                //     // toast.remove();
                // } else {
                //     console.log("=======No need to fix this toast=======");
                // }
                // const toast_close = document.querySelector('div[data-testid="toaster"] div[data-co-name="Message"] div[data-testid] button')
                // toast_close.click();
                // toast.style.display = 'none';
                // toast.remove();

            });
            console.log("========END EXECUTE JQUERY=============");
        })
    }
    
    
    async hiddenConflictToast() {
        await this.page.evaluate(() => {
            console.log("========START EXECUTE JQUERY=============");
            // let toastRoot = document.querySelector('div#toast');
            const toastRoot = document.querySelector('div[data-testid="toaster"]');
            let MutationObserver = window.MutationObserver || window.WebKitMutationObserver || window.MozMutationObserver;
            let mutationObserver = new MutationObserver((mutations) => {
                const toast = document.querySelector('div[data-testid="toaster"] div[data-co-name="Message"] div[data-testid]')
                const toast_content = document.querySelector('div[data-testid="toaster"] div[data-co-name="Message"] div[data-testid] p').textContent;
                console.log(toast);
                if (toast_content.includes('Conversations is open in another tab or device')) {
                    console.log("=====find the conversation conflict toast======");
                    toast.remove();
                } else {
                    console.log("=======No need to fix this toast=======");
                }
            })
            mutationObserver.observe(toastRoot, {
                childList: true, //
                // attributes: true, //
                // characterData: true, //
                subtree: true, //
                // attributesFilter: ['class', 'style'], //
                // attributesOldValue: true, //
                // characterDataOldValue: true //
            })
            console.log("========START EXECUTE JQUERY=============");
        });

    }

    async hiddenPendoDialog() {

    }

    async waitAndClearToast(timeout = 10) {
        // console.log("just pass it")
        while (await this.pageToast.isVisible() && timeout > 0) {
            console.log("await and clear toast")
            if (await this.pageToast.locator("button>i:text(\"close\")").isVisible()) {
                try {
                    await this.pageToast.locator("button>i:text(\"close\")").click()
                } catch (e) {
                    return
                }

            }
            // await this.pageToast.locator("button.co--dismiss").click()
            await this.wait_for_timeout(1)
            timeout -= 1
        }
    }

    async selectValuesFromDropList(seletorList, valuelist, searchkey = "") {
        // await optButton.click()
        if (!searchkey === "") {

        }
        for (let idx in valuelist) {
            const selector = await seletorList.locator("li:has-text(\"" + valuelist[idx] + "\")").nth(0)
            await selector.click()
        }
    }


    async getTableItem(table_locator, row = "") {
        await this.wait_for_locator(table_locator)
        let index = row.toString()
        const tableItem = table_locator.locator("//tbody/tr[" + index + "]")
        if (tableItem) {
            return tableItem
        } else {
            console.error("can not get tableItem from locator")
        }
    }

    async getTabelCellByIndex(table_header_locator, tableItem, columnIndex) {
        await this.wait_for_locator(table_header_locator)
        const index = columnIndex.toString()
        if ("-1" !== index) {
            return tableItem.locator("//td[" + index + "]")
        } else {
            console.error("There is no columnName match in the table header")
        }
    }

    async clickTabelCellByIndex(columnIndex, row, table_locator, table_header_locator, clickableSelector = "") {
        const tableItem = await this.getTableItem(table_locator, row)
        const tableCell = await this.getTabelCellByIndex(table_header_locator, tableItem, columnIndex)
        if (clickableSelector) {
            const selectCell = tableCell.locator(clickableSelector)
            await this.wait_for_locator(selectCell)
            await selectCell.click()
        } else {
            const selectCell = tableCell
            await this.wait_for_locator(selectCell)
            await selectCell.click()
        }

    }

    async getTableColumnIndex(table_header_locator, columName) {
        const columns = table_header_locator.locator("//tr/th")
        const columns_num = await columns.count()
        for (let index = 1; index <= columns_num; index += 1) {
            let i = index.toString()
            let colname = await table_header_locator.locator("//tr/th[" + i + "]").textContent()
            if (colname === columName || colname.replace("arrow_drop_up", "").replace("arrow_drop_down", "").replace("help_outline", "") === columName) {
                return index
            }
        }
        return -1
    }

    async checkColumnDisplay(columnList) {
        for (let col in columnList) {
            let columnName = columnList[col]
            expect(await this.getTableColumnIndex(this.commonTableHeader, columnName)).not.toEqual(-1)
        }
    }

    async getTableHeaderCell(table_header_locator, columnName) {
        await this.wait_for_locator(table_header_locator)
        const index = await this.getTableColumnIndex(table_header_locator, columnName)
        if (index !== -1) {
            return table_header_locator.locator("//tr/th[" + index.toString() + "]")
        } else {
            console.error("There is no columnName match in the table header")
            throw AssertionError("There is no columnName match in the table header")
        }
    }

    async getTabelCell(table_header_locator, tableItem, columnName) {
        await this.wait_for_locator(table_header_locator)
        const index = await this.getTableColumnIndex(table_header_locator, columnName)
        let i = index.toString()
        if (index !== -1) {
            if (columnName === "Subject") {
                return tableItem.locator("//td[" + i + "]").locator("*[href]")
            } else {
                return tableItem.locator("//td[" + i + "]")
            }
        } else {
            console.error("There is no columnName match in the table header")
        }

    }

    async clickTabelCell(columnName, row, table_locator, table_header_locator) {
        const tableItem = await this.getTableItem(table_locator, row)
        const tableCell = await this.getTabelCell(table_header_locator, tableItem, columnName)
        await tableCell.click()
    }

    async clickContextButton(tableHeaderLocator, tableBodyLocator, listColunmName, targetValue, buttonName) {
        await this.wait_for_locator(tableHeaderLocator)
        await this.wait_for_locator(tableBodyLocator)
        const table_list_num = await this.getTableSize(tableBodyLocator)
        for (let row = 1; row <= table_list_num; row += 1) {
            let find_value = await this.getTabelCellValue(listColunmName, row, tableBodyLocator, tableHeaderLocator)
            if (find_value.includes(targetValue, 0)) {
                await tableBodyLocator.locator("//tbody/tr[" + row.toString() + "]//button").click()
                await this.contextPopDialog.locator("ul>li p:text(\"" + buttonName + "\")").click()
            }
        }
    }

    async getTableSize(table_locator) {
        return await table_locator.locator("tbody>tr").count()
    }

    async getTabelCellValue(columnName, row, table_locator, table_header_locator) {
        let tableItem = await this.getTableItem(table_locator, row)
        let tableCell = await this.getTabelCell(table_header_locator, tableItem, columnName)
        return await tableCell.textContent()
        // return await this.getTabelCell(table_header_locator, tableItem, columnName).textContent()
    }

    async checkListSort(table_locator, table_header_locator, listColunmName, sort = "asc") {
        const columnIndex = await this.getTableColumnIndex(table_header_locator, listColunmName)
        const tableRows = await this.getTableSize(table_locator)
        for (let row in tableRows) {
            if (row === tableRows - 1) {
                break;
            }
            let currentRowValue = await this.getTableItem(row).locator("//td[" + columnIndex.toString() + "]").textContent()
            let nextRowValue = await this.getTableItem(eval(row + 1)).locator("//td[" + columnIndex.toString() + "]").textContent()
            if (sort === "asc") {
                expect(currentRowValue <= nextRowValue).toBe(true)
            } else {
                expect(currentRowValue >= nextRowValue).toBe(true)
            }
        }
    }

    async isDisplay(locator, timeout = 10) {
        if (await locator.isVisible() && timeout > 0) {
            return true
        } else {
            if (timeout <= 0) {
                return false
            }
            await this.wait_for_timeout(1)
            await this.isDisplay(locator, timeout - 1)
        }
    }

    // async clickFooterButtonOnModal(modalDialogFooterLocator,buttonName=""){
    // 	if(modalDialogFooterLocator){
    // 		let modalDialogFooterLocator = this.modalDialogFooter
    // 	}
    // 	if(await this.isDisplay(modalDialogFooterLocator.locator("button>div:text(\""+buttonName+"\")"),5)){
    // 		await modalDialogFooterLocator.locator("button>div:text(\""+buttonName+"\")").click()
    // 	}else if(await this.isDisplay(modalDialogFooterLocator.locator("button:text(\""+buttonName+"\")"),5)){
    // 		await modalDialogFooterLocator.locator("button:text(\""+buttonName+"\")").click()
    // 	}else{
    // 		console.error("The buttonName on modal dialog is not found(visible)!")
    // 	}
    //
    // }

    async closeModalDialog() {
        await this.dialogTopCloseButton.click()
    }

    async cancelModalDialog() {
        await this.dialogFooterCancelButton.click()
    }


    async clickNotificationButton() {
        await this.notificationButton.click({position: {x: 10, y: 25}})
    }

    async getNotificationFromTodayList(index = "1") {
        const notificationItemTitle = this.page.locator("//div[@data-testid=\"today-notifications-list\"]/header/following-sibling::div[position()=" + index.toString() + "]//span[@data-testid=\"atlas__notification-title")
        // const notificationItemTitle = this.notificationTodayList.locator("header+div>div:nth-child(" + index + ")>div[data-co-name=\"Box\"]+div span[data-testid=\"atlas__notification-title\"]")
        return await notificationItemTitle.textContent()
    }

    async clickNotificationFromTodayList(index = "1") {
        const notificationItem = this.page.locator("//div[@data-testid=\"today-notifications-list\"]/header/following-sibling::div[position()=" + index.toString() + "]")
        await notificationItem.click()
    }

    async checkNotificationFromTodayList(index = 1, name = "") {
        const notificationItem = this.page.locator("//div[@data-testid=\"today-notifications-list\"]/header/following-sibling::div[position()=" + index.toString() + "]")
        const notificationTitle = notificationItem.locator("span[data-testid=\"atlas__notification-title\"]")
        const notificationContent = notificationItem.locator("p[data-testid=\"notification-card-body\"]")
        const bodyText = await notificationItem.textContent()
        const titleText = await notificationTitle.textContent()
        const contentText = await notificationContent.textContent()
        // console.log(`notification title is ${titleText}`)
        await expect(bodyText).toContain(name)
        // await expect(await (notificationTitle.or(notificationContent)).textContent()).toContain(name)
    }


    async waitForNotification(name, duration = 5, index = 1) {
        await this.clickNotificationButton()
        await expect(this.notificationPanel).toBeVisible()
        const notificationItem = this.page.locator("//div[@data-testid=\"today-notifications-list\"]/header/following-sibling::div[position()=" + index.toString() + "]")
        const notificationTitle = notificationItem.locator("span[data-testid=\"atlas__notification-title\"]")
        let titleText = await notificationTitle.textContent()
        while (!titleText.includes(name) && duration > 0) {
            await this.wait_for_timeout(1)
            await this.clickNotificationButton()
            titleText = await notificationTitle.textContent()
            duration -= 1
        }
    }

    async checkClipboardText(expText, inputBox) {
        await inputBox.click()
        // await this.dialerRecordsSearchInput.click()
        //only available in mac os, if in windows, this should be Control+keyV
        const isMacOS = (process.platform === 'darwin');
        await this.page.keyboard.press(isMacOS ? 'Meta+KeyV' : 'Control+V');
        // await this.page.keyboard.press('Meta+KeyV');
        await this.wait_for_timeout(1)
        const text = await inputBox.getAttribute("value")
        await expect(text).toEqual(expText)
    }

    async tdHasAttribute(tdlocator, attributeName) {
        const attrExist = await tdlocator.evaluate(
            (node, attributeName) => node.hasAttribute(attributeName),
            attributeName
        );
        return attrExist
        // return attrExist{
        //     attrExist,
        // };
    }


    async closePendoDialog() {
        // const pendoDialog = await this.recommandAdv
        // await pendoDialog.evaluate(pendoDialog => {pendoDialog.style.display = 'none';});
        if(await this.recommandAdv.isVisible()){
            await this.closeRecommandAdvButton.click()
        }else if(await this.pendoDialogDismissButton.isVisible()){
            await this.pendoDialogDismissButton.click()
        }else if(await this.pendoDialogThanksButton.isVisible()){
            await this.pendoDialogThanksButton.click()
        }
        
        // await (this.closeRecommandAdvButton.or(this.pendoDialogDismissButton).or(this.pendoDialogThanksButton)).click()
    }

    async dragToTarget(source, target, vector = [0, 0]) {
        const targetBox = await target.boundingBox();
        const targetX = targetBox.x + targetBox.width + vector[0]
        const targetY = targetBox.y + targetBox.height + vector[1]

        await source.hover()
        await this.page.mouse.down()

        await this.page.mouse.move(targetX, targetY)
        await this.page.mouse.up()
        await this.wait_for_timeout(2)
    }

    async dragLine(source, target) {
        const targetBox = await target.boundingBox();
        const targetX = targetBox.x
        const targetY = targetBox.y

        await source.hover()
        await this.page.mouse.down()

        await this.page.mouse.move(targetX + targetBox.width / 2, targetY + targetBox.height / 2)
        await this.page.mouse.up()
        await this.wait_for_timeout(2)

    }

    async closeToaster(keywordlist) {
        for (let index in keywordlist) {
            const toast = this.page.locator(`div[data-testid="toaster"] div[data-co-name="Message"] div[data-testid]:has-text("${keywordlist[index]}")`).nth(0)
            await this.page.addLocatorHandler(toast,async()=>{
                console.log(`====close the toast with keyword: ${keywordlist[index]}====`)
                await toast.locator('button>i:text("close")').click()
            },{times:3,noWaitAfter: true})
        }
    }

    async addWebSocketListener(page, role="agent"){
        const testEnv = process.env.npm_config_env
        const ws_app_id = eval(`process.env.WEBSOCKET_APP_ID_${testEnv}`)
        console.log(`ws_app_id is ${ws_app_id}`)
        const messages = []
        page.on("websocket", (wsEvent) => {
            console.log(`WebSocket opened: ${wsEvent.url()}>`);
            if(wsEvent.url().includes(ws_app_id)){  
                wsEvent.on('framesent',event => {
                    console.log(`Time: ${new Date().toISOString()}, Type: Framesent, Message: ${event.payload}`);
                    messages.push({time: Date.now(), type: 'Framesent', message: event.payload});
                });
                wsEvent.on('framereceived',event => {
                    console.log(`Time: ${new Date().toISOString()}, Type: Framereceived, Message: ${event.payload}`)
                    messages.push({time: Date.now(), type: 'Framereceived', message: event.payload});
                });
                wsEvent.on("close", () => {
                    console.log(`Time: ${new Date().toISOString()}, Type: close, Message: Websocket closed`)
                    console.log("WebSocket closed");
                });
            }
        })
        page.on("close", async () => {
            console.log("Page closed");
            let messageString = ""
              messages.sort((a, b) => a.time - b.time);
              for (const message of messages) {
                const messageObject = JSON.parse(message.message);
                messageString = messageString + `Time: ${new Date(message.time).toISOString()}, Type: ${message.type}, Message: ${JSON.stringify(messageObject,null,2)}\n`
              }
            try{
              allure.attachment(
                `${role} WebSocket Log`,
                messageString,
                'text/plain'
              )
              await page.video().saveAs(`./videos/${role}_video.mp4`);
      		  allure.attachment(`${role}_Video`, fs.readFileSync(`./videos/${role}_video.mp4`), 'video/mp4');
              }catch(e){
                console.log(`WebSocket Capture Log Error: ${e}`)
              }
          })
    }


    async saveVideoAndAttach(page,videoPath='./videos/video.mp4'){
      await page.video().saveAs(videoPath);
      allure.attachment('Video', fs.readFileSync(videoPath), 'video/mp4');
    }



}


