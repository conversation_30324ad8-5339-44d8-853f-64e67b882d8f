{"count": 28, "_embedded": {"cdrs": [{"id": "195cbbb9d34d6935b76e0b18ab4b8e05", "account_id": "637b656738e6007d9c4e004b", "user_id": "de84843cfd89553e45121175f5abd86a", "provider_account_id": "a069665db27b586d4f0e2d1dc6fb4d92", "provider_user_id": "de84843cfd89553e45121175f5abd86a", "call_id": "9501e4d8-3683-11f0-9da0-154ad4c5ea2e", "call_started_at": "2025-05-21 20:38:41.000000", "call_ended_at": "2025-05-21 20:38:45.000000", "interaction_id": "***********-9236a185", "other_leg_call_id": "33950413_124621583@***********", "call_direction": "outbound", "sip_from": "+<EMAIL>", "caller_id_name": "+***********", "caller_id_number": "+***********", "sip_to": "<EMAIL>", "callee_id_name": "<PERSON> jun", "callee_id_number": "+***********", "ringing_seconds": 4, "duration_seconds": 4, "billing_seconds": 0, "hangup_cause": "NO_USER_RESPONSE", "hangup_code": "sip:480", "call_end_cause": "NO_USER_RESPONSE", "user_call_direction": "inbound", "was_answered": false, "was_transferred": false, "is_conference": false, "is_call_forward": false, "timestamp": "2025-05-21 20:38:45.000000"}, {"id": "ba20df53f3310dc45c3e162cb2174997", "account_id": "637b656738e6007d9c4e004b", "user_id": "de84843cfd89553e45121175f5abd86a", "provider_account_id": "a069665db27b586d4f0e2d1dc6fb4d92", "provider_user_id": "de84843cfd89553e45121175f5abd86a", "call_id": "3a9d419a-3683-11f0-a655-79a97bb00a02", "call_started_at": "2025-05-21 20:36:10.000000", "call_ended_at": "2025-05-21 20:36:20.000000", "interaction_id": "***********-28f718b7", "other_leg_call_id": "40411885_66785716@***********", "call_direction": "outbound", "sip_from": "+<EMAIL>", "caller_id_name": "+***********", "caller_id_number": "+***********", "sip_to": "<EMAIL>", "callee_id_name": "<PERSON> jun", "callee_id_number": "+***********", "ringing_seconds": 0, "duration_seconds": 10, "billing_seconds": 10, "hangup_cause": "NORMAL_CLEARING", "hangup_code": "sip:200", "disposition": "ANSWER", "call_end_cause": "NORMAL_CLEARING", "user_call_direction": "inbound", "was_answered": true, "was_transferred": false, "is_conference": false, "is_call_forward": false, "timestamp": "2025-05-21 20:36:20.000000"}, {"id": "7c041192fd869bd9b5ec729d80735482", "account_id": "637b656738e6007d9c4e004b", "user_id": "de84843cfd89553e45121175f5abd86a", "provider_account_id": "a069665db27b586d4f0e2d1dc6fb4d92", "provider_user_id": "de84843cfd89553e45121175f5abd86a", "call_id": "030be3bc-3683-11f0-9222-89aa926d8eae", "call_started_at": "2025-05-21 20:34:36.000000", "call_ended_at": "2025-05-21 20:34:44.000000", "interaction_id": "***********-f7b311b4", "other_leg_call_id": "40674484_102452995@***********", "call_direction": "outbound", "sip_from": "+<EMAIL>", "caller_id_name": "+***********", "caller_id_number": "+***********", "sip_to": "<EMAIL>", "callee_id_name": "<PERSON> jun", "callee_id_number": "+***********", "ringing_seconds": 1, "duration_seconds": 8, "billing_seconds": 7, "hangup_cause": "NORMAL_CLEARING", "hangup_code": "sip:200", "disposition": "ANSWER", "call_end_cause": "NORMAL_CLEARING", "user_call_direction": "inbound", "was_answered": true, "was_transferred": false, "is_conference": false, "is_call_forward": false, "timestamp": "2025-05-21 20:34:44.000000"}, {"id": "43aa46547d651d31e91d3c404ea937ef", "account_id": "637b656738e6007d9c4e004b", "user_id": "de84843cfd89553e45121175f5abd86a", "provider_account_id": "a069665db27b586d4f0e2d1dc6fb4d92", "provider_user_id": "de84843cfd89553e45121175f5abd86a", "call_id": "d2401f96-3682-11f0-9d62-154ad4c5ea2e", "call_started_at": "2025-05-21 20:33:14.000000", "call_ended_at": "2025-05-21 20:33:22.000000", "interaction_id": "***********-61445cbe", "other_leg_call_id": "40542881_59618998@***********", "call_direction": "outbound", "sip_from": "+<EMAIL>", "caller_id_name": "+***********", "caller_id_number": "+***********", "sip_to": "<EMAIL>", "callee_id_name": "<PERSON> jun", "callee_id_number": "+***********", "ringing_seconds": -1, "duration_seconds": 8, "billing_seconds": 8, "hangup_cause": "NORMAL_CLEARING", "hangup_code": "sip:200", "disposition": "ANSWER", "call_end_cause": "NORMAL_CLEARING", "user_call_direction": "inbound", "was_answered": true, "was_transferred": false, "is_conference": false, "is_call_forward": false, "timestamp": "2025-05-21 20:33:22.000000"}, {"id": "ce71b014cc1ea5d289157bf89b4cc7ad", "account_id": "637b656738e6007d9c4e004b", "user_id": "de84843cfd89553e45121175f5abd86a", "provider_account_id": "a069665db27b586d4f0e2d1dc6fb4d92", "provider_user_id": "de84843cfd89553e45121175f5abd86a", "call_id": "7d97de8e-35ba-11f0-b631-154ad4c5ea2e", "call_started_at": "2025-05-20 20:39:13.000000", "call_ended_at": "2025-05-20 20:39:18.000000", "interaction_id": "***********-96e90e22", "other_leg_call_id": "37798738_113349362@***********", "call_direction": "outbound", "sip_from": "+<EMAIL>", "caller_id_name": "+***********", "caller_id_number": "+***********", "sip_to": "<EMAIL>", "callee_id_name": "<PERSON> jun", "callee_id_number": "+***********", "ringing_seconds": 5, "duration_seconds": 5, "billing_seconds": 0, "hangup_cause": "NO_USER_RESPONSE", "hangup_code": "sip:480", "call_end_cause": "NO_USER_RESPONSE", "user_call_direction": "inbound", "was_answered": false, "was_transferred": false, "is_conference": false, "is_call_forward": false, "timestamp": "2025-05-20 20:39:18.000000"}, {"id": "47586b4c7bc156e88d3f9cc20e797866", "account_id": "637b656738e6007d9c4e004b", "user_id": "de84843cfd89553e45121175f5abd86a", "provider_account_id": "a069665db27b586d4f0e2d1dc6fb4d92", "provider_user_id": "de84843cfd89553e45121175f5abd86a", "call_id": "24eac666-35ba-11f0-b613-154ad4c5ea2e", "call_started_at": "2025-05-20 20:36:44.000000", "call_ended_at": "2025-05-20 20:36:54.000000", "interaction_id": "***********-44042f26", "other_leg_call_id": "37928313_125279591@***********", "call_direction": "outbound", "sip_from": "+<EMAIL>", "caller_id_name": "+***********", "caller_id_number": "+***********", "sip_to": "<EMAIL>", "callee_id_name": "<PERSON> jun", "callee_id_number": "+***********", "ringing_seconds": 0, "duration_seconds": 10, "billing_seconds": 10, "hangup_cause": "NORMAL_CLEARING", "hangup_code": "sip:200", "disposition": "ANSWER", "call_end_cause": "NORMAL_CLEARING", "user_call_direction": "inbound", "was_answered": true, "was_transferred": false, "is_conference": false, "is_call_forward": false, "timestamp": "2025-05-20 20:36:54.000000"}, {"id": "1ed0a2f5bff20fe8d6072b77e196dcf6", "account_id": "637b656738e6007d9c4e004b", "user_id": "de84843cfd89553e45121175f5abd86a", "provider_account_id": "a069665db27b586d4f0e2d1dc6fb4d92", "provider_user_id": "de84843cfd89553e45121175f5abd86a", "call_id": "d6167328-35b9-11f0-b9e8-79a97bb00a02", "call_started_at": "2025-05-20 20:34:32.000000", "call_ended_at": "2025-05-20 20:34:39.000000", "interaction_id": "***********-754230e7", "other_leg_call_id": "38060947_109702675@***********", "call_direction": "outbound", "sip_from": "+<EMAIL>", "caller_id_name": "+***********", "caller_id_number": "+***********", "sip_to": "<EMAIL>", "callee_id_name": "<PERSON> jun", "callee_id_number": "+***********", "ringing_seconds": 0, "duration_seconds": 7, "billing_seconds": 7, "hangup_cause": "NORMAL_CLEARING", "hangup_code": "sip:200", "disposition": "ANSWER", "call_end_cause": "NORMAL_CLEARING", "user_call_direction": "inbound", "was_answered": true, "was_transferred": false, "is_conference": false, "is_call_forward": false, "timestamp": "2025-05-20 20:34:39.000000"}, {"id": "2dbe6032d01ec9270762bc1acfe88eab", "account_id": "637b656738e6007d9c4e004b", "user_id": "de84843cfd89553e45121175f5abd86a", "provider_account_id": "a069665db27b586d4f0e2d1dc6fb4d92", "provider_user_id": "de84843cfd89553e45121175f5abd86a", "call_id": "a4971564-35b9-11f0-b5f5-154ad4c5ea2e", "call_started_at": "2025-05-20 20:33:09.000000", "call_ended_at": "2025-05-20 20:33:17.000000", "interaction_id": "***********-76d9cf95", "other_leg_call_id": "38584458_133006887@***********", "call_direction": "outbound", "sip_from": "+<EMAIL>", "caller_id_name": "+***********", "caller_id_number": "+***********", "sip_to": "<EMAIL>", "callee_id_name": "<PERSON> jun", "callee_id_number": "+***********", "ringing_seconds": 0, "duration_seconds": 8, "billing_seconds": 8, "hangup_cause": "NORMAL_CLEARING", "hangup_code": "sip:200", "disposition": "ANSWER", "call_end_cause": "NORMAL_CLEARING", "user_call_direction": "inbound", "was_answered": true, "was_transferred": false, "is_conference": false, "is_call_forward": false, "timestamp": "2025-05-20 20:33:17.000000"}, {"id": "6a7ea95ab8fd6557f0b53b9df218297b", "account_id": "637b656738e6007d9c4e004b", "user_id": "de84843cfd89553e45121175f5abd86a", "provider_account_id": "a069665db27b586d4f0e2d1dc6fb4d92", "provider_user_id": "de84843cfd89553e45121175f5abd86a", "call_id": "1a009c04-34f1-11f0-93e3-79a97bb00a02", "call_started_at": "2025-05-19 20:37:37.000000", "call_ended_at": "2025-05-19 20:37:41.000000", "interaction_id": "***********-f02c6ce8", "other_leg_call_id": "35706791_132119391@***********", "call_direction": "outbound", "sip_from": "+<EMAIL>", "caller_id_name": "+***********", "caller_id_number": "+***********", "sip_to": "<EMAIL>", "callee_id_name": "<PERSON> jun", "callee_id_number": "+***********", "ringing_seconds": 4, "duration_seconds": 4, "billing_seconds": 0, "hangup_cause": "NO_USER_RESPONSE", "hangup_code": "sip:480", "call_end_cause": "NO_USER_RESPONSE", "user_call_direction": "inbound", "was_answered": false, "was_transferred": false, "is_conference": false, "is_call_forward": false, "timestamp": "2025-05-19 20:37:41.000000"}, {"id": "93c37a3c8563d833c534217887deddfd", "account_id": "637b656738e6007d9c4e004b", "user_id": "de84843cfd89553e45121175f5abd86a", "provider_account_id": "a069665db27b586d4f0e2d1dc6fb4d92", "provider_user_id": "de84843cfd89553e45121175f5abd86a", "call_id": "dcc2ca10-34f0-11f0-bd25-89aa926d8eae", "call_started_at": "2025-05-19 20:35:54.000000", "call_ended_at": "2025-05-19 20:36:05.000000", "interaction_id": "***********-********", "other_leg_call_id": "36492574_66910638@***********", "call_direction": "outbound", "sip_from": "+<EMAIL>", "caller_id_name": "+***********", "caller_id_number": "+***********", "sip_to": "<EMAIL>", "callee_id_name": "<PERSON> jun", "callee_id_number": "+***********", "ringing_seconds": 1, "duration_seconds": 11, "billing_seconds": 10, "hangup_cause": "NORMAL_CLEARING", "hangup_code": "sip:200", "disposition": "ANSWER", "call_end_cause": "NORMAL_CLEARING", "user_call_direction": "inbound", "was_answered": true, "was_transferred": false, "is_conference": false, "is_call_forward": false, "timestamp": "2025-05-19 20:36:05.000000"}, {"id": "209ca11ba805e4ebd769eaa6873feab5", "account_id": "637b656738e6007d9c4e004b", "user_id": "de84843cfd89553e45121175f5abd86a", "provider_account_id": "a069665db27b586d4f0e2d1dc6fb4d92", "provider_user_id": "de84843cfd89553e45121175f5abd86a", "call_id": "ab5346b2-34f0-11f0-93c5-79a97bb00a02", "call_started_at": "2025-05-19 20:34:31.000000", "call_ended_at": "2025-05-19 20:34:39.000000", "interaction_id": "***********-6eaf31bf", "other_leg_call_id": "36360630_100654674@***********", "call_direction": "outbound", "sip_from": "+<EMAIL>", "caller_id_name": "+***********", "caller_id_number": "+***********", "sip_to": "<EMAIL>", "callee_id_name": "<PERSON> jun", "callee_id_number": "+***********", "ringing_seconds": 0, "duration_seconds": 8, "billing_seconds": 7, "hangup_cause": "NORMAL_CLEARING", "hangup_code": "sip:200", "disposition": "ANSWER", "call_end_cause": "NORMAL_CLEARING", "user_call_direction": "inbound", "was_answered": true, "was_transferred": false, "is_conference": false, "is_call_forward": false, "timestamp": "2025-05-19 20:34:39.000000"}, {"id": "bdb7d5445f4f5c2ded9b6fd3f8b7d5c6", "account_id": "637b656738e6007d9c4e004b", "user_id": "de84843cfd89553e45121175f5abd86a", "provider_account_id": "a069665db27b586d4f0e2d1dc6fb4d92", "provider_user_id": "de84843cfd89553e45121175f5abd86a", "call_id": "********-34f0-11f0-90d8-154ad4c5ea2e", "call_started_at": "2025-05-19 20:33:08.000000", "call_ended_at": "2025-05-19 20:33:15.000000", "interaction_id": "***********-4675201e", "other_leg_call_id": "36623433_132869288@***********", "call_direction": "outbound", "sip_from": "+<EMAIL>", "caller_id_name": "+***********", "caller_id_number": "+***********", "sip_to": "<EMAIL>", "callee_id_name": "<PERSON> jun", "callee_id_number": "+***********", "ringing_seconds": 0, "duration_seconds": 7, "billing_seconds": 7, "hangup_cause": "NORMAL_CLEARING", "hangup_code": "sip:200", "disposition": "ANSWER", "call_end_cause": "NORMAL_CLEARING", "user_call_direction": "inbound", "was_answered": true, "was_transferred": false, "is_conference": false, "is_call_forward": false, "timestamp": "2025-05-19 20:33:15.000000"}, {"id": "477236134b77b38e2b5a35fb782a9196", "account_id": "637b656738e6007d9c4e004b", "user_id": "de84843cfd89553e45121175f5abd86a", "provider_account_id": "a069665db27b586d4f0e2d1dc6fb4d92", "provider_user_id": "de84843cfd89553e45121175f5abd86a", "call_id": "f69b2afa-3427-11f0-9a64-89aa926d8eae", "call_started_at": "2025-05-18 20:37:49.000000", "call_ended_at": "2025-05-18 20:37:52.000000", "interaction_id": "***********-de3457e1", "other_leg_call_id": "36062468_117295833@***********", "call_direction": "outbound", "sip_from": "+<EMAIL>", "caller_id_name": "+***********", "caller_id_number": "+***********", "sip_to": "<EMAIL>", "callee_id_name": "<PERSON> jun", "callee_id_number": "+***********", "ringing_seconds": 3, "duration_seconds": 3, "billing_seconds": 0, "hangup_cause": "NO_USER_RESPONSE", "hangup_code": "sip:480", "call_end_cause": "NO_USER_RESPONSE", "user_call_direction": "inbound", "was_answered": false, "was_transferred": false, "is_conference": false, "is_call_forward": false, "timestamp": "2025-05-18 20:37:52.000000"}, {"id": "ca475da7dc098b3169d3f4701740d425", "account_id": "637b656738e6007d9c4e004b", "user_id": "de84843cfd89553e45121175f5abd86a", "provider_account_id": "a069665db27b586d4f0e2d1dc6fb4d92", "provider_user_id": "de84843cfd89553e45121175f5abd86a", "call_id": "b5c41e2e-3427-11f0-aa6d-154ad4c5ea2e", "call_started_at": "2025-05-18 20:36:00.000000", "call_ended_at": "2025-05-18 20:36:10.000000", "interaction_id": "***********-3fabc3e5", "other_leg_call_id": "33881154_131962281@***********", "call_direction": "outbound", "sip_from": "+<EMAIL>", "caller_id_name": "+***********", "caller_id_number": "+***********", "sip_to": "<EMAIL>", "callee_id_name": "<PERSON> jun", "callee_id_number": "+***********", "ringing_seconds": 0, "duration_seconds": 10, "billing_seconds": 10, "hangup_cause": "NORMAL_CLEARING", "hangup_code": "sip:200", "disposition": "ANSWER", "call_end_cause": "NORMAL_CLEARING", "user_call_direction": "inbound", "was_answered": true, "was_transferred": false, "is_conference": false, "is_call_forward": false, "timestamp": "2025-05-18 20:36:10.000000"}, {"id": "4c43b6179db574c25f395164bd5500f3", "account_id": "637b656738e6007d9c4e004b", "user_id": "de84843cfd89553e45121175f5abd86a", "provider_account_id": "a069665db27b586d4f0e2d1dc6fb4d92", "provider_user_id": "de84843cfd89553e45121175f5abd86a", "call_id": "855dc74e-3427-11f0-9a46-89aa926d8eae", "call_started_at": "2025-05-18 20:34:39.000000", "call_ended_at": "2025-05-18 20:34:46.000000", "interaction_id": "***********-9abcfb85", "other_leg_call_id": "33748441_114241447@***********", "call_direction": "outbound", "sip_from": "+<EMAIL>", "caller_id_name": "+***********", "caller_id_number": "+***********", "sip_to": "<EMAIL>", "callee_id_name": "<PERSON> jun", "callee_id_number": "+***********", "ringing_seconds": 0, "duration_seconds": 7, "billing_seconds": 7, "hangup_cause": "NORMAL_CLEARING", "hangup_code": "sip:200", "disposition": "ANSWER", "call_end_cause": "NORMAL_CLEARING", "user_call_direction": "inbound", "was_answered": true, "was_transferred": false, "is_conference": false, "is_call_forward": false, "timestamp": "2025-05-18 20:34:46.000000"}, {"id": "b17cf91f8e023eb8a8e1e9789d17895a", "account_id": "637b656738e6007d9c4e004b", "user_id": "de84843cfd89553e45121175f5abd86a", "provider_account_id": "a069665db27b586d4f0e2d1dc6fb4d92", "provider_user_id": "de84843cfd89553e45121175f5abd86a", "call_id": "54afa680-3427-11f0-aa29-154ad4c5ea2e", "call_started_at": "2025-05-18 20:33:17.000000", "call_ended_at": "2025-05-18 20:33:25.000000", "interaction_id": "***********-db20fb8d", "other_leg_call_id": "33748430_117407481@***********", "call_direction": "outbound", "sip_from": "+<EMAIL>", "caller_id_name": "+***********", "caller_id_number": "+***********", "sip_to": "<EMAIL>", "callee_id_name": "<PERSON> jun", "callee_id_number": "+***********", "ringing_seconds": 0, "duration_seconds": 8, "billing_seconds": 8, "hangup_cause": "NORMAL_CLEARING", "hangup_code": "sip:200", "disposition": "ANSWER", "call_end_cause": "NORMAL_CLEARING", "user_call_direction": "inbound", "was_answered": true, "was_transferred": false, "is_conference": false, "is_call_forward": false, "timestamp": "2025-05-18 20:33:25.000000"}, {"id": "cc1991ed8404deee4a1fa4c376ac7812", "account_id": "637b656738e6007d9c4e004b", "user_id": "de84843cfd89553e45121175f5abd86a", "provider_account_id": "a069665db27b586d4f0e2d1dc6fb4d92", "provider_user_id": "de84843cfd89553e45121175f5abd86a", "call_id": "cb0b29e0-335e-11f0-8e38-154ad4c5ea2e", "call_started_at": "2025-05-17 20:37:47.000000", "call_ended_at": "2025-05-17 20:37:51.000000", "interaction_id": "***********-bf4fcb87", "other_leg_call_id": "16828695_131956943@***********", "call_direction": "outbound", "sip_from": "+<EMAIL>", "caller_id_name": "+***********", "caller_id_number": "+***********", "sip_to": "<EMAIL>", "callee_id_name": "<PERSON> jun", "callee_id_number": "+***********", "ringing_seconds": 4, "duration_seconds": 4, "billing_seconds": 0, "hangup_cause": "NO_USER_RESPONSE", "hangup_code": "sip:480", "call_end_cause": "NO_USER_RESPONSE", "user_call_direction": "inbound", "was_answered": false, "was_transferred": false, "is_conference": false, "is_call_forward": false, "timestamp": "2025-05-17 20:37:51.000000"}, {"id": "5fa18df683036d19a3930aaa1c850702", "account_id": "637b656738e6007d9c4e004b", "user_id": "de84843cfd89553e45121175f5abd86a", "provider_account_id": "a069665db27b586d4f0e2d1dc6fb4d92", "provider_user_id": "de84843cfd89553e45121175f5abd86a", "call_id": "8bc7a20e-335e-11f0-bb98-89aa926d8eae", "call_started_at": "2025-05-17 20:36:01.000000", "call_ended_at": "2025-05-17 20:36:11.000000", "interaction_id": "***********-1b21eba8", "other_leg_call_id": "17745380_116892780@***********", "call_direction": "outbound", "sip_from": "+<EMAIL>", "caller_id_name": "+***********", "caller_id_number": "+***********", "sip_to": "<EMAIL>", "callee_id_name": "<PERSON> jun", "callee_id_number": "+***********", "ringing_seconds": 0, "duration_seconds": 10, "billing_seconds": 10, "hangup_cause": "NORMAL_CLEARING", "hangup_code": "sip:200", "disposition": "ANSWER", "call_end_cause": "NORMAL_CLEARING", "user_call_direction": "inbound", "was_answered": true, "was_transferred": false, "is_conference": false, "is_call_forward": false, "timestamp": "2025-05-17 20:36:11.000000"}, {"id": "0a14a7bdc8565e62f9826ed5aa6f455c", "account_id": "637b656738e6007d9c4e004b", "user_id": "de84843cfd89553e45121175f5abd86a", "provider_account_id": "a069665db27b586d4f0e2d1dc6fb4d92", "provider_user_id": "de84843cfd89553e45121175f5abd86a", "call_id": "59c275f4-335e-11f0-bb7a-89aa926d8eae", "call_started_at": "2025-05-17 20:34:37.000000", "call_ended_at": "2025-05-17 20:34:44.000000", "interaction_id": "***********-3082cf6f", "other_leg_call_id": "17614732_116669225@***********", "call_direction": "outbound", "sip_from": "+<EMAIL>", "caller_id_name": "+***********", "caller_id_number": "+***********", "sip_to": "<EMAIL>", "callee_id_name": "<PERSON> jun", "callee_id_number": "+***********", "ringing_seconds": 0, "duration_seconds": 7, "billing_seconds": 7, "hangup_cause": "NORMAL_CLEARING", "hangup_code": "sip:200", "disposition": "ANSWER", "call_end_cause": "NORMAL_CLEARING", "user_call_direction": "inbound", "was_answered": true, "was_transferred": false, "is_conference": false, "is_call_forward": false, "timestamp": "2025-05-17 20:34:44.000000"}, {"id": "da5960d4521fb1902ed6e633ddde0cf3", "account_id": "637b656738e6007d9c4e004b", "user_id": "de84843cfd89553e45121175f5abd86a", "provider_account_id": "a069665db27b586d4f0e2d1dc6fb4d92", "provider_user_id": "de84843cfd89553e45121175f5abd86a", "call_id": "25ed060e-335e-11f0-bb5c-89aa926d8eae", "call_started_at": "2025-05-17 20:33:10.000000", "call_ended_at": "2025-05-17 20:33:18.000000", "interaction_id": "***********-816e3284", "other_leg_call_id": "19270633_134017489@***********", "call_direction": "outbound", "sip_from": "+<EMAIL>", "caller_id_name": "+***********", "caller_id_number": "+***********", "sip_to": "<EMAIL>", "callee_id_name": "<PERSON> jun", "callee_id_number": "+***********", "ringing_seconds": 0, "duration_seconds": 8, "billing_seconds": 8, "hangup_cause": "NORMAL_CLEARING", "hangup_code": "sip:200", "disposition": "ANSWER", "call_end_cause": "NORMAL_CLEARING", "user_call_direction": "inbound", "was_answered": true, "was_transferred": false, "is_conference": false, "is_call_forward": false, "timestamp": "2025-05-17 20:33:18.000000"}, {"id": "753fe1e620b3d49770c33f26ce2df60a", "account_id": "637b656738e6007d9c4e004b", "user_id": "de84843cfd89553e45121175f5abd86a", "provider_account_id": "a069665db27b586d4f0e2d1dc6fb4d92", "provider_user_id": "de84843cfd89553e45121175f5abd86a", "call_id": "a6655e90-3295-11f0-afe3-154ad4c5ea2e", "call_started_at": "2025-05-16 20:37:57.000000", "call_ended_at": "2025-05-16 20:38:01.000000", "interaction_id": "***********-399c3fe9", "other_leg_call_id": "16812567_127896738@***********", "call_direction": "outbound", "sip_from": "+<EMAIL>", "caller_id_name": "+***********", "caller_id_number": "+***********", "sip_to": "<EMAIL>", "callee_id_name": "<PERSON> jun", "callee_id_number": "+***********", "ringing_seconds": 4, "duration_seconds": 4, "billing_seconds": 0, "hangup_cause": "NO_USER_RESPONSE", "hangup_code": "sip:480", "call_end_cause": "NO_USER_RESPONSE", "user_call_direction": "inbound", "was_answered": false, "was_transferred": false, "is_conference": false, "is_call_forward": false, "timestamp": "2025-05-16 20:38:01.000000"}, {"id": "3e1693b2b1c0fcdda2186502402ae327", "account_id": "637b656738e6007d9c4e004b", "user_id": "de84843cfd89553e45121175f5abd86a", "provider_account_id": "a069665db27b586d4f0e2d1dc6fb4d92", "provider_user_id": "de84843cfd89553e45121175f5abd86a", "call_id": "617cc4a8-3295-11f0-b586-79a97bb00a02", "call_started_at": "2025-05-16 20:36:01.000000", "call_ended_at": "2025-05-16 20:36:12.000000", "interaction_id": "***********-54f64ebf", "other_leg_call_id": "17336130_132366778@***********", "call_direction": "outbound", "sip_from": "+<EMAIL>", "caller_id_name": "+***********", "caller_id_number": "+***********", "sip_to": "<EMAIL>", "callee_id_name": "<PERSON> jun", "callee_id_number": "+***********", "ringing_seconds": 0, "duration_seconds": 11, "billing_seconds": 11, "hangup_cause": "NORMAL_CLEARING", "hangup_code": "sip:200", "disposition": "ANSWER", "call_end_cause": "NORMAL_CLEARING", "user_call_direction": "inbound", "was_answered": true, "was_transferred": false, "is_conference": false, "is_call_forward": false, "timestamp": "2025-05-16 20:36:12.000000"}, {"id": "ac4e2e5eb81f1a7eae7d4befbcebdf86", "account_id": "637b656738e6007d9c4e004b", "user_id": "de84843cfd89553e45121175f5abd86a", "provider_account_id": "a069665db27b586d4f0e2d1dc6fb4d92", "provider_user_id": "de84843cfd89553e45121175f5abd86a", "call_id": "2c7f42b2-3295-11f0-af9f-154ad4c5ea2e", "call_started_at": "2025-05-16 20:34:32.000000", "call_ended_at": "2025-05-16 20:34:40.000000", "interaction_id": "***********-d561210d", "other_leg_call_id": "17466732_49337817@***********", "call_direction": "outbound", "sip_from": "+<EMAIL>", "caller_id_name": "+***********", "caller_id_number": "+***********", "sip_to": "<EMAIL>", "callee_id_name": "<PERSON> jun", "callee_id_number": "+***********", "ringing_seconds": 1, "duration_seconds": 8, "billing_seconds": 7, "hangup_cause": "NORMAL_CLEARING", "hangup_code": "sip:200", "disposition": "ANSWER", "call_end_cause": "NORMAL_CLEARING", "user_call_direction": "inbound", "was_answered": true, "was_transferred": false, "is_conference": false, "is_call_forward": false, "timestamp": "2025-05-16 20:34:40.000000"}, {"id": "3a67a823b1a2f1d6efc44e0c70762824", "account_id": "637b656738e6007d9c4e004b", "user_id": "de84843cfd89553e45121175f5abd86a", "provider_account_id": "a069665db27b586d4f0e2d1dc6fb4d92", "provider_user_id": "de84843cfd89553e45121175f5abd86a", "call_id": "fb685222-3294-11f0-b548-79a97bb00a02", "call_started_at": "2025-05-16 20:33:10.000000", "call_ended_at": "2025-05-16 20:33:19.000000", "interaction_id": "***********-36a6e2f2", "other_leg_call_id": "16942459_132085532@***********", "call_direction": "outbound", "sip_from": "+<EMAIL>", "caller_id_name": "+***********", "caller_id_number": "+***********", "sip_to": "<EMAIL>", "callee_id_name": "<PERSON> jun", "callee_id_number": "+***********", "ringing_seconds": 0, "duration_seconds": 9, "billing_seconds": 9, "hangup_cause": "NORMAL_CLEARING", "hangup_code": "sip:200", "disposition": "ANSWER", "call_end_cause": "NORMAL_CLEARING", "user_call_direction": "inbound", "was_answered": true, "was_transferred": false, "is_conference": false, "is_call_forward": false, "timestamp": "2025-05-16 20:33:19.000000"}, {"id": "a3dd5f021388e86e50f1a57086e56967", "account_id": "637b656738e6007d9c4e004b", "user_id": "de84843cfd89553e45121175f5abd86a", "provider_account_id": "a069665db27b586d4f0e2d1dc6fb4d92", "provider_user_id": "de84843cfd89553e45121175f5abd86a", "call_id": "74fd36c6-31cc-11f0-b832-89aa926d8eae", "call_started_at": "2025-05-15 20:37:45.000000", "call_ended_at": "2025-05-15 20:37:49.000000", "interaction_id": "***********-e9960933", "other_leg_call_id": "24041420_129474838@***********", "call_direction": "outbound", "sip_from": "+<EMAIL>", "caller_id_name": "+***********", "caller_id_number": "+***********", "sip_to": "<EMAIL>", "callee_id_name": "<PERSON> jun", "callee_id_number": "+***********", "ringing_seconds": 4, "duration_seconds": 4, "billing_seconds": 0, "hangup_cause": "NO_USER_RESPONSE", "hangup_code": "sip:480", "call_end_cause": "NO_USER_RESPONSE", "user_call_direction": "inbound", "was_answered": false, "was_transferred": false, "is_conference": false, "is_call_forward": false, "timestamp": "2025-05-15 20:37:49.000000"}, {"id": "bc6354a42b36412b4291b6369ec5ce05", "account_id": "637b656738e6007d9c4e004b", "user_id": "de84843cfd89553e45121175f5abd86a", "provider_account_id": "a069665db27b586d4f0e2d1dc6fb4d92", "provider_user_id": "de84843cfd89553e45121175f5abd86a", "call_id": "********-31cc-11f0-b814-89aa926d8eae", "call_started_at": "2025-05-15 20:36:00.000000", "call_ended_at": "2025-05-15 20:36:10.000000", "interaction_id": "***********-7e86729d", "other_leg_call_id": "24041407_79709445@***********", "call_direction": "outbound", "sip_from": "+<EMAIL>", "caller_id_name": "+***********", "caller_id_number": "+***********", "sip_to": "<EMAIL>", "callee_id_name": "<PERSON> jun", "callee_id_number": "+***********", "ringing_seconds": 0, "duration_seconds": 10, "billing_seconds": 10, "hangup_cause": "NORMAL_CLEARING", "hangup_code": "sip:200", "disposition": "ANSWER", "call_end_cause": "NORMAL_CLEARING", "user_call_direction": "inbound", "was_answered": true, "was_transferred": false, "is_conference": false, "is_call_forward": false, "timestamp": "2025-05-15 20:36:10.000000"}, {"id": "c32926b7ddb9f8027db8d7a45eb33833", "account_id": "637b656738e6007d9c4e004b", "user_id": "de84843cfd89553e45121175f5abd86a", "provider_account_id": "a069665db27b586d4f0e2d1dc6fb4d92", "provider_user_id": "de84843cfd89553e45121175f5abd86a", "call_id": "00b81088-31cc-11f0-907b-79a97bb00a02", "call_started_at": "2025-05-15 20:34:30.000000", "call_ended_at": "2025-05-15 20:34:37.000000", "interaction_id": "***********-ee523650", "other_leg_call_id": "23124727_132813884@***********", "call_direction": "outbound", "sip_from": "+<EMAIL>", "caller_id_name": "+***********", "caller_id_number": "+***********", "sip_to": "<EMAIL>", "callee_id_name": "<PERSON> jun", "callee_id_number": "+***********", "ringing_seconds": 0, "duration_seconds": 7, "billing_seconds": 7, "hangup_cause": "NORMAL_CLEARING", "hangup_code": "sip:200", "disposition": "ANSWER", "call_end_cause": "NORMAL_CLEARING", "user_call_direction": "inbound", "was_answered": true, "was_transferred": false, "is_conference": false, "is_call_forward": false, "timestamp": "2025-05-15 20:34:37.000000"}, {"id": "9deaf61f30f0e9b72e2ab8b5d7b5804a", "account_id": "637b656738e6007d9c4e004b", "user_id": "de84843cfd89553e45121175f5abd86a", "provider_account_id": "a069665db27b586d4f0e2d1dc6fb4d92", "provider_user_id": "de84843cfd89553e45121175f5abd86a", "call_id": "ccc8fc60-31cb-11f0-905d-79a97bb00a02", "call_started_at": "2025-05-15 20:33:03.000000", "call_ended_at": "2025-05-15 20:33:09.000000", "interaction_id": "***********-8bea9e36", "other_leg_call_id": "23778706_102736416@***********", "call_direction": "outbound", "sip_from": "+<EMAIL>", "caller_id_name": "+***********", "caller_id_number": "+***********", "sip_to": "<EMAIL>", "callee_id_name": "<PERSON> jun", "callee_id_number": "+***********", "ringing_seconds": -1, "duration_seconds": 6, "billing_seconds": 7, "hangup_cause": "NORMAL_CLEARING", "hangup_code": "sip:200", "disposition": "ANSWER", "call_end_cause": "NORMAL_CLEARING", "user_call_direction": "inbound", "was_answered": true, "was_transferred": false, "is_conference": false, "is_call_forward": false, "timestamp": "2025-05-15 20:33:09.000000"}]}, "_links": {"self": {"href": "https://api.talkdeskapp.eu/enterprise-voice/users/call-history"}}}