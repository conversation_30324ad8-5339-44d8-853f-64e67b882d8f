{"total": 6, "total_pages": 1, "page": 1, "per_page": 10, "count": 6, "_embedded": {"labels": [{"id": 1, "type": "HOME", "default_name": "Home", "name": "Home"}, {"id": 2, "type": "MOBILE", "default_name": "Mobile", "name": "Mobile"}, {"id": 3, "type": "OFFICE", "default_name": "Office", "name": "Office"}, {"id": 4, "type": "EXTRA_1", "default_name": "Extra 1", "name": "Extra 1"}, {"id": 5, "type": "EXTRA_2", "default_name": "Extra 2", "name": "Extra 2"}, {"id": 6, "type": "EXTRA_3", "default_name": "Extra 3", "name": "Extra 3"}]}, "_links": {"self": {"href": "https://api.talkdeskapp.eu/phone-labels?per_page=10&page=1"}, "first": {"href": "https://api.talkdeskapp.eu/phone-labels?per_page=10&page=1"}, "last": {"href": "https://api.talkdeskapp.eu/phone-labels?per_page=10&page=1"}}, "labels": [{"id": 1, "type": "HOME", "default_name": "Home", "name": "Home"}, {"id": 2, "type": "MOBILE", "default_name": "Mobile", "name": "Mobile"}, {"id": 3, "type": "OFFICE", "default_name": "Office", "name": "Office"}, {"id": 4, "type": "EXTRA_1", "default_name": "Extra 1", "name": "Extra 1"}, {"id": 5, "type": "EXTRA_2", "default_name": "Extra 2", "name": "Extra 2"}, {"id": 6, "type": "EXTRA_3", "default_name": "Extra 3", "name": "Extra 3"}]}