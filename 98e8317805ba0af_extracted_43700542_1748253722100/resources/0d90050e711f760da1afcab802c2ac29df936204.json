{"total": 4, "count": 4, "per_page": 10, "page": 1, "_embedded": {"phone_numbers": [{"id": "6387029bf22b9f00179c76c4", "phone_number": "+15617821431", "sip": false, "vcid": false, "friendly_name": "for SIP used", "capabilities": ["voice", "sms", "mms"], "dedicated_line": false, "dedicated_line_agent_id": null}, {"id": "63f70bb1f13bc800171810e5", "phone_number": "+12762779712", "sip": false, "vcid": false, "friendly_name": "Metoto Automation test use for phone app", "capabilities": ["voice", "sms", "mms"], "dedicated_line": false, "dedicated_line_agent_id": null}, {"id": "6386f2843501690015eb20d5", "phone_number": "+17175395095", "sip": false, "vcid": false, "friendly_name": "Metoto Automation used", "capabilities": ["voice", "sms", "mms"], "dedicated_line": false, "dedicated_line_agent_id": null}, {"id": "640ee83671b7ec001504ebcc", "phone_number": "+15075287681", "sip": false, "vcid": false, "friendly_name": "<PERSON>", "capabilities": ["voice", "sms", "mms"], "dedicated_line": false, "dedicated_line_agent_id": null}]}, "_links": {"first": {"href": "https://api.talkdeskapp.eu/phone-details/numbers?per_page=10&page=1"}, "self": {"href": "https://api.talkdeskapp.eu/phone-details/numbers?per_page=10&page=1"}, "last": {"href": "https://api.talkdeskapp.eu/phone-details/numbers?per_page=10&page=1"}}}