{"keypad": "Keypad", "callerId": "Caller ID:", "call": "Call", "incomingCall": "Call from", "dialing": "Calling...", "noAnswer": "No answer", "userBusy": "User busy", "speaking": "Speaking", "accept": "Accept", "decline": "Decline", "endCall": "End call", "transferCall": "Transfer call", "connectingServer": "Loading...", "typeNumber": "Type number", "resetKeypadNumberErrorTitle": "Error", "resetKeypadNumberErrorMessage": "Error resetting phone number.", "noServerConnection": "No server connection", "noServerConnectionMessage": "We can't connect to the server. Please wait a few minutes. If the problem persists, contact your system administrator", "noNetworkConnection": "No internet connection", "noNetworkConnectionMessage": "Please check your internet connection and try again.", "noNetworkConnectionToastTitle": "Connection lost", "noNetworkConnectionToastMessage": "Call was dropped because there’s no internet connection.", "networkProblemsToastMessage": "Your internet connection is unstable", "callFailedTitle": "Invalid number", "callFailedMessage1": "The number you dialed doesn't exist.", "callFailedMessage2": "Please check your number and try again.", "confirmButton": "Got it", "mute": "Mute", "unmute": "Unmute", "muteError": "Couldn't mute", "unmuteError": "Couldn't unmute", "callAgain": "Call again", "cancel": "Cancel", "hold": "Hold", "unhold": "Unhold", "onHold": "On hold", "holdError": "Couldn't hold", "unholdError": "Couldn't unhold", "unexpectedError": "An unexpected error occurred", "sendDTMFError": "There was an error sending DTMF code", "transfer": "Transfer", "callForwardEmergencyError": "You cannot forward calls to the Emergency services (X112, X911, X933, X988 or X999)", "callForwardOwnError": "You cannot forward calls to yourself", "transferTo911Error": "You cannot transfer calls to the Emergency services (X112, X911, X933, X988 or X999)", "transferCallErrorTitle": "Unable to transfer call", "transferCallErrorMessage": "Please try again or select another person", "transferCallSuccess": "Call transferred successfully", "userExtension": "Ext:", "privateNumber": "Private number", "personalNumbersList": "Personal numbers", "ringGroupNumbersList": "Ring Group numbers", "companyNumbersList": "Company numbers", "setCallerIdErrorToastTitle": "Something went wrong", "setCallerIdErrorToastMessage": "We were unable to save the changes made, try again.", "getCallerIdListErrorToastTitle": "Something went wrong", "getCallerIdListErrorToastMessage": "We were unable to load all available caller IDs", "callerIdDropdownErrorMessage": "Couldn't load available caller IDs", "callerIdDropdownLoadingMessage": "Loading...", "callerIdDropdownNoNumbersAssigned": "No numbers assigned", "callerIdDropdownNoResultsFound": "No results found", "addCallControlButton": "Add", "addCallDialpadButton": "Add participant", "conferenceHeader": "Conference", "conferenceDescription": "Multiple calls", "endConference": "End conference", "ringing": "Ringing...", "conferenceConnected": "Connected", "disconnecting": "Disconnecting...", "participantMuted": "Muted", "participants": "participants", "participant": "participant", "newCallControlButton": "New", "newCallDialpadButton": "New call", "endAndAccept": "End & Accept", "holdAndAccept": "Hold & Accept", "incomingConference": "Incoming conference", "conferenceCallPromoted": "Call promoted to conference by", "leaveConference": "Leave conference", "merge": "<PERSON><PERSON>", "leaveAndAccept": "Leave & Accept", "disconnect": "Disconnect", "incomingConferenceCall": "Conference call from", "endAllAndAccept": "End All & Accept", "backToCall": "Back to call", "backToConference": "Back to conference", "callFrom": "Call from", "labelToggleScreensButton": {"keypad": "Keypad", "contacts": "Contacts"}, "contactsSearch": {"hint": "Type at least 3 characters to start searching", "placeholder": "Search by name or number"}, "noContactsResults": "Please make sure you typed the contact’s name or phone number correctly.", "contactSingular": "Contact", "contactsPlural": "Contacts", "contactsButtonTooltip": {"call": "Call", "addParticipant": "Add participant", "newCall": "New call", "transferCall": "Transfer call"}, "shortcutsListSidePanel": {"title": "Shortcuts list", "description": "Voicemails shortcuts to dial on keypad", "checkYourOwnVoicemailTitle": "Check your own voicemail", "checkYourOwnVoicemailDescription": "Dial your extension", "checkOtherUsersVoicemailTitle": "Check other user's voicemail", "checkOtherUsersVoicemailDescription": "Dial *97 (+ user extension)", "sendVoicemailDirectlyToUserTitle": "Send a voicemail directly to a user", "sendVoicemailDirectlyToUserDescription": "Dial ** (+ extension of recipient)"}, "voicemailCard": {"title": "You have new voicemails", "description": "Call voicemail to listen", "noVoiceMailTitle": "You don't have new voicemails", "noVoiceMailDescription": "Go to voicemail to listen to saved voicemails", "optionShortcutsList": "Voicemails shortcuts", "optionGoToVoicemail": "Call voicemail"}, "voicemail": "Voicemail", "emergencyNumber": "Emergency call", "emergencyTestNumber": "Emergency test call", "callHistory": {"all": "All", "calls": "Calls", "conference": "Conference call", "recentCalls": "Recent calls", "invalidDate": "Invalid date", "lastUpdated": "Last updated:", "listMissedCall": "Missed · ", "listUnansweredCall": "Unanswered · ", "listTransferredCall": "Transferred · ", "forwardCall": "Forwarded · ", "loading": "Loading...", "loadMore": "Load more...", "missedCalls": "Missed", "noHistoryYetTitle": "Your recent calls will appear here", "refresh": "Refresh", "today": "Today, ", "yesterday": "Yesterday, "}, "emergencyBanner": {"info": "Emergency information missing", "addInformation": "Please update your ", "linkMessage": "Emergency settings"}, "default": "<PERSON><PERSON><PERSON>", "incomingCallNotification": "Incoming call", "incomingConferenceNotification": "Incoming conference call", "incomingAnonymousNotification": "No caller ID", "refresh": "Refresh"}