{"new_app.bookmarks.page_title": "Bookmarks", "new_app.empty_state.transcriptions.title": "Call transcript", "messages_bookmark_delete_error": "Could not remove bookmark", "summary": "Summary", "kBExternalSources.sharepointv3": "Sharepoint", "kBExternalSources.sharepointv2": "Sharepoint", "kBExternalSources.sharepoint": "Sharepoint", "kBExternalSources.googledrive": "Google Drive", "kBExternalSources.intercom": "Intercom", "generic.contact": "Contact", "updated_on": "Updated on {updatedAt}", "wrap_up.label": "Wrap-up", "copied_to_clipboard_success": "Copied to clipboard", "summaryOnDemandTooltip": "Summarize every 20 seconds what happened until now", "regenerate": "Regenerate", "secondsAgo": "a few seconds ago", "summaryCreated": "Summary created", "summaryNotCreated": "Summary not created", "pleaseTryAgainLater": "Please try again later.", "hoursAgo": "{hours} hours ago", "action_buttons_search": "Search", "search.heading": "Search on Agent Assist", "top_searches_empty_msg": "Start searching now", "date.abbrWeek.fri": "<PERSON><PERSON>", "automation.confirm": "Confirm", "get_started_card_ai_models_title": "Empower Copilot with AI", "date.abbrWeek.mon": "Mon", "date.abbrWeek.sat": "Sat", "empty_state.bookmarks.description": "Bookmark recommendations to quickly access them at any time", "date.months.sep": "September", "kBExternalSources.fileUpload": "File Upload", "teaser_upgrade_request_success_title": "Upgrade request sent", "kBExternalSources.webcrawlerv3": "Web Crawler", "empty_state_error_message": "There was a problem loading this page. Please refresh or contact Talkdesk for support", "kBExternalSources.webcrawlerv2": "Web Crawler", "transcription.teaser.description": "With live call transcriptions, you will never miss a detail of the conversation.<br/>Our AI will <strong>highlight key customer information</strong> and allow you to <strong>copy or save for later.</strong>", "viewer_empty_state_message": "This automation does not include a flow that can be used in Agent Assist", "gpt_answers_tooltip": "Copilot identifies unanswered customer queries and writes an answer based on your company's Knowledge Base content.", "footer.search": "Search", "messages.websocketReconnected": "Reconnected to server", "pinned.copyAllPinnedButton": "<PERSON>py all pinned messages", "empty_state_error_alt_image": "No elements were found (img)", "get_started_card_voice_transcription_title": "Enable voice transcription of customer interactions", "messages.feedbackError": "Something went wrong while submitting your feedback. Please try again.", "generic.recommendation": "Recommendation", "empty_state_bookmarks.title": "No bookmarks yet", "search.noResults": "No results found for", "transcription_teaser_title": "Never ask the same question twice!", "viewer_summary_title": "Interaction Summary", "tip_feedback_title": "Copilot needs your feedback", "messages.automationError": "Something went wrong while submitting your action. Please try again.", "pinned.pinTooltip": "Pin message", "pinned.showSidePanelButton": "Pinned messages", "pinned.sidePanelTitle": "Pinned messages", "empty_state_no_content_alt_image": "No content configured", "teaser.upgrade.request.title": "Request Upgrade", "empty_state.recommendations.description": "Agent Assist will recommend the most relevant content or actions during a conversation so you can help your customers faster", "teaser.upgrade.request.success_title": "Upgrade request sent", "wrapUp.copyPinnedMessages": "<PERSON><PERSON> pinned messages", "add_to_notes_error_description": "Sorry! The Notes widget is currently unavailable.", "teaser.alt_image": "Request Upgrade", "tip_agent_assist_description": "Copilot interprets your conversations with customers in real time and automatically looks for the best answer to each question.", "quick_answer_title": "Quick Answer for {intent}", "recomendationType.automation": "Automation", "noIntent": "No intent match", "emptyState.error.refresh": "Refresh", "recomendationType.answer": "Answer", "date.months.mar": "March", "date.months.may": "May", "automation.next": "Next", "date_months_mar": "March", "recommendation_type.answer": "Answer", "teaser_upgrade_request_success_btn": "Keep searching", "generic_provider": "default", "transcription.teaser.title": "Never ask the same question twice!", "search_action_tooltip": "Search this message", "date_months_sep": "September", "interaction_disposition_title": "Disposition", "slider_first": "First", "emptyState.timeline.title": "Timeline of events", "search.placeholder": "Search...", "viewer_empty_state_title": "Invalid Flow", "wrapUp.callEnded": "Call ended", "date.abbrWeek.sun": "Sun", "navbar.recommendations": "Recommendations", "emptyState.highlights.title": "Highlights of events", "summary_title": "Interaction summary", "bookmarks.remove_bookmark": "Unbookmark", "empty_state.bookmarks.title": "No bookmarks yet", "messages_flow_step_error": "Could not communicate with server", "top_intents_recommendations": "Top recommended article", "top_intents_error": "Could not retrieve recommended articles", "bookmarks_automations_title": "Automations", "teaser_upgrade_request_success_message": "We'll reach out to you soon", "search.ariaLabel": "Search by term", "gpt_answers_dismiss": "<PERSON><PERSON><PERSON>", "search.updated": "Updated {updatedAt}", "pinned.show_side_panel_button": "Pinned messages", "date.abbr_week.wed": "Wed", "empty_state.recommendations.title": "Recommended content and actions", "pinned.side_panel_title": "Pinned messages", "smart_scripts_page_title": "Smart scripts", "messages.connection_error": "Something went wrong", "viewer_default_title": "Automation", "add_template_to_message": "Add to message", "messages_bookmark_cannot_add_deleted": "This content was deleted and cannot be bookmarked", "viewer_empty_or_invalid_flow_title": "Could not load smart script step", "navbar.search": "Search", "exportModal.or": "OR", "recommendations_widget_title": "Recommendations", "action_buttons_restore": "Rest<PERSON>", "smart_scripts_recommended_title": "Recommended", "slider_suggestions": "Suggestions", "emptyState.error.altImage": "No elements were found (img)", "emptyState.highlights.searching": "Searching for highlights...", "empty_state.no_content.title": "Fill up your box!", "export_modal.copy_to_clipboard": "Copy to clipboard", "date.abbr_week.sat": "Sat", "pinned.copy_all_pinned_button": "<PERSON>py all pinned messages", "recommendation_type.article": "Article", "messages_get_automation_error": "Could not retrieve automation {name}", "empty_state_bookmarks.description": "Bookmark answers and access them here", "new_app.search.ariaLabel": "Search by term", "article.updated": "Updated", "date.abbrWeek.thu": "<PERSON>hu", "messages.bookmark_create_error": "Failed to add bookmark", "bookmarks_answers_title": "Answers", "generic_agent": "Agent", "messages_connection_error": "Something went wrong", "interaction_summary_title": "Summary", "navbar.transcription": "Transcription", "highlights_widget_title": "Highlights", "date.abbr_week.fri": "<PERSON><PERSON>", "date.abbr_week.mon": "Mon", "search.need_help": "Do you need help?", "new_app.search_placeholder": "Search your knowledge base", "search.initial.show_all": "Show all", "search.initial.subtitle": "Try these suggestions", "teaser.upgrade.request.success_btn": "Keep searching", "ai_generated_try_again": "Try again", "empty_state_transcriptions_searching": "Waiting for the conversation to start...", "highlights_widget_sub_title": "Main topics of the conversation", "recommendations_widget_sub_title": "{count} suggested in total", "action_buttons_expand": "Expand", "date_months_may": "May", "messages.bookmark_list_error": "Failed to retrieve bookmarks", "date_months_nov": "November", "search_need_help": "Do you need help?", "bookmarks_deleted_tag": "Deleted", "interaction_next_actions_title": "Agent next steps", "transcription_widget_sub_title": "{duration} call", "add_to_notes": "Add to Notes", "action_buttons_download": "Download .txt", "warning_page_title": "Get Started", "websocket_parsing_error": "Failed to process server response", "generic_app_knowledge_management": "Knowledge Management", "empty_state_no_content_title": "Fill up your box!", "empty_state_error_refresh": "Refresh", "generic_app_launchpad": "AI Launchpad", "smart_scripts_recent_title": "Recent", "action_buttons_summarize": "Summarize", "smart_scripts_placeholder_image_alt": "Placeholder image for empty scripts list", "close": "Close", "search_welcome": "Welcome to Copilot", "go_back": "Back", "rag_answer": "Generated Answer", "ai_generated_feedback_negative": "No", "ai_generated_feedback_positive": "Yes", "ai_generated_feedback_thank_you_message": "Thank you for the feedback!", "ai_generated_sources": "Sources", "on_maintenance_title": "Copilot is currently down for maintenance", "on_maintenance_description": "We expect to be back in a couple hours. Thank you for your patience", "warning_page_subtitle": "Follow the recommended steps to start using Copilot", "footer.transcription": "Transcription", "automation_next": "Next", "automation_placeholder": "Insert {label}", "minutesAgo": "{minutes} min ago", "date.months.feb": "February", "export_modal.export_transcription": "Export transcription", "navbar.highlights": "Highlights", "messages.bookmark_delete_error": "Failed to remove bookmark", "get_started_skip_btn": "Skip for now", "top_intents_sub_title": "See the best answer to each trending topic", "bookmarks.add_bookmark": "Bookmark", "date.abbrWeek.wed": "Wed", "exportModal.includeTimestamps": "Include timestamps", "date.months.jan": "January", "viewer_summary_error": "There was an issue communicating with the summarization service.", "date.months.jul": "July", "date_months_apr": "April", "export_modal.or": "OR", "pinned.pin_tooltip": "Pin message", "date.abbr_week.sun": "Sun", "date.months.jun": "June", "add_to_disposition": "Add to Disposition", "date.months.oct": "October", "emptyState.recommendations.searching": "Searching for recommendations...", "emptyState.timeline.searching": "Searching for highlights...", "synced_on": "Synced on {updatedAt}", "recommendation_type.automation": "Automation", "empty_state_digital_description": "Copilot is looking for the best tips to help you during your interaction with the customer", "copiedToClipboardSuccess": "Copied to clipboard", "date.months.aug": "August", "date.months.dec": "December", "tip_bookmark_description": "Bookmark relevant answers and articles to easily access them at any time, without the need to search or ask someone again.", "knowledge_management": "Knowledge Management", "smart_scripts_placeholder_text": "Real-time smart scripts recommendations will appear here, as well as scripts from the last interaction", "generic_language": "configured", "messages.connectionError": "Something went wrong", "messages.websocketClosed": "Lost connection to server", "tip_feedback_description": "Use the thumbs up and down buttons to let us know if a recommendation was useful or not. Our algorithms are always learning, and your contribution is precious.", "no_answer_recommendations_available_warning": "There is no available answer. Use the recommendations available, or try again later.", "kBExternalSources.webcrawler": "Web Crawler", "search.teaser.description": "Request the full version of Agent Assist and get the answers you are looking for when you most need them, with zero manual effort.", "empty_state_no_content_note": "Reach out to your manager to start creating content!", "search_initial_show_all": "Show all", "search_initial_subtitle": "Try these suggestions", "search_initial_title": "Don't know how to start?", "gpt_answers_fallback_results": "It wasn’t possible to generate an answer but these articles might help you:", "teaser.upgrade.admin": "Upgrade your account to access this feature!", "get_started_pending_tasks_btn": "See pending tasks", "ai_fact_check_explanation": "Fact-check before answering", "insert_value": "Insert value", "slider_last": "{count, plural, =0 {Latest} other {Latest (+{count})}}", "copy_message_tooltip": "Copy to clipboard", "pinned.numberPinnedMessages": "{count, plural, =1 {1 pinned message} other {{count} pinned messages}}", "pinned.unpinTooltip": "Unpin message", "call_accepted": "{<PERSON><PERSON><PERSON>} accepted call ⋅ {time}", "transcription_search_keywords": "Search for keywords...", "ai_generated_title_tooltip": "AI-generated from transcription", "summary_failed_to_load_next_actions": "Not able to provide next actions", "highlights.newEntries": "New Entries", "recommendations.seeInfo": "See more information", "messages_bookmark_create_error": "Could not add bookmark", "bookmarks_add_bookmark": "Bookmark", "feedback_message": "Was this helpful?", "automations_source": "Automation Designer", "bookmarks_knowledge_title": "Knowledge", "source_open_url": "Open in {source}", "emptyState.transcriptions.title": "Call transcript", "emptyState.recommendations.title": "Recommended content and actions", "messages_automations_list_error": "Cannot retrieve automations", "automation.placeholder": "Insert {label}", "generic_app": "Copilot", "generate_answer_error": "Couldn’t generate answer", "gpt_answers_fetch_article_error": "Unable to fetch the article", "emptyState.timeline.description": "Agent <PERSON><PERSON> will show you a timeline with the important moments and information from the conversation", "emptyState.transcriptions.description": "During a call Agent <PERSON><PERSON> will show you the full transcript of the conversation with the customer", "exportModal.copyToClipboard": "Copy to clipboard", "search.teaser.title": "Tired of searching?", "export_modal.download_message": "The download is starting, if you do not see it check your browser permissions", "export_modal.include_timestamps": "Include timestamps", "highlights.new_entries": "New Entries", "highlights.teaser.description": "Agent Assist will work on the background to <strong>create a live summary of the conversation</strong>, with the <strong>best answers</strong> for each customer query being displayed as you go.<br><strong>No more manual search or jumping between apps.</strong>", "viewer_empty_or_invalid_flow": "The content is either missing or not properly configured, check with your conversation designer", "transcription_widget_title": "Transcription", "add_to_notes_error_title": "Couldn't add message to Notes", "plus": "Plus", "highlights.teaser.title": "Focus on your customer!", "search_results_count": "{count, plural, =1 {<strong>1 Result</strong> from your sources} other {<strong>{count} Results</strong> from your sources}}", "empty_state_error_title": "Something went wrong", "export_modal.download_file": "Download file (.txt)", "teaser.upgrade.agent": "Reach out your administrator to upgrade your account!", "feedback_error": "Something went wrong while submitting your feedback. Please try again.", "new_app.messages.bookmark_list_error": "Cannot retrieve bookmarks", "ai_generated_btn": "Generate with AI", "tip_agent_assist_title": "Resolve customer issues quickly", "search.no_results": "No search results for <strong>{searchTerm}</strong>", "bookmarks.quick_answer_title": "Quick Answer for <strong>{intent}</strong>", "search.initial.title": "Don't know how to start?", "empty_state.no_content.alt_image": "No content configured", "emptyState.error.title": "Something went wrong", "pinned.unpin_tooltip": "Unpin message", "wrap_up.call_ended": "Call ended", "wrap_up.copy_pinned_messages": "<PERSON><PERSON> pinned messages", "empty_state.highlights.title": "Highlights of events", "empty_state.no_content.btn_label": "Go to Knowledge Management", "exportModal.downloadMessage": "The download is starting, if you do not see it check your browser permissions", "teaser.upgrade": "Reach out your administrator to upgrade your account!", "teaser.upgrade.request": "Request Upgrade", "teaser.upgrade.request.error_message": "We cannot process your request.<br>Please, try again in a few seconds", "search.description": "Search for useful content to assist you during your interaction with the customer", "teaser.upgrade.request.success_message": "We'll reach out to you soon", "recommendations.quickAnswer": "Quick Answer", "virtual_agent": "Virtual Agent", "websocket_reconnected": "Agent Assist reconnected to the server", "search_teaser_title": "Tired of searching?", "ai_generated_error": "Couldn’t generate with AI", "ai_generated_send_action": "Add to Disposition & Notes", "ai_generated_title": "Disposition and Summary", "generate_answer": "Help me answer", "wrap_up.export_transcription": "Export transcription", "generic.app": "Agent Assist", "messages.websocketParsingError": "Failed to process server response", "footer.timeline": "Timeline", "top_intents_empty_recommendations": "There are no recommended articles for this topic in your knowledge base. If you need help handling this topic, please ask your manager.", "websocket_call_event_unsupported_language": "The configured service does not support the \"{language}\" value in the language configuration.", "answers_content_widget_title": "Answers & content", "teaser_upgrade_admin": "Upgrade your account to access this feature!", "websocket_failure": "Copilot lost connection to the server", "date.abbrWeek.tue": "<PERSON><PERSON>", "ai_fact_check_label": "AI-generated answer", "generic.agent": "Agent", "generic_contact": "Contact", "footer.recommendations": "Recommendations", "websocket_cannot_connect": "Agent <PERSON><PERSON> could not connect to the server", "emptyState.error.message": "There was a problem loading this page. Please refresh or contact Talkdesk for support", "no_answer_available_warning": "There is no answer available. Try again later.", "source_open_url_fallback": "Open in knowledge base", "talkdesk": "Talkdesk", "empty_state_digital_title": "Searching for recommendations...", "ai_generated_feedback_title": "Was this helpful?", "search.welcome": "Welcome to Agent <PERSON>!", "generic.recommendations": "Recommendations", "summary_failed_to_load_disposition": "Not able to provide disposition", "kBExternalSources.c2Perform": "C2Perform", "recommendations_filters_answers": "{count, plural, =1 {1 Answer} other {{count} Answers}}", "transcription_new_message": "New message", "kBInternalKnowledge": "Internal Knowledge", "kBExternalSources.confluence": "Confluence", "recommendations_filters_articles": "{count} Content", "messages.feedback_error": "Something went wrong while submitting your feedback. Please try again.", "messages.websocket_closed": "Lost connection to server", "messages.websocket_parsing_error": "Failed to process server response", "messages.websocket_reconnected": "Reconnected to server", "pinned.number_pinned_messages": "{count, plural, =1 {1 pinned message} other {{count} pinned messages}}", "tip_knowledge_title": "The knowledge you need is right here", "emptyState.recommendations.description": "Agent Assist will recommend the most relevant content or actions during a conversation so you can help your customers faster", "slider_placeholder": "Searching for recommendations...", "messages_smart_scripts_list_error": "Cannot retrieve smart scripts", "smart_script_item_name": "Smart script for {name}", "smart_scripts_btn_tooltip": "See smart scripts", "empty_state_no_content_btn_label": "Go to Knowledge Management", "navbar_transcription": "Transcription", "date.abbr_week.thu": "<PERSON>hu", "date.abbr_week.tue": "<PERSON><PERSON>", "wrapUp.exportTranscription": "Export transcription", "date.months.nov": "November", "copyMessageTooltip": "Copy to clipboard", "recommendations.readMore": "Read more", "messages_bookmark_list_error": "Cannot retrieve bookmarks", "search_ariaLabel": "Search by term", "article.header": "Go back", "emptyState.highlights.description": "Agent <PERSON><PERSON> will show you a timeline with the important moments and information from the conversation", "empty_state_transcriptions_description": "During a call Agent <PERSON><PERSON> will show you the full transcript of the conversation with the customer", "search_placeholder": "Search your knowledge base", "bookmarks_btn_tooltip": "See bookmarks", "bookmarks_page_title": "Bookmarks", "empty_state_transcriptions_title": "Call transcript", "empty_state.recommendations.searching": "Searching for recommendations...", "empty_state.transcriptions.description": "During a call Agent <PERSON><PERSON> will show you the full transcript of the conversation with the customer", "empty_state.transcriptions.title": "Call transcript", "messages.automation_error": "Something went wrong while submitting your action. Please try again.", "new_app.empty_state.transcriptions.description": "During a call Agent <PERSON><PERSON> will show you the full transcript of the conversation with the customer", "highlights.teaser.description.text": "Agent Assist will work on the background to <strong>create a live summary of the conversation</strong>, with the <strong>best answers</strong> for each customer query being displayed as you go.<br/><strong>No more manual search or jumping between apps.</strong>", "top_searches_title": "Most frequent searches", "transcription_teaser_description": "With live call transcriptions, you will never miss a detail of the conversation.<br>Our AI will <strong>highlight key customer information</strong> and allow you to <strong>copy or save for later.</strong>", "websocket_call_event_no_config": "Missing configurations in Launchpad", "get_started_card_ai_models_subtitle": "Enable AI Models to automate your contact center performance", "top_intents_title": "Trending topics", "automations_item_name": "Smart script for {name}", "quick_replies_next_btn": "Next", "kBExternalSources.remoteFile": "Remote File", "kBExternalSources.salesforce": "Salesforce", "kBExternalSources.serviceNow": "ServiceNow", "kBExternalSources.zendeskGuide": "Zendesk Guide", "smart_scripts_placeholder_title": "No recent smart scripts", "empty_state.highlights.searching": "Searching for highlights...", "empty_state.no_content.description": "Get your answers on Agent Assist by connecting your knowledge base or creating new content in Knowledge Management.", "empty_state.no_content.note": "Reach out to your manager to start creating content!", "search.result_count": "{count, plural, =1 {1 search result for <strong>{searchTerm}</strong>} other {{count} search results for <strong>{searchTerm}</strong>}}", "websocket_closed": "There is a connection issue.", "teaser_upgrade_request_error_message": "We cannot process your request.<br>Please, try again in a few seconds", "bookmarks_edited_tag_tooltip": "This article has been edited in {source} since you bookmarked it on {createdAt}", "tip_bookmark_title": "Was that answer useful?", "automation_confirm": "Confirm", "exportModal.downloadFile": "Download file (.txt)", "bookmarks_deleted_tag_tooltip": "This article was deleted in {source} on {deletedAt}", "bookmarks_edited_tag": "Edited", "get_started_card_voice_transcription_subtitle": "Identify languages and assign them to your users", "get_started_header": "Get started", "recomendationType.article": "Article", "automation_title": "Automation for {intent}", "read_more": "Read more", "updated": "Updated {updatedAt}", "bookmarks.km_source": "Knowledge Management", "bookmarks.knowledge_title": "Knowledge", "teaser_upgrade_agent": "Reach out your administrator to upgrade your account!", "slider_see_all": "See all ({count})", "mainNotEnoughUtterancesTooltip": "Not enough information to summarize yet", "regenerateNotEnoughUtterancesTooltip": "No new information to summarize yet", "teaser_upgrade_request_title": "Request Upgrade", "viewer_summary_disclaimer": "Summarization feature in Preview mode", "gpt_answers_preview_label": "This feature is in preview mode", "kBExternalSources.custom": "Custom", "summary_failed_to_load_content": "Not able to provide summary", "download_message_success": "The download is starting, if you do not see it check your browser permissions", "search_teaser_description": "With live call transcriptions, you will never miss a detail of the conversation.<br>Our AI will <strong>highlight key customer information</strong> and allow you to <strong>copy or save for later.</strong>", "gpt_answers_title": "Answers", "preview": "Preview", "messages_add_template_error": "Unable to add to message", "rag_tooltip": "Copilot generated answer based on your company's knowledge base content", "wrapUp.label": "Wrap-up", "date_months_oct": "October", "search_no_results": "No search results for <strong>{searchTerm}</strong>", "transcription_match_count": "{count, plural, =1 {1 match} other {{count} matches}}", "empty_state_no_content_description": "Get your answers on Copilot by connecting your knowledge base or creating new content in Knowledge Management.", "bookmarks_remove_bookmark": "Unbookmark", "ai_generated_source_tag": "AI-generated", "date.months.apr": "April", "tip_knowledge_description": "Jumping between lots of different apps to get the right answer? Copilot does that job for you, so you don't need to switch windows.", "protocol_navigation_error": "Could not navigate to {app}", "readMore": "Read more", "empty_state.error.alt_image": "No elements were found (img)", "empty_state.error.message": "There was a problem loading this page. Please refresh or contact Talkdesk for support", "empty_state.error.refresh": "Refresh", "empty_state.error.title": "Something went wrong", "empty_state.highlights.description": "Agent <PERSON><PERSON> will show you a timeline with the important moments and information from the conversation", "transcription.teaser.description.text": "With live call transcriptions, you will never miss a detail of the conversation.<br/>Our AI will <strong>highlight key customer information</strong> and allow you to <strong>copy or save for later.</strong>", "quick_replies_key_press_hint": "press ENTER ↵", "rich_media_speak_tag": "Speak", "automation_error": "Something went wrong while submitting your action. Please try again.", "automation_success": "Your action was submitted successfully", "date_months_aug": "August", "date_months_dec": "December", "date_months_feb": "February", "date_months_jan": "January", "date_months_jul": "July", "date_months_jun": "June", "top_searches_subtitle": "Click to search for answers for these topics", "quick_answer": "Quick Answer", "exportModal.exportTranscription": "Export transcription"}