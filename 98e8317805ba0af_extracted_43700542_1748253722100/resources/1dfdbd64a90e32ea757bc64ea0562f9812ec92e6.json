{"emptyDoNotCallListsTitle": "Upload your first Do Not Call list", "tableLists.field.all": "All", "tableHeaders.team": "Team", "modals.uploadDoNotCallList.removeLabel": "Remove file", "tableLists.tableRow.downloadButton.latest.tooltip.ready": "Prepare list for download", "modals.uploadList.title": "Upload record list", "modals.uploadList.uploadFileButtonLabel": "Upload file", "tableHeaders.status": "Status", "tabs.records": "Records", "doNotCallListsCount": "{{count}} DNC list", "entryList.editTeam": "Edit team", "entryList.save": "Save", "filters.campaigns": "Campaigns", "sidePanels.filters.campaign.title": "Campaigns", "tableHeaders.name": "Name", "tabs.doNotCall": "Do Not Call", "loadMore.loadingElements": "Loading", "noRecordTitle": "No records found", "newDoNotCallListButtonLabelTooltip": "Do Not Call list limit reached", "entriesCount_plural": "{{formattedCount}} entries", "modals.uploadList.recordListLabel": "Record list", "modals.uploadList.template.allFieldsRequired": "All fields in the template are required.", "filters.emptyTitle": "No lists found", "modals.uploadDoNotCallList.chooseCsvButtonLabel": "Choose a CSV file", "modals.uploadDoNotCallList.recordListLabel": "Do Not Call list", "sidePanels.filters.status.label": "Status", "modals.uploadList.namePlaceholder": "e.g. Prospects", "sidePanels.filters.campaign.search.noCampaigns": "There are no campaigns to use as filter", "sidePanels.filters.campaign.search.noResults": "No campaigns found", "tableLists.tableRow.downloadButton.latest.tooltip.expired": "Expired CSV", "modals.uploadDoNotCallList.nameLabel": "Name", "modals.uploadDoNotCallList.title": "Upload Do Not Call list", "tableLists.tableRow.downloadButton.latest.tooltip.incomplete": "CSV not processed", "filters.campaigns.plural": "campaigns", "modals.uploadList.dropDown.phoneNumberSubtitle": "Any type", "modals.uploadList.template.fieldsWithGuidelines": "Required fields may vary in some situations. Check guidelines to learn more.", "sidePanels.filters.created.label": "Created", "searchEmptyTitle": "No lists found", "searchEmptyMessage": "Try using different keywords or checking for typos", "modals.uploadList.selectOutScopeTeam": "You can't upload a record list for this team. Please select a team within your management scope.", "modals.uploadDoNotCallList.selectOutScopeTeam": "You can't upload a Do Not Call list for this team. Please select a team within your management scope.", "modals.uploadList.nameLabel": "Name", "count": "{{count}} list", "entriesCount": "{{formattedCount}} entry", "filters.filterText": "Filters", "modals.uploadList.errorMaxFileSizeExceeded": "This file exceeds {{maxFileSize}}. Please select another file.", "tableHeaders.numberOfRecords": "Total records", "tableHeaders.numberOfEntries": "Total entries", "tableHeaders.createdAt.hint": "All times are displayed in the {{timezone}} timezone", "loadMore.loadMoreElements": "Load more", "tableHeaders.campaigns": "Campaigns", "tableLists.field.noTeam": "No teams", "emptyMessage": "Upload a new record list to start your outbound campaign\t", "modals.uploadList.hoverSelectTeamTip": "If you select one team, the list won't be visible to other team users; If you select “All”, the list will be visible to all users.", "emptyTitle": "Upload your first record list", "modals.deleteListConfirmation.title": "Delete record list", "modals.doNotCallListApi.title": "DNCL API", "modals.recordListApi.title": "List API", "modals.uploadDoNotCallList.cancelUploadButtonLabel": "Cancel upload", "modals.uploadDoNotCallList.errorNameNotUnique": "This list name is already taken", "modals.uploadDoNotCallList.uploadFileButtonLabel": "Upload file", "modals.uploadList.cancelUploadButtonLabel": "Cancel upload", "headerLabel": "Lists", "modals.uploadDoNotCallList.downloadListTemplateLabel": "Download template", "sidePanels.filters.all": "All", "modals.uploadList.errorMaxFileNameExceededMessage": "File names cannot be longer than {{maxFileNameSize}} characters.", "filters.status": "Status", "tabs.permission.hint": "You don't have access to this", "filters.campaigns.single": "campaign", "modals.uploadList.downloadListTemplateLabel": "Download template", "modals.uploadList.removeLabel": "Remove file", "modals.uploadList.chooseCsvButtonLabel": "Choose a CSV file", "modals.uploadList.errorNameNotUnique": "This list name is already taken", "tableHeaders.createdAt.label": "Created", "entryList.team": "Team", "modals.uploadList.duplicatesLabel": "Duplicates", "modals.uploadList.seeGuidelinesLabel": "See guidelines", "search": "Search by list name", "filters.emptyMessage": "Try using different criteria", "sidePanels.filters.campaign.search.loading": "Loading", "sidePanels.filters.campaign.maxCampaignsHint": "Select up to 10 campaigns", "modals.uploadDoNotCallList.errorMaxFileSizeExceeded": "This file exceeds {{maxFileSize}}. Please select another file.", "searchTooltip": "Type at least {{minLength}} characters", "sidePanels.filters.campaign.search": "Search for a name", "sidePanels.filters.campaign.search.minLimit": "Type at least 3 characters", "modals.uploadDoNotCallList.template.allFieldsExceptExpirationDateRequired": "All fields in the template are required, except Expiration Date.", "newButtonLabel": "Add record list", "newDoNotCallListButtonLabel": "Add Do Not Call list", "modals.uploadList.errorMaxFileNameExceededTitle": "Long name", "modals.uploadDoNotCallList.namePlaceholder": "e.g. Prospects", "tableLists.tableRow.downloadButton.latest.tooltip.corrupted": "Damaged CSV", "modals.uploadList.errorMaxUploadFileLinesExceedTitle": "File exceeds max limit of {{maxRecordListFileLimit}} records", "modals.uploadDoNotCallList.teamPermission.hint": "Users can only see the do not call lists within their permissions", "modals.uploadList.teamPermission.hint": "Users can only see the records lists within their permissions", "modals.deleteListConfirmation.message": "Are you sure you want to delete the \"{{recordListName}}\" list? You won't be able to retrieve it back.", "modals.deleteDoNotCallListConfirmation.message": "Are you sure you want to delete the \"{{name}}\" list? You won't be able to retrieve it back.", "count_plural": "{{count}} lists", "sidePanels.filters.title": "Filters", "tableLists.tableRow.downloadButton.latest.tooltip.noRecord": "No records in this list", "tableLists.tableRow.downloadButton.latest.tooltip.validating": "Still validating list", "filters.clearAllButton": "Clear all", "filters.created": "Created", "modals.listApi.content.title": "ID", "newButtonLabelTooltip": "Records list limit reached", "noTeam": "No team", "team": "Team", "emptyDoNotCallListsMessage": "Upload a new Do Not Call list to associate with a campaign\t", "recordsCount_plural": "{{formattedCount}} records", "recordsCount": "{{formattedCount}} record", "doNotCallListsCount_plural": "{{count}} DNC lists", "modals.uploadList.team.hint": "Team users can only see lists from their own team", "entryList.contradictedTip": "Please unlink the record list with the following campaign(s), then delete the team.", "entryList.update.contradictedTip": "Please unlink the record list with the following campaign(s), then change the team.", "modals.uploadList.template.allFieldsExceptExternalRequired": "All fields in the template are required, except External ID and Provider", "modals.uploadList.dropDown.externalField": "External ID and Provider", "modals.uploadDoNotCallList.team.hint": "Team users can only see lists from their own team", "modals.uploadList.errorMaxUploadFileLinesExceedDescription": "Please choose another file or divide this list in two or more files", "entryList.delete.contradictedTip": "Please unlink the record list with the following campaign(s), then delete the team.", "modals.deleteDoNotCallListConfirmation.title": "Delete do not call list", "modals.uploadDoNotCallList.hoverSelectTeamTip": "If you select one team, the list won't be visible to other team users; If you select “All”, the list will be visible to all users.", "modals.uploadList.dropDown.phoneNumberField": "Phone Number (default)", "noRecordMessage": "Please use different criteria to search or filter for records.", "modals.uploadList.toggleMessage": "Remove records with the same", "entryList.onHover": "Users can only see the records lists within their permissions"}