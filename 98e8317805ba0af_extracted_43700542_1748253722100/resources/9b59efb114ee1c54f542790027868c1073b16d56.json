{"timestampLabel": "Last updated: Today, {{updateTime}}", "headerLabel": "Campaigns", "tableHeaders.priority.title": "Priority", "tableHeaders.priority.hint": "Priority is set between {{min}} (lowest) and {{max}} (highest)", "modals.startConfirmation.title": "Start campaign?", "filters.priority": "Priority", "sidePanels.filters.priority.label": "Priority", "sidePanels.filters.status.placeholder": "Select status", "sidePanels.filters.status.label": "Status", "sidePanels.filters.dialingMode.placeholder": "Select dialing mode", "sidePanels.filters.dialingMode.option.preview": "Preview dialing", "sidePanels.filters.dialingMode.option.predictive": "Predictive dialing", "sidePanels.filters.dialingMode.label": "Dialing Mode", "sidePanels.filters.actionRequired.label": "Show only campaigns with warning flags", "tableHeaders.createdAt": "Created at", "modals.startConfirmation.info": "The dialing mode cannot be changed after the campaign starts.", "tableHeaders.name": "Name", "toolbar.title": "Filters", "duplicate": "Duplicate", "delete": "Delete", "filters.created": "Created", "tableHeaders.team": "Team", "sidePanels.filters.title": "Filters", "toolbar.filters.availableFilters.dialingMode": "Dialing Mode", "toolbar.filters.availableFilters.status": "Status", "toolbar.filters.availableFilters.extra": "Extra", "toolbar.filters.clearButton": "Clear All", "tableHeaders.dialingMode": "Dialing mode", "tableHeaders.status": "Status", "filters.emptyMessage": "We couldn’t find any campaigns with the applied filters", "sidePanels.filters.created.label": "Created", "modals.createCampaign.teamTooltip": "Users can only see campaigns from their own team", "tableHeaders.createdAt.hint": "All times are displayed in the {{timezone}} timezone", "tableHeaders.createdAt.label": "Created", "newButtonLabel": "Create campaign", "filters.emptyTitle": "No campaigns found", "filters.dialingMode": "Dialing Mode", "filters.status": "Status", "filters.extra": "Extra", "modals.createCampaign.teamPermissionTooltip": "Users can only see the campaigns within their permissions", "search": "Search by campaign name", "searchTooltip": "Type at least {{minLength}} characters", "emptyMessage": "Create a campaign to view it in the campaigns list", "emptyTitle": "No campaigns yet", "modals.createCampaign.namePlaceholder": "e.g. Black Friday", "modals.duplicateCampaign.title": "Duplicate campaign", "modals.duplicateCampaign.warningArea": "Setting up could take some seconds. You can close this modal and access the campaign in the campaigns list, once its setup is complete.", "modals.startConfirmation.messageLine": "The campaign will start calling records and the reporting information will be available in the campaign details", "toolbar.filters.title": "Filters", "modals.duplicateCampaign.infoArea": "{{originalCampaignName}}'s lists will not be added to the new campaign. To run the campaign you must add lists in the Lists tab.", "modals.duplicateCampaign.placeholder": "{{originalCampaignName}} Copy", "count": "{{count}} campaign", "count_plural": "{{count}} campaigns", "modals.createCampaign.title": "Create campaign", "noTeams": "No teams"}