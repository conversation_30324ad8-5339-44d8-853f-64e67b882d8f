{"details.emptyTitle": "No available details", "update.keyField": "Field", "update.title": "Record fields", "details.description": "Description", "modals.deleteRecordConfirmation.title": "Delete record?", "tableHeaders.lastName": "Last name", "hints.highestPriority": "Hot leads have the highest priority", "hints.detailsAction": "See record extra fields", "update.value": "Value", "count_plural": "{{count}} records", "details.count_plural": "{{count}} extra fields", "tableHeaders.phoneNumber": "Phone number", "tableHeaders.timezone": "Time zone", "sidePanels.filters.title": "Filters", "noRecords": "No records", "sidePanels.filters.lastUpdated.label": "Last Updated", "sidePanels.filters.timezone.label": "Time zone", "filters.timezones.single": "time zone", "emptyTitleByFilter": "Nothing turned up", "sidePanels.filters.timezone.search.loadMore": "Load more...", "hints.viewAction": "View record", "hotLead": "Hot lead", "sidePanels.filters.timezone.search.noResults": "No timezones found", "details.keyField": "Field", "tableHeaders.createdAt.label": "Created at", "hints.updateAction": "Edit record", "tableHeaders.updatedAt.hint": "All times are displayed in the {{timezone}} time zone", "tableHeaders.updatedAt.label": "Last updated", "tableHeaders.priority.hint": "Record’s dialing priority(1 being the lowest)", "hotLead.not.description": "Set a dialing priority between 1 and 10 (Hot leads are only assigned through API)", "details.count": "{{count}} extra field", "emptyMessage": "Guarantee that the record list is valid", "emptyTitle": "No available records", "filters.updatedTime": "Updated at", "filters.timezones": "Time zone", "sidePanels.filters.timezone.search.minLimit": "Type at least 3 characters to start searching", "filters.priorities.single": "priority", "sidePanels.filters.timezone.search.networkError.button": "Try again", "details.emptyMessage": "Guarantee that the searched detail is valid", "searchPlaceHolderText": "Search for first/last name or number", "sidePanels.filters.timezone.search.noTimezones": "There are no timezones to use as filter", "sidePanels.filters.timezone.search.loading": "Loading", "filters.priorities": "Priority", "sidePanels.filters.timezone.search.networkError.message": "Couldn't load data", "searchTooltipText": "Type at least {{minLength}} characters to start searching", "count": "{{count}} record", "details.subtitle": "Overview on {{firstName}} {{lastName}} with the number {{phoneNumber}} extra fields", "filters.priorities.plural": "priorities", "filters.timezones.plural": "time zones", "sidePanels.filters.timezone.search.networkError.loadMoreErrorMessage": "An error occurred, please try again", "emptyMessageByFilter": "You may want to try using different keywords, checking for typos, or adjusting applied filters", "hotLead.is.description": "Dialer only allows editing records with a priority set between 1 and 10", "noRecordsMessage": "There are no records left in this list, but you can still add new items through the API", "details.searchPlaceholder": "Find a record detail...", "hints.deleteAction": "Delete record", "modals.deleteRecordConfirmation.message": "You won't be able to retrieve the record back\t", "tableHeaders.firstName": "First name", "tableHeaders.priority": "Priority", "tableHeaders.createdAt.hint": "All times are displayed in the {timezone} timezone\t", "update.subtitle": "Overview on {{firstName}} {{lastName}} with the number {{phoneNumber}}", "details.value": "Value", "details.title": "Extra fields"}