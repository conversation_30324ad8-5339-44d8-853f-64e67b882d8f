{"emptyWidget.minSearchInputLength.title": "Type {{minInputLength}} characters to start searching", "selectedCount": "{{count}} selected", "warningNameCouldNotBeVerified": "Couldn't verify name. Please try again.", "teamDropdownLabel": "Team", "dropdownLoadingText": "Loading...", "disabled": "Disabled", "discard": "Discard", "warningNameCouldNotHaveRestrictedCharacters": "Characters \"&\" and \"#\" are not allowed when creating campaign's name. Please choose a different name without those characters.", "add": "Add", "errorNameNotUnique": "This campaign name is already taken", "timePeriod.custom": "Custom...", "emptyWidget.requestTimeoutError.title": "Oops", "days.count": "{{count}} day", "resourceChanged": "Resource has changed", "maxLengthWarning": "We can only support a maximum number of {{maxLength}} characters", "refresh": "Refresh", "remove": "Remove", "file": "file", "hours.general": "hour(s)", "emptyWidget.minSearchInputLength.message": "Type at least {{minInputLength}} characters for us to fetch your search results", "emptyWidget.searchWithNoResults.message": "Please use different criteria to search or filter for campaigns", "list.emptyTitle": "No entries", "list.emptyMessage": "There are currently no available entries", "emptyWidget.noTeam.message": "Check with your manager about your permissions.", "timePeriod.anyTime": "All time", "nameAllOptionText": "All", "timePeriod.lastMonth": "Last month", "noMatchesFoundTitle": "No results found", "numberOfRecords": "Number of records", "search": "Search", "outOfBoundsAndIntOnlyWarningMessage": "Enter a number between {{min}} and {{max}}", "teamDropdownPlaceholder": "Select an option", "download": "download", "createCampaign.outScope": "You can't create a campaign for this team. Please select a team within your management scope.", "timePeriod.lastSixHours": "Last 6 hours", "timePeriod.lastWeek": "Last week", "filters.filterText": "Filters", "option.name.default": "All", "leave": "Leave", "name": "Name", "hours.count": "{{count}} hour", "hours.count_plural": "{{count}} hours", "emptyWidget.noTeam.title": "You don't belong to any team", "emptyWidget.noPermission.title": "You don't have access to this", "timePeriod.lastDay": "Last 24 hours", "dismissibleTags.activity": "activity", "dismissibleTags.activities": "activities", "searchExactNumberPlaceHolderText": "Search for an exact number", "listPagination.jumpTo": "Jump to", "close": "Close", "filters.clearAllButton": "Clear all", "outMaxAttempt": "The number cannot exceed the max attempts per record", "emptyWidget.noConnection.message": "We weren't able to process your request. Please try again", "emptyWidget.noConnection.title": "No internet connection", "enabled": "Enabled", "dropdownLoadMoreButton": "Load more...", "searchByName": "Search by name", "emptyWidget.searchWithNoResults.title": "No campaigns found", "exceedingGlobalMaxAttemptsErrorMessage": "Some attempts exceed this limit", "outOfBoundsAndIntOnlyWarning": "Please insert a value between {{min}} and {{max}}, without decimal places", "exceedingGlobalMaxAttemptsError": "The attempts in record chaining is exceeding the global max. attempts per record", "seconds.count": "{{count}} second", "seconds.count_plural": "{{count}} seconds", "minutes.count_plural": "{{count}} minutes", "days.count_plural": "{{count}} days", "emptyWidget.requestTimeoutError.message": "there was a problem loading data.", "enterIsOutOfBoundsAndIntOnlyWarning": "Entering a number between {{min}} and {{max}}, without decimal places", "searchExactNumberTooltipText": "Type at least {{minLength}} characters", "goBack": "Go back", "incomplete": "Incomplete", "loadMore": "Load more", "somethingWentWrong": "Something went wrong", "maxCharacterLimitExceded": "The maximum number of characters has been exceeded\t", "minutes.general": "minute(s)", "newName": "New name", "next": "Next", "noItemsFoundTitle": "No items found", "pause": "Pause", "paused": "Paused", "pausing": "Pausing", "previous": "Previous", "ready": "Ready", "requiredFieldWarning": "This field is required", "ringGroups": "Ring groups", "running": "Running", "save": "Save", "noMatchesFoundMessage": "Please search for another keyword", "minutes.count": "{{count}} minute", "seconds.general": "second(s)", "select": "Select", "start": "Start", "starting": "Starting", "status": "Status", "to": "To", "emptyWidget.noPermission.message": "It looks like you don't have permission to see this content.", "teamAllOptionText": "All teams", "loading": "Loading", "apply": "Apply", "cancel": "Cancel\t", "clear": "Clear", "create": "Create", "createdAt": "Created at", "days.general": "day(s)", "delete": "Delete", "emptyRowValue": "N/A", "from": "From"}