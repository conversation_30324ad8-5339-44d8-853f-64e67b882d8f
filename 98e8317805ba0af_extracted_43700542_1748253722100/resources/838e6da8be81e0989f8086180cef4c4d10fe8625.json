{"on": "On", "off": "Off", "onUntil": "On until", "tryAgain": "Try again", "settingsTitle": "Settings", "settingsDescription": "Edit your user settings and preferences", "editSettings": "Edit", "doNotDisturb": {"headerTitle": "Do not disturb", "headerDescription": "New incoming calls will automatically be forwarded to voicemail, even if call forward is on", "dnd30Minutes": "30 minutes", "dnd1Hour": "1 hour", "dnd2Hours": "2 hours", "dnd24Hours": "24 hours", "dndSetCustom": "Set a custom time", "dndCustom": "Custom time", "errorFetchingTitle": "We couldn't load the user's do not disturb setting", "timePlaceholder": "Adjust time"}, "callWaiting": {"headerTitle": "Call waiting", "headerDescription": "Enable a waiting line for incoming calls instead of automatically sending them to voicemail", "errorFetchingTitle": "We couldn't load the user's call waiting setting", "checkboxHeader": "Enable call waiting", "checkboxDescription": "With call waiting enabled when you are in call you can have an incoming call on wait instead of hearing a busy signal.", "checkboxLink": "Learn more about Call waiting"}, "countryCode": {"errorFetchingTitle": "We couldn't load the user's default country code", "headerDescription": "Set a default country code for calls placed from the keypad or your SIP devices", "headerTitle": "Default country code", "tooltip": "You will not have to dial the country code for numbers of the selected country", "searchPlaceholder": "Search by country code or name...", "noResultsFound": "No results found"}, "callForward": {"errorFetchingTitle": "We couldn't load the user's call forward settings", "headerDescription": "Forward all your incoming calls to another number or extension", "headerTitle": "Call forward", "placeholder": "Type or paste a number", "error": {"length": "Numbers need to have between 3 and 20 digits", "own": "You cannot forward calls to yourself", "emergency": "You cannot forward calls to the Emergency services (X112, X911, X933, X988 or X999)", "empty": "You need to set a number to activate call forward"}}}