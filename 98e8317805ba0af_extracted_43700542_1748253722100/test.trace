{"version":7,"type":"context-options","origin":"testRunner","browserName":"","options":{},"platform":"linux","wallTime":1747944176361,"monotonicTime":63640.604,"sdkLanguage":"javascript"}
{"type":"before","callId":"hook@1","startTime":63641.805,"class":"Test","method":"step","apiName":"Before Hooks","params":{},"stack":[]}
{"type":"before","callId":"hook@2","parentId":"hook@1","startTime":63642.167,"class":"Test","method":"step","apiName":"beforeEach hook","params":{},"stack":[{"file":"/tests/smoke/test_campaign_record_flow_clone.spec.js","line":36,"column":6}]}
{"type":"stdout","timestamp":63644.279,"text":"==== 【po_automation】Running test Smoke-Predicative Campaign Create/Start/Pause/Resume/View/Edit/Check/Dup with worker pid 518 on chromium=====\n"}
{"type":"after","callId":"hook@2","endTime":63644.336,"attachments":[]}
{"type":"before","callId":"fixture@3","parentId":"hook@1","startTime":63644.823,"class":"Test","method":"step","apiName":"fixture: context","params":{},"stack":[]}
{"type":"before","callId":"pw:api@4","parentId":"fixture@3","startTime":63646.039,"class":"Test","method":"step","apiName":"browser.newContext","params":{"acceptDownloads":"accept","bypassCSP":"false","colorScheme":"light","deviceScaleFactor":"1","hasTouch":"false","ignoreHTTPSErrors":"false","isMobile":"false","javaScriptEnabled":"true","locale":"en-US","offline":"false","permissions":"[microphone, notifications, camera]","storageState":"Object","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.6778.33 Safari/537.36","viewport":"Object","baseURL":"https://po-automation.mytalkdesk.eu","serviceWorkers":"allow","recordVideo":"Object","noDefaultViewport":"false","extraHTTPHeaders":"undefined","recordHar":"undefined","reducedMotion":"undefined","forcedColors":"undefined","clientCertificates":"undefined"},"stack":[]}
{"type":"after","callId":"pw:api@4","endTime":64201.812,"attachments":[]}
{"type":"after","callId":"fixture@3","endTime":64205.474,"attachments":[]}
{"type":"before","callId":"fixture@5","parentId":"hook@1","startTime":64205.753,"class":"Test","method":"step","apiName":"fixture: page","params":{},"stack":[]}
{"type":"before","callId":"pw:api@6","parentId":"fixture@5","startTime":64206.499,"class":"Test","method":"step","apiName":"browserContext.newPage","params":{},"stack":[]}
{"type":"after","callId":"pw:api@6","endTime":64247.813,"attachments":[]}
{"type":"after","callId":"fixture@5","endTime":64247.911,"attachments":[]}
{"type":"after","callId":"hook@1","endTime":64247.978,"attachments":[]}
{"type":"before","callId":"test.step@7","startTime":64254.089,"class":"Test","method":"step","apiName":"Smoke-RCX-1308: Create Campaign","params":{},"stack":[{"file":"/tests/smoke/test_campaign_record_flow_clone.spec.js","line":578,"column":16}]}
{"type":"before","callId":"test.step@8","parentId":"test.step@7","startTime":64255.377,"class":"Test","method":"step","apiName":"Step1-Click on the Campaign Manager APP","params":{},"stack":[{"file":"/tests/smoke/test_campaign_record_flow_clone.spec.js","line":582,"column":20}]}
{"type":"stdout","timestamp":64255.557,"text":"======1308-Step1======\n"}
{"type":"before","callId":"pw:api@9","parentId":"test.step@8","startTime":64257.383,"class":"Test","method":"step","apiName":"page.goto","params":{"url":"/atlas/apps/outbound-dialer","waitUntil":"load"},"stack":[{"file":"/pages/basePage.js","line":228,"column":23,"function":"DialerPage.retry_goto"}]}
{"type":"before","callId":"pw:api@10","parentId":"test.step@8","startTime":64259.061,"class":"Test","method":"step","apiName":"page.waitForNavigation","params":{"info":"Object"},"stack":[{"file":"/pages/basePage.js","line":229,"column":29,"function":"DialerPage.retry_goto"}]}
{"type":"after","callId":"pw:api@10","endTime":65578.931,"attachments":[]}
{"type":"before","callId":"pw:api@11","parentId":"test.step@8","startTime":65579.921,"class":"Test","method":"step","apiName":"page.waitForTimeout","params":{"timeout":"3000"},"stack":[{"file":"/pages/basePage.js","line":356,"column":25,"function":"DialerPage.wait_for_timeout"}]}
{"type":"after","callId":"pw:api@9","endTime":65786.021,"attachments":[]}
{"type":"after","callId":"pw:api@11","endTime":69220.116,"attachments":[]}
{"type":"before","callId":"pw:api@12","parentId":"test.step@8","startTime":69221.584,"class":"Test","method":"step","apiName":"locator.isVisible","params":{"selector":"div:not([style*=\"display: none\"])[data-testid=\"app\"] svg.loader-module__loader >> nth=0","strict":"true"},"stack":[{"file":"/pages/basePage.js","line":283,"column":37,"function":"DialerPage.waitForError_new"}]}
{"type":"after","callId":"pw:api@12","endTime":69433.171,"attachments":[]}
{"type":"before","callId":"pw:api@13","parentId":"test.step@8","startTime":69435.434,"class":"Test","method":"step","apiName":"page.waitForTimeout","params":{"timeout":"3000"},"stack":[{"file":"/pages/basePage.js","line":356,"column":25,"function":"DialerPage.wait_for_timeout"}]}
{"type":"after","callId":"pw:api@13","endTime":72474.011,"attachments":[]}
{"type":"before","callId":"pw:api@14","parentId":"test.step@8","startTime":72475.227,"class":"Test","method":"step","apiName":"page.waitForTimeout","params":{"timeout":"3000"},"stack":[{"file":"/pages/basePage.js","line":356,"column":25,"function":"DialerPage.wait_for_timeout"}]}
{"type":"after","callId":"pw:api@14","endTime":75508.697,"attachments":[]}
{"type":"before","callId":"pw:api@15","parentId":"test.step@8","startTime":75509.828,"class":"Test","method":"step","apiName":"locator.isVisible","params":{"selector":"div:not([style*=\"display: none\"])[data-testid=\"app\"] svg.loader-module__loader >> nth=0","strict":"true"},"stack":[{"file":"/pages/basePage.js","line":283,"column":37,"function":"DialerPage.waitForError_new"}]}
{"type":"after","callId":"pw:api@15","endTime":75537.783,"attachments":[]}
{"type":"before","callId":"pw:api@16","parentId":"test.step@8","startTime":75539.635,"class":"Test","method":"step","apiName":"page.evaluate","params":{"expression":"() => {\n      console.log(\"========START EXECUTE JQUERY=============\");\n      // let toastRoot = document.querySelector('div#toast');\n      const toastRoot = document.querySelector('div[data-testid=\"toaster\"]');\n      let MutationObserver = window.MutationObserver || window.WebKitMutationObserver || window.MozMutationObserver;\n      let mutationObserver = new MutationObserver(mutations => {\n        const toast = document.querySelector('div[data-testid=\"toaster\"] div[data-co-name=\"Message\"] div[data-testid]');\n        const toast_content = document.querySelector('div[data-testid=\"toaster\"] div[data-co-name=\"Message\"] div[data-testid] p').textContent;\n        console.log(toast);\n        if (toast_content.includes('Conversations is open in another tab or device')) {\n          console.log(\"=====find the conversation conflict toast======\");\n          toast.remove();\n        } else {\n          console.log(\"=======No need to fix this toast=======\");\n        }\n      });\n      mutationObserver.observe(toastRoot, {\n        childList: true,\n        //\n        // attributes: true, //\n        // characterData: true, //\n        subtree: true //\n        // attributesFilter: ['class', 'style'], //\n        // attributesOldValue: true, //\n        // characterDataOldValue: true //\n      });\n      console.log(\"========START EXECUTE JQUERY=============\");\n    }","isFunction":"true","arg":"Object"},"stack":[{"file":"/pages/basePage.js","line":486,"column":25,"function":"DialerPage.hiddenConflictToast"}]}
{"type":"after","callId":"pw:api@16","endTime":75562.657,"attachments":[]}
{"type":"before","callId":"expect@17","parentId":"test.step@8","startTime":75563.821,"class":"Test","method":"step","apiName":"expect.toBeVisible","params":{},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":329,"column":38,"function":"DialerPage.waitForCampaignPage"}]}
{"type":"after","callId":"expect@17","endTime":75584.366,"attachments":[]}
{"type":"before","callId":"pw:api@18","parentId":"test.step@8","startTime":75585.368,"class":"Test","method":"step","apiName":"locator.textContent","params":{"selector":"header.dock-drawer-component-module__header h4","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":412,"column":31,"function":"DialerPage.getPageTitle"}]}
{"type":"after","callId":"pw:api@18","endTime":75603.502,"attachments":[]}
{"type":"before","callId":"expect@19","parentId":"test.step@8","startTime":75604.33,"class":"Test","method":"step","apiName":"expect.toEqual","params":{"expected":"Dialer"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":330,"column":43,"function":"DialerPage.waitForCampaignPage"}]}
{"type":"after","callId":"expect@19","endTime":75604.905,"attachments":[]}
{"type":"before","callId":"pw:api@20","parentId":"test.step@8","startTime":75605.85,"class":"Test","method":"step","apiName":"locator.isVisible","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> header h1","strict":"true"},"stack":[{"file":"/pages/basePage.js","line":292,"column":47,"function":"DialerPage.waitForVisibleLocator"}]}
{"type":"after","callId":"pw:api@20","endTime":75636.751,"attachments":[]}
{"type":"before","callId":"pw:api@21","parentId":"test.step@8","startTime":75637.717,"class":"Test","method":"step","apiName":"locator.isVisible","params":{"selector":"button.secondary-area-module__active","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":386,"column":44,"function":"DialerPage.waitForFrameLoad"}]}
{"type":"after","callId":"pw:api@21","endTime":75655.98,"attachments":[]}
{"type":"stdout","timestamp":75656.695,"text":"get Second Area Panel again\n"}
{"type":"before","callId":"pw:api@22","parentId":"test.step@8","startTime":75658.065,"class":"Test","method":"step","apiName":"locator.isVisible","params":{"selector":"div._pendo-step-container-size","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":388,"column":34,"function":"DialerPage.waitForFrameLoad"}]}
{"type":"after","callId":"pw:api@22","endTime":75676.484,"attachments":[]}
{"type":"stdout","timestamp":75676.564,"text":"the recommandAdv is hidden\n"}
{"type":"before","callId":"pw:api@23","parentId":"test.step@8","startTime":75677.648,"class":"Test","method":"step","apiName":"locator.click","params":{"selector":"button.secondary-area-module__active","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":395,"column":42,"function":"DialerPage.waitForFrameLoad"}]}
{"type":"after","callId":"pw:api@23","endTime":75877.112,"attachments":[]}
{"type":"stdout","timestamp":75877.216,"text":"The frame content load successfully.\n"}
{"type":"before","callId":"pw:api@24","parentId":"test.step@8","startTime":75878.322,"class":"Test","method":"step","apiName":"locator.isVisible","params":{"selector":"div[data-testid=\"toaster\"] div[data-co-name=\"Message\"]:nth-child(1) div[data-testid]","strict":"true"},"stack":[{"file":"/pages/basePage.js","line":522,"column":37,"function":"DialerPage.waitAndClearToast"}]}
{"type":"after","callId":"pw:api@24","endTime":75904.458,"attachments":[]}
{"type":"stdout","timestamp":75904.55,"text":"await and clear toast\n"}
{"type":"before","callId":"pw:api@25","parentId":"test.step@8","startTime":75906.03,"class":"Test","method":"step","apiName":"locator.isVisible","params":{"selector":"div[data-testid=\"toaster\"] div[data-co-name=\"Message\"]:nth-child(1) div[data-testid] >> button>i:text(\"close\")","strict":"true"},"stack":[{"file":"/pages/basePage.js","line":524,"column":74,"function":"DialerPage.waitAndClearToast"}]}
{"type":"after","callId":"pw:api@25","endTime":75936.948,"attachments":[]}
{"type":"before","callId":"pw:api@26","parentId":"test.step@8","startTime":75937.958,"class":"Test","method":"step","apiName":"locator.click","params":{"selector":"div[data-testid=\"toaster\"] div[data-co-name=\"Message\"]:nth-child(1) div[data-testid] >> button>i:text(\"close\")","strict":"true"},"stack":[{"file":"/pages/basePage.js","line":526,"column":78,"function":"DialerPage.waitAndClearToast"}]}
{"type":"after","callId":"pw:api@26","endTime":76138.492,"attachments":[]}
{"type":"before","callId":"pw:api@27","parentId":"test.step@8","startTime":76139.629,"class":"Test","method":"step","apiName":"page.waitForTimeout","params":{"timeout":"1000"},"stack":[{"file":"/pages/basePage.js","line":356,"column":25,"function":"DialerPage.wait_for_timeout"}]}
{"type":"after","callId":"pw:api@27","endTime":77168.591,"attachments":[]}
{"type":"before","callId":"pw:api@28","parentId":"test.step@8","startTime":77170.223,"class":"Test","method":"step","apiName":"locator.isVisible","params":{"selector":"div[data-testid=\"toaster\"] div[data-co-name=\"Message\"]:nth-child(1) div[data-testid]","strict":"true"},"stack":[{"file":"/pages/basePage.js","line":522,"column":37,"function":"DialerPage.waitAndClearToast"}]}
{"type":"after","callId":"pw:api@28","endTime":77188.021,"attachments":[]}
{"type":"after","callId":"test.step@8","endTime":77188.087,"attachments":[]}
{"type":"before","callId":"test.step@29","parentId":"test.step@7","startTime":77188.624,"class":"Test","method":"step","apiName":"Step5-Fill Campaign name with an unique name.","params":{},"stack":[{"file":"/tests/smoke/test_campaign_record_flow_clone.spec.js","line":588,"column":20}]}
{"type":"stdout","timestamp":77188.855,"text":"======1308-Step5======\n"}
{"type":"before","callId":"pw:api@30","parentId":"test.step@29","startTime":77190.632,"class":"Test","method":"step","apiName":"locator.click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div button[data-pendo-campaignlist-header-createbutton]","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":982,"column":32,"function":"DialerPage.clickCreateCampaign"}]}
{"type":"after","callId":"pw:api@30","endTime":77548.481,"attachments":[]}
{"type":"before","callId":"pw:api@31","parentId":"test.step@29","startTime":77553.916,"class":"Test","method":"step","apiName":"page.waitForResponse","params":{"info":"Object"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":975,"column":14,"function":"DialerPage.createCampaign"}]}
{"type":"before","callId":"pw:api@32","parentId":"test.step@29","startTime":77556.553,"class":"Test","method":"step","apiName":"locator.fill","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-modal__dialog >> #name-form-field-id","value":"metoto_test_jfflsuxs","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":986,"column":32,"function":"DialerPage.inputCampaignName"}]}
{"type":"after","callId":"pw:api@32","endTime":77847.94,"attachments":[]}
{"type":"after","callId":"pw:api@31","endTime":78154.051,"attachments":[]}
{"type":"before","callId":"pw:api@33","parentId":"test.step@29","startTime":78155.587,"class":"Test","method":"step","apiName":"locator.click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-modal__dialog >> button[data-testid=\"confirmButton\"]","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":990,"column":25,"function":"DialerPage.confirmCreateCampaign"}]}
{"type":"after","callId":"pw:api@33","endTime":78446.723,"attachments":[]}
{"type":"before","callId":"expect@34","parentId":"test.step@29","startTime":78448.118,"class":"Test","method":"step","apiName":"expect.toBeEnabled","params":{},"stack":[{"file":"/tests/smoke/test_campaign_record_flow_clone.spec.js","line":598,"column":49}]}
{"type":"after","callId":"expect@34","endTime":78739.496,"attachments":[]}
{"type":"before","callId":"expect@35","parentId":"test.step@29","startTime":78744.545,"class":"Test","method":"step","apiName":"expect.toBeVisible","params":{},"stack":[{"file":"/tests/smoke/test_campaign_record_flow_clone.spec.js","line":599,"column":50}]}
{"type":"after","callId":"expect@35","endTime":78780.228,"attachments":[]}
{"type":"before","callId":"expect@36","parentId":"test.step@29","startTime":78781.433,"class":"Test","method":"step","apiName":"expect.not.toBeEnabled","params":{},"stack":[{"file":"/tests/smoke/test_campaign_record_flow_clone.spec.js","line":600,"column":54}]}
{"type":"after","callId":"expect@36","endTime":78829.687,"attachments":[]}
{"type":"before","callId":"expect@37","parentId":"test.step@29","startTime":78831.122,"class":"Test","method":"step","apiName":"expect.toBeVisible","params":{},"stack":[{"file":"/tests/smoke/test_campaign_record_flow_clone.spec.js","line":601,"column":46}]}
{"type":"after","callId":"expect@37","endTime":78854.011,"attachments":[]}
{"type":"before","callId":"expect@38","parentId":"test.step@29","startTime":78855.425,"class":"Test","method":"step","apiName":"expect.not.toBeEnabled","params":{},"stack":[{"file":"/tests/smoke/test_campaign_record_flow_clone.spec.js","line":602,"column":50}]}
{"type":"after","callId":"expect@38","endTime":78881.445,"attachments":[]}
{"type":"before","callId":"pw:api@39","parentId":"test.step@29","startTime":78882.627,"class":"Test","method":"step","apiName":"page.screenshot","params":{"mask":"undefined","type":"undefined"},"stack":[{"file":"/pages/basePage.js","line":103,"column":60,"function":"DialerPage.saveImage"}]}
{"type":"after","callId":"pw:api@39","endTime":79007.348,"attachments":[]}
{"type":"before","callId":"test.step@40","parentId":"test.step@29","startTime":79008.098,"class":"Test","method":"step","apiName":"allureattach_f66dae15-ca3b-4598-a71a-6c90f17e96c9_capture","params":{},"stack":[{"file":"/node_modules/allure-playwright/src/helpers.ts","line":21,"column":17,"function":"Function.step"}]}
{"type":"before","callId":"attach@41","parentId":"test.step@40","startTime":79009.731,"class":"Test","method":"step","apiName":"attach \"allureattach_f66dae15-ca3b-4598-a71a-6c90f17e96c9_capture\"","params":{},"stack":[{"file":"/node_modules/allure-playwright/src/helpers.ts","line":33,"column":7}]}
{"type":"after","callId":"attach@41","endTime":79010.631,"attachments":[{"name":"allureattach_f66dae15-ca3b-4598-a71a-6c90f17e96c9_capture","contentType":"image/png","sha1":"49f24c48daf1e9bbc24b20639b2c0750a126a538"}]}
{"type":"after","callId":"test.step@40","endTime":79010.855,"attachments":[]}
{"type":"after","callId":"test.step@29","endTime":79010.887,"attachments":[]}
{"type":"before","callId":"test.step@42","parentId":"test.step@7","startTime":79011.525,"class":"Test","method":"step","apiName":"Step7-Change the default priority (5) to another value.","params":{},"stack":[{"file":"/tests/smoke/test_campaign_record_flow_clone.spec.js","line":606,"column":20}]}
{"type":"stdout","timestamp":79011.776,"text":"======1308-Step7======\n"}
{"type":"before","callId":"pw:api@43","parentId":"test.step@42","startTime":79013.641,"class":"Test","method":"step","apiName":"locator.isVisible","params":{"selector":"div[data-testid=\"toaster\"] div[data-co-name=\"Message\"]:nth-child(1) div[data-testid]","strict":"true"},"stack":[{"file":"/pages/basePage.js","line":522,"column":37,"function":"DialerPage.waitAndClearToast"}]}
{"type":"after","callId":"pw:api@43","endTime":79050.826,"attachments":[]}
{"type":"before","callId":"pw:api@44","parentId":"test.step@42","startTime":79052.319,"class":"Test","method":"step","apiName":"locator.textContent","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-grid__column.co-grid__column--min>h2.co-heading","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":926,"column":45,"function":"DialerPage.getSettingTitle"}]}
{"type":"after","callId":"pw:api@44","endTime":79068.691,"attachments":[]}
{"type":"before","callId":"expect@45","parentId":"test.step@42","startTime":79069.474,"class":"Test","method":"step","apiName":"expect.toEqual","params":{"expected":"Mode"},"stack":[{"file":"/tests/smoke/test_campaign_record_flow_clone.spec.js","line":613,"column":62}]}
{"type":"after","callId":"expect@45","endTime":79069.575,"attachments":[]}
{"type":"before","callId":"pw:api@46","parentId":"test.step@42","startTime":79070.45,"class":"Test","method":"step","apiName":"locator.click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-section__content:has(label[for=\"DIALING_MODE_FIELD_ID\"]) >> div#dialDropdown","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1054,"column":32,"function":"DialerPage.setDialingMode"}]}
{"type":"after","callId":"pw:api@46","endTime":79254.035,"attachments":[]}
{"type":"before","callId":"pw:api@47","parentId":"test.step@42","startTime":79255.331,"class":"Test","method":"step","apiName":"locator.click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-section__content:has(label[for=\"DIALING_MODE_FIELD_ID\"]) >> div#dialDropdown >> ul >> span.co-list__item-content:not(.co-list__item-content--minimal):text(\"Predictive dialing\")","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1055,"column":124,"function":"DialerPage.setDialingMode"}]}
{"type":"after","callId":"pw:api@47","endTime":79546.181,"attachments":[]}
{"type":"before","callId":"expect@48","parentId":"test.step@42","startTime":79547.802,"class":"Test","method":"step","apiName":"expect.toBeVisible","params":{},"stack":[{"file":"/tests/smoke/test_campaign_record_flow_clone.spec.js","line":616,"column":64}]}
{"type":"after","callId":"expect@48","endTime":79627.941,"attachments":[]}
{"type":"before","callId":"pw:api@49","parentId":"test.step@42","startTime":79629.095,"class":"Test","method":"step","apiName":"locator.click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> label[for=\"priority-id-9\"]","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1034,"column":78,"function":"DialerPage.setCampaignPriority"}]}
{"type":"after","callId":"pw:api@49","endTime":79875.485,"attachments":[]}
{"type":"before","callId":"pw:api@50","parentId":"test.step@42","startTime":79876.781,"class":"Test","method":"step","apiName":"locator.getAttribute","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> label[for=\"priority-id-0\"]","name":"class","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1042,"column":31,"function":"DialerPage.checkPriorityValue"}]}
{"type":"after","callId":"pw:api@50","endTime":79900.218,"attachments":[]}
{"type":"before","callId":"expect@51","parentId":"test.step@42","startTime":79901.35,"class":"Test","method":"step","apiName":"expect.toContain","params":{"expected":"co--bg-green-500"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1042,"column":54,"function":"DialerPage.checkPriorityValue"}]}
{"type":"after","callId":"expect@51","endTime":79901.828,"attachments":[]}
{"type":"before","callId":"pw:api@52","parentId":"test.step@42","startTime":79902.872,"class":"Test","method":"step","apiName":"locator.getAttribute","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> label[for=\"priority-id-1\"]","name":"class","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1042,"column":31,"function":"DialerPage.checkPriorityValue"}]}
{"type":"after","callId":"pw:api@52","endTime":79937.841,"attachments":[]}
{"type":"before","callId":"expect@53","parentId":"test.step@42","startTime":79938.982,"class":"Test","method":"step","apiName":"expect.toContain","params":{"expected":"co--bg-green-500"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1042,"column":54,"function":"DialerPage.checkPriorityValue"}]}
{"type":"after","callId":"expect@53","endTime":79939.101,"attachments":[]}
{"type":"before","callId":"pw:api@54","parentId":"test.step@42","startTime":79940.323,"class":"Test","method":"step","apiName":"locator.getAttribute","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> label[for=\"priority-id-2\"]","name":"class","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1042,"column":31,"function":"DialerPage.checkPriorityValue"}]}
{"type":"after","callId":"pw:api@54","endTime":79959.032,"attachments":[]}
{"type":"before","callId":"expect@55","parentId":"test.step@42","startTime":79959.892,"class":"Test","method":"step","apiName":"expect.toContain","params":{"expected":"co--bg-green-500"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1042,"column":54,"function":"DialerPage.checkPriorityValue"}]}
{"type":"after","callId":"expect@55","endTime":79959.995,"attachments":[]}
{"type":"before","callId":"pw:api@56","parentId":"test.step@42","startTime":79960.854,"class":"Test","method":"step","apiName":"locator.getAttribute","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> label[for=\"priority-id-3\"]","name":"class","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1042,"column":31,"function":"DialerPage.checkPriorityValue"}]}
{"type":"after","callId":"pw:api@56","endTime":79977.875,"attachments":[]}
{"type":"before","callId":"expect@57","parentId":"test.step@42","startTime":79978.788,"class":"Test","method":"step","apiName":"expect.toContain","params":{"expected":"co--bg-green-500"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1042,"column":54,"function":"DialerPage.checkPriorityValue"}]}
{"type":"after","callId":"expect@57","endTime":79978.924,"attachments":[]}
{"type":"before","callId":"pw:api@58","parentId":"test.step@42","startTime":79979.741,"class":"Test","method":"step","apiName":"locator.getAttribute","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> label[for=\"priority-id-4\"]","name":"class","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1042,"column":31,"function":"DialerPage.checkPriorityValue"}]}
{"type":"after","callId":"pw:api@58","endTime":80001.737,"attachments":[]}
{"type":"before","callId":"expect@59","parentId":"test.step@42","startTime":80002.595,"class":"Test","method":"step","apiName":"expect.toContain","params":{"expected":"co--bg-green-500"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1042,"column":54,"function":"DialerPage.checkPriorityValue"}]}
{"type":"after","callId":"expect@59","endTime":80002.715,"attachments":[]}
{"type":"before","callId":"pw:api@60","parentId":"test.step@42","startTime":80003.508,"class":"Test","method":"step","apiName":"locator.getAttribute","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> label[for=\"priority-id-5\"]","name":"class","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1042,"column":31,"function":"DialerPage.checkPriorityValue"}]}
{"type":"after","callId":"pw:api@60","endTime":80037.987,"attachments":[]}
{"type":"before","callId":"expect@61","parentId":"test.step@42","startTime":80039.456,"class":"Test","method":"step","apiName":"expect.toContain","params":{"expected":"co--bg-green-500"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1042,"column":54,"function":"DialerPage.checkPriorityValue"}]}
{"type":"after","callId":"expect@61","endTime":80039.609,"attachments":[]}
{"type":"before","callId":"pw:api@62","parentId":"test.step@42","startTime":80041.009,"class":"Test","method":"step","apiName":"locator.getAttribute","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> label[for=\"priority-id-6\"]","name":"class","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1042,"column":31,"function":"DialerPage.checkPriorityValue"}]}
{"type":"after","callId":"pw:api@62","endTime":80057.177,"attachments":[]}
{"type":"before","callId":"expect@63","parentId":"test.step@42","startTime":80058.433,"class":"Test","method":"step","apiName":"expect.toContain","params":{"expected":"co--bg-green-500"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1042,"column":54,"function":"DialerPage.checkPriorityValue"}]}
{"type":"after","callId":"expect@63","endTime":80058.602,"attachments":[]}
{"type":"before","callId":"pw:api@64","parentId":"test.step@42","startTime":80059.958,"class":"Test","method":"step","apiName":"locator.getAttribute","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> label[for=\"priority-id-7\"]","name":"class","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1042,"column":31,"function":"DialerPage.checkPriorityValue"}]}
{"type":"after","callId":"pw:api@64","endTime":80076.749,"attachments":[]}
{"type":"before","callId":"expect@65","parentId":"test.step@42","startTime":80078.142,"class":"Test","method":"step","apiName":"expect.toContain","params":{"expected":"co--bg-green-500"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1042,"column":54,"function":"DialerPage.checkPriorityValue"}]}
{"type":"after","callId":"expect@65","endTime":80078.304,"attachments":[]}
{"type":"before","callId":"pw:api@66","parentId":"test.step@42","startTime":80079.763,"class":"Test","method":"step","apiName":"locator.getAttribute","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> label[for=\"priority-id-8\"]","name":"class","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1042,"column":31,"function":"DialerPage.checkPriorityValue"}]}
{"type":"after","callId":"pw:api@66","endTime":80104.93,"attachments":[]}
{"type":"before","callId":"expect@67","parentId":"test.step@42","startTime":80105.743,"class":"Test","method":"step","apiName":"expect.toContain","params":{"expected":"co--bg-green-500"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1042,"column":54,"function":"DialerPage.checkPriorityValue"}]}
{"type":"after","callId":"expect@67","endTime":80105.842,"attachments":[]}
{"type":"before","callId":"pw:api@68","parentId":"test.step@42","startTime":80126.888,"class":"Test","method":"step","apiName":"page.screenshot","params":{"mask":"undefined","type":"undefined"},"stack":[{"file":"/pages/basePage.js","line":103,"column":60,"function":"DialerPage.saveImage"}]}
{"type":"after","callId":"pw:api@68","endTime":80229.666,"attachments":[]}
{"type":"before","callId":"test.step@69","parentId":"test.step@42","startTime":80230.444,"class":"Test","method":"step","apiName":"allureattach_ccb65d1b-060d-4a0e-819b-3cbcdbc6d3bf_capture","params":{},"stack":[{"file":"/node_modules/allure-playwright/src/helpers.ts","line":21,"column":17,"function":"Function.step"}]}
{"type":"before","callId":"attach@70","parentId":"test.step@69","startTime":80231.25,"class":"Test","method":"step","apiName":"attach \"allureattach_ccb65d1b-060d-4a0e-819b-3cbcdbc6d3bf_capture\"","params":{},"stack":[{"file":"/node_modules/allure-playwright/src/helpers.ts","line":33,"column":7}]}
{"type":"after","callId":"attach@70","endTime":80231.987,"attachments":[{"name":"allureattach_ccb65d1b-060d-4a0e-819b-3cbcdbc6d3bf_capture","contentType":"image/png","sha1":"b0b88f9d2fb96f42151b203176590b39a2e6c450"}]}
{"type":"after","callId":"test.step@69","endTime":80232.19,"attachments":[]}
{"type":"after","callId":"test.step@42","endTime":80232.214,"attachments":[]}
{"type":"before","callId":"test.step@71","parentId":"test.step@7","startTime":80232.733,"class":"Test","method":"step","apiName":"Step9-Fill Max.Dialing Ratio with a valid value - between 1 and 3.","params":{},"stack":[{"file":"/tests/smoke/test_campaign_record_flow_clone.spec.js","line":622,"column":20}]}
{"type":"stdout","timestamp":80232.96,"text":"======1308-Step9======\n"}
{"type":"before","callId":"pw:api@72","parentId":"test.step@71","startTime":80233.987,"class":"Test","method":"step","apiName":"locator.fill","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> #max-dialing-ratio-id-input","value":"2","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1059,"column":32,"function":"DialerPage.setDialingRadio"}]}
{"type":"after","callId":"pw:api@72","endTime":80374.584,"attachments":[]}
{"type":"before","callId":"pw:api@73","parentId":"test.step@71","startTime":80375.687,"class":"Test","method":"step","apiName":"page.waitForTimeout","params":{"timeout":"1000"},"stack":[{"file":"/pages/basePage.js","line":356,"column":25,"function":"DialerPage.wait_for_timeout"}]}
{"type":"after","callId":"pw:api@73","endTime":81389.033,"attachments":[]}
{"type":"before","callId":"expect@74","parentId":"test.step@71","startTime":81389.801,"class":"Test","method":"step","apiName":"expect.toBeHidden","params":{},"stack":[{"file":"/tests/smoke/test_campaign_record_flow_clone.spec.js","line":628,"column":71}]}
{"type":"after","callId":"expect@74","endTime":81405.228,"attachments":[]}
{"type":"before","callId":"pw:api@75","parentId":"test.step@71","startTime":81406.062,"class":"Test","method":"step","apiName":"page.screenshot","params":{"mask":"undefined","type":"undefined"},"stack":[{"file":"/pages/basePage.js","line":103,"column":60,"function":"DialerPage.saveImage"}]}
{"type":"after","callId":"pw:api@75","endTime":81507.181,"attachments":[]}
{"type":"before","callId":"test.step@76","parentId":"test.step@71","startTime":81507.668,"class":"Test","method":"step","apiName":"allureattach_d3ce7629-05c9-4c39-866c-133da71c1f6c_capture","params":{},"stack":[{"file":"/node_modules/allure-playwright/src/helpers.ts","line":21,"column":17,"function":"Function.step"}]}
{"type":"before","callId":"attach@77","parentId":"test.step@76","startTime":81508.322,"class":"Test","method":"step","apiName":"attach \"allureattach_d3ce7629-05c9-4c39-866c-133da71c1f6c_capture\"","params":{},"stack":[{"file":"/node_modules/allure-playwright/src/helpers.ts","line":33,"column":7}]}
{"type":"after","callId":"attach@77","endTime":81508.839,"attachments":[{"name":"allureattach_d3ce7629-05c9-4c39-866c-133da71c1f6c_capture","contentType":"image/png","sha1":"0f31929d57aba8160f073b1f32df7a0f48a295af"}]}
{"type":"after","callId":"test.step@76","endTime":81508.901,"attachments":[]}
{"type":"after","callId":"test.step@71","endTime":81508.913,"attachments":[]}
{"type":"before","callId":"test.step@78","parentId":"test.step@7","startTime":81509.188,"class":"Test","method":"step","apiName":"Step11-Fill Max.Abandonment Rate with a valid value - between 1 and 100. ","params":{},"stack":[{"file":"/tests/smoke/test_campaign_record_flow_clone.spec.js","line":633,"column":20}]}
{"type":"stdout","timestamp":81509.337,"text":"======1308-Step11======\n"}
{"type":"before","callId":"pw:api@79","parentId":"test.step@78","startTime":81510.202,"class":"Test","method":"step","apiName":"locator.fill","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> #max-abandonment-rate-id-input","value":"10.55","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1064,"column":35,"function":"DialerPage.setAbandonmentRate"}]}
{"type":"after","callId":"pw:api@79","endTime":81654.87,"attachments":[]}
{"type":"before","callId":"pw:api@80","parentId":"test.step@78","startTime":81655.803,"class":"Test","method":"step","apiName":"page.waitForTimeout","params":{"timeout":"1000"},"stack":[{"file":"/pages/basePage.js","line":356,"column":25,"function":"DialerPage.wait_for_timeout"}]}
{"type":"after","callId":"pw:api@80","endTime":82669.401,"attachments":[]}
{"type":"before","callId":"expect@81","parentId":"test.step@78","startTime":82670.185,"class":"Test","method":"step","apiName":"expect.not.toHaveClass","params":{"expected":"Object"},"stack":[{"file":"/tests/smoke/test_campaign_record_flow_clone.spec.js","line":639,"column":71}]}
{"type":"after","callId":"expect@81","endTime":82686.461,"attachments":[]}
{"type":"before","callId":"pw:api@82","parentId":"test.step@78","startTime":82687.296,"class":"Test","method":"step","apiName":"page.screenshot","params":{"mask":"undefined","type":"undefined"},"stack":[{"file":"/pages/basePage.js","line":103,"column":60,"function":"DialerPage.saveImage"}]}
{"type":"after","callId":"pw:api@82","endTime":82792.136,"attachments":[]}
{"type":"before","callId":"test.step@83","parentId":"test.step@78","startTime":82792.83,"class":"Test","method":"step","apiName":"allureattach_7ede4187-0b07-4596-9cf6-a4cddd0a994b_capture","params":{},"stack":[{"file":"/node_modules/allure-playwright/src/helpers.ts","line":21,"column":17,"function":"Function.step"}]}
{"type":"before","callId":"attach@84","parentId":"test.step@83","startTime":82793.354,"class":"Test","method":"step","apiName":"attach \"allureattach_7ede4187-0b07-4596-9cf6-a4cddd0a994b_capture\"","params":{},"stack":[{"file":"/node_modules/allure-playwright/src/helpers.ts","line":33,"column":7}]}
{"type":"after","callId":"attach@84","endTime":82794.038,"attachments":[{"name":"allureattach_7ede4187-0b07-4596-9cf6-a4cddd0a994b_capture","contentType":"image/png","sha1":"72fdb1d27fda02b09756ff4ca57eef7c47c14db5"}]}
{"type":"after","callId":"test.step@83","endTime":82794.104,"attachments":[]}
{"type":"after","callId":"test.step@78","endTime":82794.117,"attachments":[]}
{"type":"before","callId":"test.step@85","parentId":"test.step@7","startTime":82794.409,"class":"Test","method":"step","apiName":"Step12-Fill Abandonment Timeout with an valid value","params":{},"stack":[{"file":"/tests/smoke/test_campaign_record_flow_clone.spec.js","line":644,"column":20}]}
{"type":"stdout","timestamp":82794.692,"text":"======1308-Step12======\n"}
{"type":"before","callId":"pw:api@86","parentId":"test.step@85","startTime":82795.723,"class":"Test","method":"step","apiName":"locator.fill","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> #abandonment-timeout-id-input","value":"10","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1069,"column":38,"function":"DialerPage.setAbandonmentTimeout"}]}
{"type":"after","callId":"pw:api@86","endTime":82939.69,"attachments":[]}
{"type":"before","callId":"pw:api@87","parentId":"test.step@85","startTime":82940.633,"class":"Test","method":"step","apiName":"page.waitForTimeout","params":{"timeout":"1000"},"stack":[{"file":"/pages/basePage.js","line":356,"column":25,"function":"DialerPage.wait_for_timeout"}]}
{"type":"after","callId":"pw:api@87","endTime":83956.018,"attachments":[]}
{"type":"before","callId":"expect@88","parentId":"test.step@85","startTime":83956.827,"class":"Test","method":"step","apiName":"expect.not.toHaveClass","params":{"expected":"Object"},"stack":[{"file":"/tests/smoke/test_campaign_record_flow_clone.spec.js","line":647,"column":74}]}
{"type":"after","callId":"expect@88","endTime":83975.226,"attachments":[]}
{"type":"before","callId":"pw:api@89","parentId":"test.step@85","startTime":83976.522,"class":"Test","method":"step","apiName":"page.screenshot","params":{"mask":"undefined","type":"undefined"},"stack":[{"file":"/pages/basePage.js","line":103,"column":60,"function":"DialerPage.saveImage"}]}
{"type":"after","callId":"pw:api@89","endTime":84080.983,"attachments":[]}
{"type":"before","callId":"test.step@90","parentId":"test.step@85","startTime":84081.501,"class":"Test","method":"step","apiName":"allureattach_4d18cfd8-f349-4ba6-b9f9-6c847788b405_capture","params":{},"stack":[{"file":"/node_modules/allure-playwright/src/helpers.ts","line":21,"column":17,"function":"Function.step"}]}
{"type":"before","callId":"attach@91","parentId":"test.step@90","startTime":84081.977,"class":"Test","method":"step","apiName":"attach \"allureattach_4d18cfd8-f349-4ba6-b9f9-6c847788b405_capture\"","params":{},"stack":[{"file":"/node_modules/allure-playwright/src/helpers.ts","line":33,"column":7}]}
{"type":"after","callId":"attach@91","endTime":84082.809,"attachments":[{"name":"allureattach_4d18cfd8-f349-4ba6-b9f9-6c847788b405_capture","contentType":"image/png","sha1":"9788e96750892e1e071f91dfe020e00fcc674af3"}]}
{"type":"after","callId":"test.step@90","endTime":84082.954,"attachments":[]}
{"type":"after","callId":"test.step@85","endTime":84082.967,"attachments":[]}
{"type":"before","callId":"test.step@92","parentId":"test.step@7","startTime":84083.258,"class":"Test","method":"step","apiName":"Step13-Fill Max. Ring Time with an valid value","params":{},"stack":[{"file":"/tests/smoke/test_campaign_record_flow_clone.spec.js","line":651,"column":20}]}
{"type":"stdout","timestamp":84083.441,"text":"======1308-Step13======\n"}
{"type":"before","callId":"pw:api@93","parentId":"test.step@92","startTime":84084.363,"class":"Test","method":"step","apiName":"locator.fill","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> #max-ring-time-id-input","value":"100","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1074,"column":31,"function":"DialerPage.setMaxRingtime"}]}
{"type":"after","callId":"pw:api@93","endTime":84227.415,"attachments":[]}
{"type":"before","callId":"pw:api@94","parentId":"test.step@92","startTime":84228.393,"class":"Test","method":"step","apiName":"page.waitForTimeout","params":{"timeout":"1000"},"stack":[{"file":"/pages/basePage.js","line":356,"column":25,"function":"DialerPage.wait_for_timeout"}]}
{"type":"after","callId":"pw:api@94","endTime":85243.252,"attachments":[]}
{"type":"before","callId":"expect@95","parentId":"test.step@92","startTime":85244.079,"class":"Test","method":"step","apiName":"expect.not.toHaveClass","params":{"expected":"Object"},"stack":[{"file":"/tests/smoke/test_campaign_record_flow_clone.spec.js","line":654,"column":67}]}
{"type":"after","callId":"expect@95","endTime":85262.624,"attachments":[]}
{"type":"before","callId":"pw:api@96","parentId":"test.step@92","startTime":85263.534,"class":"Test","method":"step","apiName":"page.screenshot","params":{"mask":"undefined","type":"undefined"},"stack":[{"file":"/pages/basePage.js","line":103,"column":60,"function":"DialerPage.saveImage"}]}
{"type":"after","callId":"pw:api@96","endTime":85378.036,"attachments":[]}
{"type":"before","callId":"test.step@97","parentId":"test.step@92","startTime":85378.592,"class":"Test","method":"step","apiName":"allureattach_dd2b876d-11e4-4bf4-adcc-dee9db50c784_capture","params":{},"stack":[{"file":"/node_modules/allure-playwright/src/helpers.ts","line":21,"column":17,"function":"Function.step"}]}
{"type":"before","callId":"attach@98","parentId":"test.step@97","startTime":85379.142,"class":"Test","method":"step","apiName":"attach \"allureattach_dd2b876d-11e4-4bf4-adcc-dee9db50c784_capture\"","params":{},"stack":[{"file":"/node_modules/allure-playwright/src/helpers.ts","line":33,"column":7}]}
{"type":"after","callId":"attach@98","endTime":85379.674,"attachments":[{"name":"allureattach_dd2b876d-11e4-4bf4-adcc-dee9db50c784_capture","contentType":"image/png","sha1":"5369b8b34026e3360ecced4e4835eeda702d0e03"}]}
{"type":"after","callId":"test.step@97","endTime":85379.744,"attachments":[]}
{"type":"after","callId":"test.step@92","endTime":85379.758,"attachments":[]}
{"type":"before","callId":"test.step@99","parentId":"test.step@7","startTime":85380.058,"class":"Test","method":"step","apiName":"Step15-click on the next button","params":{},"stack":[{"file":"/tests/smoke/test_campaign_record_flow_clone.spec.js","line":658,"column":20}]}
{"type":"stdout","timestamp":85380.231,"text":"======1308-Step15======\n"}
{"type":"before","callId":"pw:api@100","parentId":"test.step@99","startTime":85381.195,"class":"Test","method":"step","apiName":"locator.click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> [data-testid=\"nextButton\"]","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":963,"column":22,"function":"DialerPage.saveAndNext"}]}
{"type":"after","callId":"pw:api@100","endTime":85630.044,"attachments":[]}
{"type":"before","callId":"pw:api@101","parentId":"test.step@99","startTime":85631.846,"class":"Test","method":"step","apiName":"locator.textContent","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-grid__column.co-grid__column--min>h2.co-heading","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":926,"column":45,"function":"DialerPage.getSettingTitle"}]}
{"type":"after","callId":"pw:api@101","endTime":85711.11,"attachments":[]}
{"type":"before","callId":"expect@102","parentId":"test.step@99","startTime":85711.886,"class":"Test","method":"step","apiName":"expect.toEqual","params":{"expected":"Dialing strategy"},"stack":[{"file":"/tests/smoke/test_campaign_record_flow_clone.spec.js","line":664,"column":62}]}
{"type":"after","callId":"expect@102","endTime":85712.028,"attachments":[]}
{"type":"before","callId":"pw:api@103","parentId":"test.step@99","startTime":85712.815,"class":"Test","method":"step","apiName":"page.screenshot","params":{"mask":"undefined","type":"undefined"},"stack":[{"file":"/pages/basePage.js","line":103,"column":60,"function":"DialerPage.saveImage"}]}
{"type":"after","callId":"pw:api@103","endTime":85831.286,"attachments":[]}
{"type":"before","callId":"test.step@104","parentId":"test.step@99","startTime":85831.957,"class":"Test","method":"step","apiName":"allureattach_846203f7-ba52-4368-bd98-6575a20ef1fe_capture","params":{},"stack":[{"file":"/node_modules/allure-playwright/src/helpers.ts","line":21,"column":17,"function":"Function.step"}]}
{"type":"before","callId":"attach@105","parentId":"test.step@104","startTime":85832.559,"class":"Test","method":"step","apiName":"attach \"allureattach_846203f7-ba52-4368-bd98-6575a20ef1fe_capture\"","params":{},"stack":[{"file":"/node_modules/allure-playwright/src/helpers.ts","line":33,"column":7}]}
{"type":"after","callId":"attach@105","endTime":85833.01,"attachments":[{"name":"allureattach_846203f7-ba52-4368-bd98-6575a20ef1fe_capture","contentType":"image/png","sha1":"d2b0d29bd908791a364b26b0fb51f001abe06abe"}]}
{"type":"after","callId":"test.step@104","endTime":85833.079,"attachments":[]}
{"type":"after","callId":"test.step@99","endTime":85833.096,"attachments":[]}
{"type":"before","callId":"test.step@106","parentId":"test.step@7","startTime":85833.448,"class":"Test","method":"step","apiName":"Step16-Select two caller_ids and click","params":{},"stack":[{"file":"/tests/smoke/test_campaign_record_flow_clone.spec.js","line":668,"column":20}]}
{"type":"stdout","timestamp":85833.587,"text":"======1308-Step16======\n"}
{"type":"before","callId":"pw:api@107","parentId":"test.step@106","startTime":85834.801,"class":"Test","method":"step","apiName":"page.waitForResponse","params":{"info":"Object"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1105,"column":14,"function":"DialerPage.selectCallIDs"}]}
{"type":"before","callId":"pw:api@108","parentId":"test.step@106","startTime":85836.17,"class":"Test","method":"step","apiName":"locator.click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-section__content:has(label[for=\"caller-id-chip-group\"]) a[href=\"#\"]>span","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1124,"column":26,"function":"DialerPage.clickCallIDsLink"}]}
{"type":"after","callId":"pw:api@107","endTime":86255.593,"attachments":[]}
{"type":"after","callId":"pw:api@108","endTime":86282.822,"attachments":[]}
{"type":"before","callId":"pw:api@109","parentId":"test.step@106","startTime":86284.461,"class":"Test","method":"step","apiName":"response.json","params":{},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1108,"column":36,"function":"DialerPage.selectCallIDs"}]}
{"type":"after","callId":"pw:api@109","endTime":86290.816,"attachments":[]}
{"type":"before","callId":"pw:api@110","parentId":"test.step@106","startTime":86291.987,"class":"Test","method":"step","apiName":"locator.waitFor","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> nth=0","strict":"true","omitReturnValue":"true","timeout":"60000","state":"visible"},"stack":[{"file":"/pages/basePage.js","line":319,"column":38,"function":"DialerPage.wait_for_locator"}]}
{"type":"after","callId":"pw:api@110","endTime":86383.307,"attachments":[]}
{"type":"before","callId":"pw:api@111","parentId":"test.step@106","startTime":86384.97,"class":"Test","method":"step","apiName":"locator.waitFor","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> nth=0","strict":"true","omitReturnValue":"true","timeout":"60000","state":"visible"},"stack":[{"file":"/pages/basePage.js","line":319,"column":38,"function":"DialerPage.wait_for_locator"}]}
{"type":"after","callId":"pw:api@111","endTime":86405.948,"attachments":[]}
{"type":"before","callId":"pw:api@112","parentId":"test.step@106","startTime":86406.949,"class":"Test","method":"step","apiName":"locator.waitFor","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> //tbody/tr[1]/td[2] >> div.co-placeholder--animated","strict":"true","omitReturnValue":"true","timeout":"10000","state":"hidden"},"stack":[{"file":"/pages/basePage.js","line":395,"column":40,"function":"DialerPage.wait_for_animated"}]}
{"type":"after","callId":"pw:api@112","endTime":86655.213,"attachments":[]}
{"type":"before","callId":"pw:api@113","parentId":"test.step@106","startTime":86656.9,"class":"Test","method":"step","apiName":"page.waitForTimeout","params":{"timeout":"1000"},"stack":[{"file":"/pages/basePage.js","line":356,"column":25,"function":"DialerPage.wait_for_timeout"}]}
{"type":"after","callId":"pw:api@113","endTime":87682.848,"attachments":[]}
{"type":"before","callId":"pw:api@114","parentId":"test.step@106","startTime":87683.904,"class":"Test","method":"step","apiName":"page.waitForTimeout","params":{"timeout":"3000"},"stack":[{"file":"/pages/basePage.js","line":356,"column":25,"function":"DialerPage.wait_for_timeout"}]}
{"type":"after","callId":"pw:api@114","endTime":90698.903,"attachments":[]}
{"type":"before","callId":"pw:api@115","parentId":"test.step@106","startTime":90700.518,"class":"Test","method":"step","apiName":"locator.waitFor","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> nth=0","strict":"true","omitReturnValue":"true","timeout":"60000","state":"visible"},"stack":[{"file":"/pages/basePage.js","line":319,"column":38,"function":"DialerPage.wait_for_locator"}]}
{"type":"after","callId":"pw:api@115","endTime":90721.846,"attachments":[]}
{"type":"before","callId":"pw:api@116","parentId":"test.step@106","startTime":90723.216,"class":"Test","method":"step","apiName":"locator.waitFor","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> thead >> nth=0","strict":"true","omitReturnValue":"true","timeout":"60000","state":"visible"},"stack":[{"file":"/pages/basePage.js","line":319,"column":38,"function":"DialerPage.wait_for_locator"}]}
{"type":"after","callId":"pw:api@116","endTime":90743.035,"attachments":[]}
{"type":"before","callId":"pw:api@117","parentId":"test.step@106","startTime":90744.384,"class":"Test","method":"step","apiName":"locator.waitFor","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> //tbody/tr[1] >> //td[1] >> div>input >> nth=0","strict":"true","omitReturnValue":"true","timeout":"60000","state":"visible"},"stack":[{"file":"/pages/basePage.js","line":319,"column":38,"function":"DialerPage.wait_for_locator"}]}
{"type":"after","callId":"pw:api@117","endTime":90764.154,"attachments":[]}
{"type":"before","callId":"pw:api@118","parentId":"test.step@106","startTime":90765.19,"class":"Test","method":"step","apiName":"locator.click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> //tbody/tr[1] >> //td[1] >> div>input","strict":"true"},"stack":[{"file":"/pages/basePage.js","line":577,"column":30,"function":"DialerPage.clickTabelCellByIndex"}]}
{"type":"after","callId":"pw:api@118","endTime":90958.783,"attachments":[]}
{"type":"before","callId":"pw:api@119","parentId":"test.step@106","startTime":90959.855,"class":"Test","method":"step","apiName":"locator.isVisible","params":{"selector":"div[data-testid=\"toaster\"] div[data-co-name=\"Message\"]:nth-child(1) div[data-testid]","strict":"true"},"stack":[{"file":"/pages/basePage.js","line":522,"column":37,"function":"DialerPage.waitAndClearToast"}]}
{"type":"after","callId":"pw:api@119","endTime":90982.19,"attachments":[]}
{"type":"before","callId":"pw:api@120","parentId":"test.step@106","startTime":90987.412,"class":"Test","method":"step","apiName":"locator.click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> button>*:text(\"Select\")","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1135,"column":36,"function":"DialerPage.clickConfirmSelectButton"}]}
{"type":"after","callId":"pw:api@120","endTime":91234.321,"attachments":[]}
{"type":"before","callId":"pw:api@121","parentId":"test.step@106","startTime":91235.574,"class":"Test","method":"step","apiName":"page.screenshot","params":{"mask":"undefined","type":"undefined"},"stack":[{"file":"/pages/basePage.js","line":103,"column":60,"function":"DialerPage.saveImage"}]}
{"type":"after","callId":"pw:api@121","endTime":91365.345,"attachments":[]}
{"type":"before","callId":"test.step@122","parentId":"test.step@106","startTime":91365.844,"class":"Test","method":"step","apiName":"allureattach_4e6b55fd-ec43-4e3e-82a6-a9de42f29a51_capture","params":{},"stack":[{"file":"/node_modules/allure-playwright/src/helpers.ts","line":21,"column":17,"function":"Function.step"}]}
{"type":"before","callId":"attach@123","parentId":"test.step@122","startTime":91366.321,"class":"Test","method":"step","apiName":"attach \"allureattach_4e6b55fd-ec43-4e3e-82a6-a9de42f29a51_capture\"","params":{},"stack":[{"file":"/node_modules/allure-playwright/src/helpers.ts","line":33,"column":7}]}
{"type":"after","callId":"attach@123","endTime":91366.797,"attachments":[{"name":"allureattach_4e6b55fd-ec43-4e3e-82a6-a9de42f29a51_capture","contentType":"image/png","sha1":"c5a4e7ae62a107b943818e509579fd74a1647b83"}]}
{"type":"after","callId":"test.step@122","endTime":91366.853,"attachments":[]}
{"type":"after","callId":"test.step@106","endTime":91366.867,"attachments":[]}
{"type":"before","callId":"test.step@124","parentId":"test.step@7","startTime":91367.142,"class":"Test","method":"step","apiName":"Step17-Select week days for calling hours","params":{},"stack":[{"file":"/tests/smoke/test_campaign_record_flow_clone.spec.js","line":679,"column":20}]}
{"type":"stdout","timestamp":91367.263,"text":"======1308-Step17======\n"}
{"type":"before","callId":"pw:api@125","parentId":"test.step@124","startTime":91368.214,"class":"Test","method":"step","apiName":"locator.click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> :text-is('MON')","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1141,"column":67,"function":"DialerPage.setCallingDays"}]}
{"type":"after","callId":"pw:api@125","endTime":91836.803,"attachments":[]}
{"type":"before","callId":"pw:api@126","parentId":"test.step@124","startTime":91837.95,"class":"Test","method":"step","apiName":"locator.click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> :text-is('THU')","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1141,"column":67,"function":"DialerPage.setCallingDays"}]}
{"type":"after","callId":"pw:api@126","endTime":92040.847,"attachments":[]}
{"type":"before","callId":"pw:api@127","parentId":"test.step@124","startTime":92042.443,"class":"Test","method":"step","apiName":"page.screenshot","params":{"mask":"undefined","type":"undefined"},"stack":[{"file":"/pages/basePage.js","line":103,"column":60,"function":"DialerPage.saveImage"}]}
{"type":"after","callId":"pw:api@127","endTime":92164.471,"attachments":[]}
{"type":"before","callId":"test.step@128","parentId":"test.step@124","startTime":92165.247,"class":"Test","method":"step","apiName":"allureattach_d6c26dc8-5285-4a4e-8bda-5e6e359cc389_capture","params":{},"stack":[{"file":"/node_modules/allure-playwright/src/helpers.ts","line":21,"column":17,"function":"Function.step"}]}
{"type":"before","callId":"attach@129","parentId":"test.step@128","startTime":92165.795,"class":"Test","method":"step","apiName":"attach \"allureattach_d6c26dc8-5285-4a4e-8bda-5e6e359cc389_capture\"","params":{},"stack":[{"file":"/node_modules/allure-playwright/src/helpers.ts","line":33,"column":7}]}
{"type":"after","callId":"attach@129","endTime":92166.254,"attachments":[{"name":"allureattach_d6c26dc8-5285-4a4e-8bda-5e6e359cc389_capture","contentType":"image/png","sha1":"a93005e6cc558d90574898be550f209f32133668"}]}
{"type":"after","callId":"test.step@128","endTime":92166.405,"attachments":[]}
{"type":"after","callId":"test.step@124","endTime":92166.421,"attachments":[]}
{"type":"before","callId":"test.step@130","parentId":"test.step@7","startTime":92166.857,"class":"Test","method":"step","apiName":"Step19-Select valid hours to the campaign.","params":{},"stack":[{"file":"/tests/smoke/test_campaign_record_flow_clone.spec.js","line":688,"column":20}]}
{"type":"stdout","timestamp":92167.022,"text":"======1308-Step19======\n"}
{"type":"before","callId":"pw:api@131","parentId":"test.step@130","startTime":92167.887,"class":"Test","method":"step","apiName":"locator.click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.sc-bdVaJa.cxEcVj input","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1150,"column":33,"function":"DialerPage.setCallingFromHour"}]}
{"type":"after","callId":"pw:api@131","endTime":92352.463,"attachments":[]}
{"type":"before","callId":"pw:api@132","parentId":"test.step@130","startTime":92353.898,"class":"Test","method":"step","apiName":"locator.fill","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> #timepicker-in-popup-id-From-hours input","value":"07","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1154,"column":29,"function":"DialerPage.setFromHour"}]}
{"type":"after","callId":"pw:api@132","endTime":92516.18,"attachments":[]}
{"type":"before","callId":"pw:api@133","parentId":"test.step@130","startTime":92517.207,"class":"Test","method":"step","apiName":"locator.fill","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> #timepicker-in-popup-id-From-minutes input","value":"30","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1158,"column":31,"function":"DialerPage.setFromMinute"}]}
{"type":"after","callId":"pw:api@133","endTime":92674.475,"attachments":[]}
{"type":"before","callId":"pw:api@134","parentId":"test.step@130","startTime":92675.609,"class":"Test","method":"step","apiName":"locator.textContent","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> [for=\"timepicker-in-popup-id-From-meridian\"]","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1174,"column":57,"function":"DialerPage.setFromMeridian"}]}
{"type":"after","callId":"pw:api@134","endTime":92693.615,"attachments":[]}
{"type":"stdout","timestamp":92693.684,"text":"no need to change from meridian setting\n"}
{"type":"before","callId":"pw:api@135","parentId":"test.step@130","startTime":92695.159,"class":"Test","method":"step","apiName":"locator.click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div>label:not([for]):has(abbr)","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1146,"column":29,"function":"DialerPage.blur"}]}
{"type":"after","callId":"pw:api@135","endTime":92874.918,"attachments":[]}
{"type":"before","callId":"pw:api@136","parentId":"test.step@130","startTime":92876.366,"class":"Test","method":"step","apiName":"locator.click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.sc-bdVaJa.gpIkRm input","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1170,"column":31,"function":"DialerPage.setCallingToHour"}]}
{"type":"after","callId":"pw:api@136","endTime":93087.431,"attachments":[]}
{"type":"before","callId":"pw:api@137","parentId":"test.step@130","startTime":93088.401,"class":"Test","method":"step","apiName":"locator.fill","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> #timepicker-in-popup-id-To-hours input","value":"11","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1162,"column":27,"function":"DialerPage.setToHour"}]}
{"type":"after","callId":"pw:api@137","endTime":93297.458,"attachments":[]}
{"type":"before","callId":"pw:api@138","parentId":"test.step@130","startTime":93298.76,"class":"Test","method":"step","apiName":"locator.fill","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> #timepicker-in-popup-id-To-minutes input","value":"00","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1166,"column":29,"function":"DialerPage.setToMinute"}]}
{"type":"after","callId":"pw:api@138","endTime":93438.477,"attachments":[]}
{"type":"before","callId":"pw:api@139","parentId":"test.step@130","startTime":93439.493,"class":"Test","method":"step","apiName":"locator.textContent","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> [for=\"timepicker-in-popup-id-To-meridian\"]","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1183,"column":53,"function":"DialerPage.setToMeridian"}]}
{"type":"after","callId":"pw:api@139","endTime":93457.186,"attachments":[]}
{"type":"stdout","timestamp":93457.235,"text":"no need to change from meridian setting\n"}
{"type":"before","callId":"pw:api@140","parentId":"test.step@130","startTime":93458.101,"class":"Test","method":"step","apiName":"locator.click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div>label:not([for]):has(abbr)","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1146,"column":29,"function":"DialerPage.blur"}]}
{"type":"after","callId":"pw:api@140","endTime":93620.664,"attachments":[]}
{"type":"before","callId":"pw:api@141","parentId":"test.step@130","startTime":93621.536,"class":"Test","method":"step","apiName":"page.screenshot","params":{"mask":"undefined","type":"undefined"},"stack":[{"file":"/pages/basePage.js","line":103,"column":60,"function":"DialerPage.saveImage"}]}
{"type":"after","callId":"pw:api@141","endTime":93728.439,"attachments":[]}
{"type":"before","callId":"test.step@142","parentId":"test.step@130","startTime":93728.946,"class":"Test","method":"step","apiName":"allureattach_2046da33-449c-471f-8d94-f538f649b33c_capture","params":{},"stack":[{"file":"/node_modules/allure-playwright/src/helpers.ts","line":21,"column":17,"function":"Function.step"}]}
{"type":"before","callId":"attach@143","parentId":"test.step@142","startTime":93729.413,"class":"Test","method":"step","apiName":"attach \"allureattach_2046da33-449c-471f-8d94-f538f649b33c_capture\"","params":{},"stack":[{"file":"/node_modules/allure-playwright/src/helpers.ts","line":33,"column":7}]}
{"type":"after","callId":"attach@143","endTime":93729.947,"attachments":[{"name":"allureattach_2046da33-449c-471f-8d94-f538f649b33c_capture","contentType":"image/png","sha1":"7ee00192d67e239a2712b1040781eb20e9c73916"}]}
{"type":"after","callId":"test.step@142","endTime":93730.017,"attachments":[]}
{"type":"after","callId":"test.step@130","endTime":93730.037,"attachments":[]}
{"type":"before","callId":"test.step@144","parentId":"test.step@7","startTime":93730.406,"class":"Test","method":"step","apiName":"Step21-Fill Global Max.Attempts with a valid value (e.g.3) - between 1 and 100.","params":{},"stack":[{"file":"/tests/smoke/test_campaign_record_flow_clone.spec.js","line":707,"column":20}]}
{"type":"stdout","timestamp":93730.545,"text":"======1308-Step21======\n"}
{"type":"before","callId":"pw:api@145","parentId":"test.step@144","startTime":93731.519,"class":"Test","method":"step","apiName":"locator.fill","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> #max-attempts-per-record-id-input","value":"3","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1192,"column":37,"function":"DialerPage.setGlobalMaxAttempts"}]}
{"type":"after","callId":"pw:api@145","endTime":93884.963,"attachments":[]}
{"type":"before","callId":"pw:api@146","parentId":"test.step@144","startTime":93885.882,"class":"Test","method":"step","apiName":"page.waitForTimeout","params":{"timeout":"1000"},"stack":[{"file":"/pages/basePage.js","line":356,"column":25,"function":"DialerPage.wait_for_timeout"}]}
{"type":"after","callId":"pw:api@146","endTime":94905.362,"attachments":[]}
{"type":"before","callId":"expect@147","parentId":"test.step@144","startTime":94906.308,"class":"Test","method":"step","apiName":"expect.toBeHidden","params":{},"stack":[{"file":"/tests/smoke/test_campaign_record_flow_clone.spec.js","line":713,"column":76}]}
{"type":"after","callId":"expect@147","endTime":94928.006,"attachments":[]}
{"type":"before","callId":"pw:api@148","parentId":"test.step@144","startTime":94929.016,"class":"Test","method":"step","apiName":"page.screenshot","params":{"mask":"undefined","type":"undefined"},"stack":[{"file":"/pages/basePage.js","line":103,"column":60,"function":"DialerPage.saveImage"}]}
{"type":"after","callId":"pw:api@148","endTime":95044.376,"attachments":[]}
{"type":"before","callId":"test.step@149","parentId":"test.step@144","startTime":95044.839,"class":"Test","method":"step","apiName":"allureattach_7ede2879-2dcb-46fe-ab77-d039fd8bc7d8_capture","params":{},"stack":[{"file":"/node_modules/allure-playwright/src/helpers.ts","line":21,"column":17,"function":"Function.step"}]}
{"type":"before","callId":"attach@150","parentId":"test.step@149","startTime":95045.281,"class":"Test","method":"step","apiName":"attach \"allureattach_7ede2879-2dcb-46fe-ab77-d039fd8bc7d8_capture\"","params":{},"stack":[{"file":"/node_modules/allure-playwright/src/helpers.ts","line":33,"column":7}]}
{"type":"after","callId":"attach@150","endTime":95045.699,"attachments":[{"name":"allureattach_7ede2879-2dcb-46fe-ab77-d039fd8bc7d8_capture","contentType":"image/png","sha1":"0c8e2047ba55b8d6572c0a39628261455976e099"}]}
{"type":"after","callId":"test.step@149","endTime":95045.747,"attachments":[]}
{"type":"after","callId":"test.step@144","endTime":95045.759,"attachments":[]}
{"type":"before","callId":"test.step@151","parentId":"test.step@7","startTime":95046.036,"class":"Test","method":"step","apiName":"Step22-Fill Default Retry Period with valid value","params":{},"stack":[{"file":"/tests/smoke/test_campaign_record_flow_clone.spec.js","line":718,"column":20}]}
{"type":"stdout","timestamp":95046.285,"text":"======1308-Step22======\n"}
{"type":"before","callId":"pw:api@152","parentId":"test.step@151","startTime":95047.246,"class":"Test","method":"step","apiName":"locator.click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> #retry-period-id-dropdown","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1213,"column":36,"function":"DialerPage.setRetryPeriod"}]}
{"type":"after","callId":"pw:api@152","endTime":95226.126,"attachments":[]}
{"type":"before","callId":"pw:api@153","parentId":"test.step@151","startTime":95227.62,"class":"Test","method":"step","apiName":"locator.click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div[data-transition=\"entered\"][data-co-name=\"Popup\"] ul >> li *:text(\"minute(s)\")","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1215,"column":71,"function":"DialerPage.setRetryPeriod"}]}
{"type":"after","callId":"pw:api@153","endTime":95423.146,"attachments":[]}
{"type":"before","callId":"pw:api@154","parentId":"test.step@151","startTime":95424.44,"class":"Test","method":"step","apiName":"locator.fill","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> #retry-period-id-input","value":"1","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1226,"column":31,"function":"DialerPage.setRetryPeriod"}]}
{"type":"after","callId":"pw:api@154","endTime":95580.469,"attachments":[]}
{"type":"before","callId":"pw:api@155","parentId":"test.step@151","startTime":95581.635,"class":"Test","method":"step","apiName":"page.waitForTimeout","params":{"timeout":"1000"},"stack":[{"file":"/pages/basePage.js","line":356,"column":25,"function":"DialerPage.wait_for_timeout"}]}
{"type":"after","callId":"pw:api@155","endTime":96598.403,"attachments":[]}
{"type":"before","callId":"expect@156","parentId":"test.step@151","startTime":96599.154,"class":"Test","method":"step","apiName":"expect.toBeHidden","params":{},"stack":[{"file":"/tests/smoke/test_campaign_record_flow_clone.spec.js","line":721,"column":70}]}
{"type":"after","callId":"expect@156","endTime":96619.763,"attachments":[]}
{"type":"before","callId":"pw:api@157","parentId":"test.step@151","startTime":96620.769,"class":"Test","method":"step","apiName":"page.screenshot","params":{"mask":"undefined","type":"undefined"},"stack":[{"file":"/pages/basePage.js","line":103,"column":60,"function":"DialerPage.saveImage"}]}
{"type":"after","callId":"pw:api@157","endTime":96720.628,"attachments":[]}
{"type":"before","callId":"test.step@158","parentId":"test.step@151","startTime":96721.071,"class":"Test","method":"step","apiName":"allureattach_a1c0e711-a74c-458a-90a5-7134c1a52408_capture","params":{},"stack":[{"file":"/node_modules/allure-playwright/src/helpers.ts","line":21,"column":17,"function":"Function.step"}]}
{"type":"before","callId":"attach@159","parentId":"test.step@158","startTime":96721.504,"class":"Test","method":"step","apiName":"attach \"allureattach_a1c0e711-a74c-458a-90a5-7134c1a52408_capture\"","params":{},"stack":[{"file":"/node_modules/allure-playwright/src/helpers.ts","line":33,"column":7}]}
{"type":"after","callId":"attach@159","endTime":96721.969,"attachments":[{"name":"allureattach_a1c0e711-a74c-458a-90a5-7134c1a52408_capture","contentType":"image/png","sha1":"f939876548e9c3448a9cb044de6f5a55f4dec712"}]}
{"type":"after","callId":"test.step@158","endTime":96722.023,"attachments":[]}
{"type":"after","callId":"test.step@151","endTime":96722.035,"attachments":[]}
{"type":"before","callId":"test.step@160","parentId":"test.step@7","startTime":96722.317,"class":"Test","method":"step","apiName":"Step23-In System Dispositions click on the link: admin area of your Talkdesk account.","params":{},"stack":[{"file":"/tests/smoke/test_campaign_record_flow_clone.spec.js","line":726,"column":20}]}
{"type":"stdout","timestamp":96722.49,"text":"======1308-Step23======\n"}
{"type":"stdout","timestamp":96722.505,"text":"the system disposition is remove in new UI version\n"}
{"type":"before","callId":"pw:api@161","parentId":"test.step@160","startTime":96723.345,"class":"Test","method":"step","apiName":"page.screenshot","params":{"mask":"undefined","type":"undefined"},"stack":[{"file":"/pages/basePage.js","line":103,"column":60,"function":"DialerPage.saveImage"}]}
{"type":"after","callId":"pw:api@161","endTime":96821.774,"attachments":[]}
{"type":"before","callId":"test.step@162","parentId":"test.step@160","startTime":96822.22,"class":"Test","method":"step","apiName":"allureattach_d0652d16-1c75-4321-80b6-39acbda1549a_capture","params":{},"stack":[{"file":"/node_modules/allure-playwright/src/helpers.ts","line":21,"column":17,"function":"Function.step"}]}
{"type":"before","callId":"attach@163","parentId":"test.step@162","startTime":96822.678,"class":"Test","method":"step","apiName":"attach \"allureattach_d0652d16-1c75-4321-80b6-39acbda1549a_capture\"","params":{},"stack":[{"file":"/node_modules/allure-playwright/src/helpers.ts","line":33,"column":7}]}
{"type":"after","callId":"attach@163","endTime":96823.139,"attachments":[{"name":"allureattach_d0652d16-1c75-4321-80b6-39acbda1549a_capture","contentType":"image/png","sha1":"f939876548e9c3448a9cb044de6f5a55f4dec712"}]}
{"type":"after","callId":"test.step@162","endTime":96823.191,"attachments":[]}
{"type":"after","callId":"test.step@160","endTime":96823.202,"attachments":[]}
{"type":"before","callId":"test.step@164","parentId":"test.step@7","startTime":96823.47,"class":"Test","method":"step","apiName":"Step26-Click on the next button","params":{},"stack":[{"file":"/tests/smoke/test_campaign_record_flow_clone.spec.js","line":746,"column":20}]}
{"type":"stdout","timestamp":96823.596,"text":"======1308-Step26======\n"}
{"type":"before","callId":"pw:api@165","parentId":"test.step@164","startTime":96824.646,"class":"Test","method":"step","apiName":"page.waitForResponse","params":{"info":"Object"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1312,"column":14,"function":"DialerPage.transiteToAgentStep"}]}
{"type":"before","callId":"pw:api@166","parentId":"test.step@164","startTime":96825.592,"class":"Test","method":"step","apiName":"locator.click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> [data-testid=\"nextButton\"]","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":963,"column":22,"function":"DialerPage.saveAndNext"}]}
{"type":"after","callId":"pw:api@166","endTime":97047.024,"attachments":[]}
{"type":"after","callId":"pw:api@165","endTime":97119.403,"attachments":[]}
{"type":"before","callId":"pw:api@167","parentId":"test.step@164","startTime":97120.425,"class":"Test","method":"step","apiName":"locator.textContent","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-grid__column.co-grid__column--min>h2.co-heading","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":926,"column":45,"function":"DialerPage.getSettingTitle"}]}
{"type":"after","callId":"pw:api@167","endTime":97176.57,"attachments":[]}
{"type":"before","callId":"expect@168","parentId":"test.step@164","startTime":97177.734,"class":"Test","method":"step","apiName":"expect.toEqual","params":{"expected":"Agents"},"stack":[{"file":"/tests/smoke/test_campaign_record_flow_clone.spec.js","line":752,"column":62}]}
{"type":"after","callId":"expect@168","endTime":97177.916,"attachments":[]}
{"type":"before","callId":"pw:api@169","parentId":"test.step@164","startTime":97179.156,"class":"Test","method":"step","apiName":"page.screenshot","params":{"mask":"undefined","type":"undefined"},"stack":[{"file":"/pages/basePage.js","line":103,"column":60,"function":"DialerPage.saveImage"}]}
{"type":"after","callId":"pw:api@169","endTime":97276.437,"attachments":[]}
{"type":"before","callId":"test.step@170","parentId":"test.step@164","startTime":97276.896,"class":"Test","method":"step","apiName":"allureattach_755a2bd2-2b60-43a5-8222-23aaea0c38c6_capture","params":{},"stack":[{"file":"/node_modules/allure-playwright/src/helpers.ts","line":21,"column":17,"function":"Function.step"}]}
{"type":"before","callId":"attach@171","parentId":"test.step@170","startTime":97277.342,"class":"Test","method":"step","apiName":"attach \"allureattach_755a2bd2-2b60-43a5-8222-23aaea0c38c6_capture\"","params":{},"stack":[{"file":"/node_modules/allure-playwright/src/helpers.ts","line":33,"column":7}]}
{"type":"after","callId":"attach@171","endTime":97277.758,"attachments":[{"name":"allureattach_755a2bd2-2b60-43a5-8222-23aaea0c38c6_capture","contentType":"image/png","sha1":"412a11196335d6f2bbc71eadc8d5e467d41bd2ca"}]}
{"type":"after","callId":"test.step@170","endTime":97277.805,"attachments":[]}
{"type":"after","callId":"test.step@164","endTime":97277.818,"attachments":[]}
{"type":"before","callId":"test.step@172","parentId":"test.step@7","startTime":97278.12,"class":"Test","method":"step","apiName":"Step29-Filter agents by their ring group","params":{},"stack":[{"file":"/tests/smoke/test_campaign_record_flow_clone.spec.js","line":756,"column":20}]}
{"type":"stdout","timestamp":97278.278,"text":"======1308-Step29======\n"}
{"type":"before","callId":"pw:api@173","parentId":"test.step@172","startTime":97279.446,"class":"Test","method":"step","apiName":"page.waitForResponse","params":{"info":"Object"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1340,"column":14,"function":"DialerPage.selectRingGroups"}]}
{"type":"before","callId":"pw:api@174","parentId":"test.step@172","startTime":97280.561,"class":"Test","method":"step","apiName":"locator.click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> //*[contains(text(),\"Filters\")]","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1341,"column":25,"function":"DialerPage.selectRingGroups"}]}
{"type":"after","callId":"pw:api@174","endTime":97496.461,"attachments":[]}
{"type":"after","callId":"pw:api@173","endTime":97610.834,"attachments":[]}
{"type":"before","callId":"pw:api@175","parentId":"test.step@172","startTime":97611.728,"class":"Test","method":"step","apiName":"page.waitForTimeout","params":{"timeout":"2000"},"stack":[{"file":"/pages/basePage.js","line":356,"column":25,"function":"DialerPage.wait_for_timeout"}]}
{"type":"after","callId":"pw:api@175","endTime":99634.102,"attachments":[]}
{"type":"before","callId":"pw:api@176","parentId":"test.step@172","startTime":99635.016,"class":"Test","method":"step","apiName":"locator.click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div[data-pendo-campaignform-agents-filters-ringgroupsmulti-select]","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1344,"column":27,"function":"DialerPage.selectRingGroups"}]}
{"type":"after","callId":"pw:api@176","endTime":99804.418,"attachments":[]}
{"type":"before","callId":"pw:api@177","parentId":"test.step@172","startTime":99805.46,"class":"Test","method":"step","apiName":"page.waitForResponse","params":{"info":"Object"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1353,"column":16,"function":"DialerPage.selectRingGroups"}]}
{"type":"before","callId":"pw:api@178","parentId":"test.step@172","startTime":99806.367,"class":"Test","method":"step","apiName":"locator.fill","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div[data-pendo-campaignform-agents-filters-ringgroupsmulti-select] >> input[type=\"search\"]","value":"agents","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1354,"column":38,"function":"DialerPage.selectRingGroups"}]}
{"type":"after","callId":"pw:api@178","endTime":99948.905,"attachments":[]}
{"type":"after","callId":"pw:api@177","endTime":100353.892,"attachments":[]}
{"type":"before","callId":"pw:api@179","parentId":"test.step@172","startTime":100354.865,"class":"Test","method":"step","apiName":"response.json","params":{},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1356,"column":32,"function":"DialerPage.selectRingGroups"}]}
{"type":"before","callId":"pw:api@180","parentId":"test.step@172","startTime":100355.691,"class":"Test","method":"step","apiName":"locator.click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div[data-pendo-campaignform-agents-filters-ringgroupsmulti-select] >> div.co-dropdown__menu ul.co-list >> //*[text()=\"agents\"]","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1361,"column":85,"function":"DialerPage.selectRingGroups"}]}
{"type":"after","callId":"pw:api@179","endTime":100359.778,"attachments":[]}
{"type":"after","callId":"pw:api@180","endTime":100553.073,"attachments":[]}
{"type":"before","callId":"pw:api@181","parentId":"test.step@172","startTime":100554.217,"class":"Test","method":"step","apiName":"page.waitForRequest","params":{"info":"Object"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1364,"column":16,"function":"DialerPage.selectRingGroups"}]}
{"type":"before","callId":"pw:api@182","parentId":"test.step@172","startTime":100555.146,"class":"Test","method":"step","apiName":"locator.click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> button[data-testid=\"applyButton\"]","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1365,"column":31,"function":"DialerPage.selectRingGroups"}]}
{"type":"after","callId":"pw:api@181","endTime":100671.478,"attachments":[]}
{"type":"after","callId":"pw:api@182","endTime":100781.637,"attachments":[]}
{"type":"before","callId":"expect@183","parentId":"test.step@172","startTime":100782.904,"class":"Test","method":"step","apiName":"expect.toContain","params":{"expected":"agents"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1371,"column":31,"function":"DialerPage.selectRingGroups"}]}
{"type":"after","callId":"expect@183","endTime":100783.103,"attachments":[]}
{"type":"before","callId":"pw:api@184","parentId":"test.step@172","startTime":100783.923,"class":"Test","method":"step","apiName":"locator.click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-toolbar+div.co-toolbar >> button>span.co--truncate","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1380,"column":40,"function":"DialerPage.clickClearFilterSettings"}]}
{"type":"after","callId":"pw:api@184","endTime":101369.915,"attachments":[]}
{"type":"before","callId":"pw:api@185","parentId":"test.step@172","startTime":101370.992,"class":"Test","method":"step","apiName":"page.waitForResponse","params":{"info":"Object"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1340,"column":14,"function":"DialerPage.selectRingGroups"}]}
{"type":"before","callId":"pw:api@186","parentId":"test.step@172","startTime":101371.833,"class":"Test","method":"step","apiName":"locator.click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> //*[contains(text(),\"Filters\")]","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1341,"column":25,"function":"DialerPage.selectRingGroups"}]}
{"type":"after","callId":"pw:api@186","endTime":101650.739,"attachments":[]}
{"type":"after","callId":"pw:api@185","endTime":101735.797,"attachments":[]}
{"type":"before","callId":"pw:api@187","parentId":"test.step@172","startTime":101736.705,"class":"Test","method":"step","apiName":"page.waitForTimeout","params":{"timeout":"2000"},"stack":[{"file":"/pages/basePage.js","line":356,"column":25,"function":"DialerPage.wait_for_timeout"}]}
{"type":"after","callId":"pw:api@187","endTime":103764.731,"attachments":[]}
{"type":"before","callId":"pw:api@188","parentId":"test.step@172","startTime":103765.574,"class":"Test","method":"step","apiName":"locator.click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div[data-pendo-campaignform-agents-filters-ringgroupsmulti-select]","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1344,"column":27,"function":"DialerPage.selectRingGroups"}]}
{"type":"after","callId":"pw:api@188","endTime":103937.662,"attachments":[]}
{"type":"before","callId":"pw:api@189","parentId":"test.step@172","startTime":103938.679,"class":"Test","method":"step","apiName":"page.waitForResponse","params":{"info":"Object"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1353,"column":16,"function":"DialerPage.selectRingGroups"}]}
{"type":"before","callId":"pw:api@190","parentId":"test.step@172","startTime":103939.529,"class":"Test","method":"step","apiName":"locator.fill","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div[data-pendo-campaignform-agents-filters-ringgroupsmulti-select] >> input[type=\"search\"]","value":"agents","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1354,"column":38,"function":"DialerPage.selectRingGroups"}]}
{"type":"after","callId":"pw:api@190","endTime":104079.794,"attachments":[]}
{"type":"after","callId":"pw:api@189","endTime":104467.052,"attachments":[]}
{"type":"before","callId":"pw:api@191","parentId":"test.step@172","startTime":104468.011,"class":"Test","method":"step","apiName":"response.json","params":{},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1356,"column":32,"function":"DialerPage.selectRingGroups"}]}
{"type":"before","callId":"pw:api@192","parentId":"test.step@172","startTime":104468.857,"class":"Test","method":"step","apiName":"locator.click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div[data-pendo-campaignform-agents-filters-ringgroupsmulti-select] >> div.co-dropdown__menu ul.co-list >> //*[text()=\"agents\"]","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1361,"column":85,"function":"DialerPage.selectRingGroups"}]}
{"type":"after","callId":"pw:api@191","endTime":104472.927,"attachments":[]}
{"type":"after","callId":"pw:api@192","endTime":104649.356,"attachments":[]}
{"type":"before","callId":"pw:api@193","parentId":"test.step@172","startTime":104650.38,"class":"Test","method":"step","apiName":"page.waitForResponse","params":{"info":"Object"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1353,"column":16,"function":"DialerPage.selectRingGroups"}]}
{"type":"before","callId":"pw:api@194","parentId":"test.step@172","startTime":104651.273,"class":"Test","method":"step","apiName":"locator.fill","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div[data-pendo-campaignform-agents-filters-ringgroupsmulti-select] >> input[type=\"search\"]","value":"test","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1354,"column":38,"function":"DialerPage.selectRingGroups"}]}
{"type":"after","callId":"pw:api@194","endTime":104791.125,"attachments":[]}
{"type":"after","callId":"pw:api@193","endTime":105110.023,"attachments":[]}
{"type":"before","callId":"pw:api@195","parentId":"test.step@172","startTime":105111.022,"class":"Test","method":"step","apiName":"response.json","params":{},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1356,"column":32,"function":"DialerPage.selectRingGroups"}]}
{"type":"before","callId":"pw:api@196","parentId":"test.step@172","startTime":105111.842,"class":"Test","method":"step","apiName":"locator.click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div[data-pendo-campaignform-agents-filters-ringgroupsmulti-select] >> div.co-dropdown__menu ul.co-list >> //*[text()=\"test\"]","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1361,"column":85,"function":"DialerPage.selectRingGroups"}]}
{"type":"after","callId":"pw:api@195","endTime":105117.09,"attachments":[]}
{"type":"after","callId":"pw:api@196","endTime":105293.999,"attachments":[]}
{"type":"before","callId":"pw:api@197","parentId":"test.step@172","startTime":105295.067,"class":"Test","method":"step","apiName":"page.waitForRequest","params":{"info":"Object"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1364,"column":16,"function":"DialerPage.selectRingGroups"}]}
{"type":"before","callId":"pw:api@198","parentId":"test.step@172","startTime":105296.018,"class":"Test","method":"step","apiName":"locator.click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> button[data-testid=\"applyButton\"]","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1365,"column":31,"function":"DialerPage.selectRingGroups"}]}
{"type":"after","callId":"pw:api@197","endTime":105403.273,"attachments":[]}
{"type":"after","callId":"pw:api@198","endTime":105514.945,"attachments":[]}
{"type":"before","callId":"expect@199","parentId":"test.step@172","startTime":105516.351,"class":"Test","method":"step","apiName":"expect.toContain","params":{"expected":"agents"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1371,"column":31,"function":"DialerPage.selectRingGroups"}]}
{"type":"after","callId":"expect@199","endTime":105516.485,"attachments":[]}
{"type":"before","callId":"expect@200","parentId":"test.step@172","startTime":105517.296,"class":"Test","method":"step","apiName":"expect.toContain","params":{"expected":"test"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1371,"column":31,"function":"DialerPage.selectRingGroups"}]}
{"type":"after","callId":"expect@200","endTime":105517.397,"attachments":[]}
{"type":"before","callId":"pw:api@201","parentId":"test.step@172","startTime":105518.152,"class":"Test","method":"step","apiName":"locator.click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-toolbar+div.co-toolbar >> button>span.co--truncate","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1380,"column":40,"function":"DialerPage.clickClearFilterSettings"}]}
{"type":"after","callId":"pw:api@201","endTime":106127.411,"attachments":[]}
{"type":"before","callId":"pw:api@202","parentId":"test.step@172","startTime":106128.438,"class":"Test","method":"step","apiName":"page.waitForResponse","params":{"info":"Object"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1340,"column":14,"function":"DialerPage.selectRingGroups"}]}
{"type":"before","callId":"pw:api@203","parentId":"test.step@172","startTime":106129.285,"class":"Test","method":"step","apiName":"locator.click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> //*[contains(text(),\"Filters\")]","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1341,"column":25,"function":"DialerPage.selectRingGroups"}]}
{"type":"after","callId":"pw:api@203","endTime":106386.134,"attachments":[]}
{"type":"after","callId":"pw:api@202","endTime":106469.597,"attachments":[]}
{"type":"before","callId":"pw:api@204","parentId":"test.step@172","startTime":106470.534,"class":"Test","method":"step","apiName":"page.waitForTimeout","params":{"timeout":"2000"},"stack":[{"file":"/pages/basePage.js","line":356,"column":25,"function":"DialerPage.wait_for_timeout"}]}
{"type":"after","callId":"pw:api@204","endTime":108498.223,"attachments":[]}
{"type":"before","callId":"pw:api@205","parentId":"test.step@172","startTime":108499.152,"class":"Test","method":"step","apiName":"locator.click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div[data-pendo-campaignform-agents-filters-ringgroupsmulti-select]","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1344,"column":27,"function":"DialerPage.selectRingGroups"}]}
{"type":"after","callId":"pw:api@205","endTime":108675.385,"attachments":[]}
{"type":"before","callId":"pw:api@206","parentId":"test.step@172","startTime":108676.762,"class":"Test","method":"step","apiName":"locator.click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div[data-pendo-campaignform-agents-filters-ringgroupsmulti-select] >> div.co-dropdown__menu ul.co-list >> //*[text()=\"Select All\"]","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1346,"column":72,"function":"DialerPage.selectRingGroups"}]}
{"type":"after","callId":"pw:api@206","endTime":108950.965,"attachments":[]}
{"type":"before","callId":"pw:api@207","parentId":"test.step@172","startTime":108951.838,"class":"Test","method":"step","apiName":"locator.click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> button[data-testid=\"applyButton\"]","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1347,"column":29,"function":"DialerPage.selectRingGroups"}]}
{"type":"after","callId":"pw:api@207","endTime":109171.794,"attachments":[]}
{"type":"before","callId":"pw:api@208","parentId":"test.step@172","startTime":109172.676,"class":"Test","method":"step","apiName":"locator.click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-toolbar+div.co-toolbar >> button>span.co--truncate","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1380,"column":40,"function":"DialerPage.clickClearFilterSettings"}]}
{"type":"after","callId":"pw:api@208","endTime":109791.045,"attachments":[]}
{"type":"before","callId":"pw:api@209","parentId":"test.step@172","startTime":109791.995,"class":"Test","method":"step","apiName":"locator.click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> //*[contains(text(),\"Filters\")]","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1384,"column":24,"function":"DialerPage.clickFilterButton"}]}
{"type":"after","callId":"pw:api@209","endTime":110040.01,"attachments":[]}
{"type":"before","callId":"pw:api@210","parentId":"test.step@172","startTime":110041.007,"class":"Test","method":"step","apiName":"locator.click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> button[data-testid=\"cancelButton\"]","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1388,"column":29,"function":"DialerPage.cancelFilterPanel"}]}
{"type":"after","callId":"pw:api@210","endTime":110595.517,"attachments":[]}
{"type":"before","callId":"expect@211","parentId":"test.step@172","startTime":110596.378,"class":"Test","method":"step","apiName":"expect.toBeHidden","params":{},"stack":[{"file":"/tests/smoke/test_campaign_record_flow_clone.spec.js","line":775,"column":51}]}
{"type":"after","callId":"expect@211","endTime":110617.751,"attachments":[]}
{"type":"before","callId":"pw:api@212","parentId":"test.step@172","startTime":110618.65,"class":"Test","method":"step","apiName":"page.screenshot","params":{"mask":"undefined","type":"undefined"},"stack":[{"file":"/pages/basePage.js","line":103,"column":60,"function":"DialerPage.saveImage"}]}
{"type":"after","callId":"pw:api@212","endTime":110727.301,"attachments":[]}
{"type":"before","callId":"test.step@213","parentId":"test.step@172","startTime":110727.81,"class":"Test","method":"step","apiName":"allureattach_6a9f95f9-ef27-4028-a563-f9e6e432b2ce_capture","params":{},"stack":[{"file":"/node_modules/allure-playwright/src/helpers.ts","line":21,"column":17,"function":"Function.step"}]}
{"type":"before","callId":"attach@214","parentId":"test.step@213","startTime":110728.325,"class":"Test","method":"step","apiName":"attach \"allureattach_6a9f95f9-ef27-4028-a563-f9e6e432b2ce_capture\"","params":{},"stack":[{"file":"/node_modules/allure-playwright/src/helpers.ts","line":33,"column":7}]}
{"type":"after","callId":"attach@214","endTime":110728.721,"attachments":[{"name":"allureattach_6a9f95f9-ef27-4028-a563-f9e6e432b2ce_capture","contentType":"image/png","sha1":"aa09403551315f4d71fd99d7eb0b7c9380f45526"}]}
{"type":"after","callId":"test.step@213","endTime":110728.794,"attachments":[]}
{"type":"after","callId":"test.step@172","endTime":110728.824,"attachments":[]}
{"type":"before","callId":"test.step@215","parentId":"test.step@7","startTime":110729.226,"class":"Test","method":"step","apiName":"Step30-In Agents page select one agent or all agents using select all funtionality","params":{},"stack":[{"file":"/tests/smoke/test_campaign_record_flow_clone.spec.js","line":780,"column":20}]}
{"type":"stdout","timestamp":110729.393,"text":"======1308-Step30======\n"}
{"type":"before","callId":"pw:api@216","parentId":"test.step@215","startTime":110730.625,"class":"Test","method":"step","apiName":"page.waitForResponse","params":{"info":"Object"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1403,"column":15,"function":"DialerPage.selectAgents"}]}
{"type":"before","callId":"pw:api@217","parentId":"test.step@215","startTime":110731.911,"class":"Test","method":"step","apiName":"locator.fill","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> input[data-co-name=\"Input\"]","value":"Yuxiao Huang","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1404,"column":33,"function":"DialerPage.selectAgents"}]}
{"type":"after","callId":"pw:api@217","endTime":110873.984,"attachments":[]}
{"type":"after","callId":"pw:api@216","endTime":111259.205,"attachments":[]}
{"type":"before","callId":"pw:api@218","parentId":"test.step@215","startTime":111260.091,"class":"Test","method":"step","apiName":"response.json","params":{},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1408,"column":37,"function":"DialerPage.selectAgents"}]}
{"type":"after","callId":"pw:api@218","endTime":111262.771,"attachments":[]}
{"type":"before","callId":"pw:api@219","parentId":"test.step@215","startTime":111264.006,"class":"Test","method":"step","apiName":"page.waitForTimeout","params":{"timeout":"2000"},"stack":[{"file":"/pages/basePage.js","line":356,"column":25,"function":"DialerPage.wait_for_timeout"}]}
{"type":"after","callId":"pw:api@219","endTime":113283.98,"attachments":[]}
{"type":"before","callId":"pw:api@220","parentId":"test.step@215","startTime":113285.802,"class":"Test","method":"step","apiName":"locator.waitFor","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> table >> nth=0","strict":"true","omitReturnValue":"true","timeout":"60000","state":"visible"},"stack":[{"file":"/pages/basePage.js","line":319,"column":38,"function":"DialerPage.wait_for_locator"}]}
{"type":"after","callId":"pw:api@220","endTime":113328.703,"attachments":[]}
{"type":"before","callId":"pw:api@221","parentId":"test.step@215","startTime":113329.702,"class":"Test","method":"step","apiName":"locator.waitFor","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> table >> thead >> nth=0","strict":"true","omitReturnValue":"true","timeout":"60000","state":"visible"},"stack":[{"file":"/pages/basePage.js","line":319,"column":38,"function":"DialerPage.wait_for_locator"}]}
{"type":"after","callId":"pw:api@221","endTime":113350.02,"attachments":[]}
{"type":"before","callId":"pw:api@222","parentId":"test.step@215","startTime":113350.978,"class":"Test","method":"step","apiName":"locator.waitFor","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> table >> //tbody/tr[1] >> //td[1] >> nth=0","strict":"true","omitReturnValue":"true","timeout":"60000","state":"visible"},"stack":[{"file":"/pages/basePage.js","line":319,"column":38,"function":"DialerPage.wait_for_locator"}]}
{"type":"after","callId":"pw:api@222","endTime":113371.931,"attachments":[]}
{"type":"before","callId":"pw:api@223","parentId":"test.step@215","startTime":113372.846,"class":"Test","method":"step","apiName":"locator.click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> table >> //tbody/tr[1] >> //td[1]","strict":"true"},"stack":[{"file":"/pages/basePage.js","line":581,"column":30,"function":"DialerPage.clickTabelCellByIndex"}]}
{"type":"after","callId":"pw:api@223","endTime":113604.319,"attachments":[]}
{"type":"before","callId":"pw:api@224","parentId":"test.step@215","startTime":113605.506,"class":"Test","method":"step","apiName":"page.waitForTimeout","params":{"timeout":"1000"},"stack":[{"file":"/pages/basePage.js","line":356,"column":25,"function":"DialerPage.wait_for_timeout"}]}
{"type":"after","callId":"pw:api@224","endTime":114618.846,"attachments":[]}
{"type":"before","callId":"pw:api@225","parentId":"test.step@215","startTime":114619.856,"class":"Test","method":"step","apiName":"page.waitForResponse","params":{"info":"Object"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1403,"column":15,"function":"DialerPage.selectAgents"}]}
{"type":"before","callId":"pw:api@226","parentId":"test.step@215","startTime":114620.729,"class":"Test","method":"step","apiName":"locator.fill","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> input[data-co-name=\"Input\"]","value":"Damon","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1404,"column":33,"function":"DialerPage.selectAgents"}]}
{"type":"after","callId":"pw:api@226","endTime":114753.345,"attachments":[]}
{"type":"after","callId":"pw:api@225","endTime":115129.391,"attachments":[]}
{"type":"before","callId":"pw:api@227","parentId":"test.step@215","startTime":115130.283,"class":"Test","method":"step","apiName":"response.json","params":{},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1408,"column":37,"function":"DialerPage.selectAgents"}]}
{"type":"after","callId":"pw:api@227","endTime":115134.455,"attachments":[]}
{"type":"before","callId":"pw:api@228","parentId":"test.step@215","startTime":115135.846,"class":"Test","method":"step","apiName":"page.waitForTimeout","params":{"timeout":"2000"},"stack":[{"file":"/pages/basePage.js","line":356,"column":25,"function":"DialerPage.wait_for_timeout"}]}
{"type":"after","callId":"pw:api@228","endTime":117150.75,"attachments":[]}
{"type":"before","callId":"pw:api@229","parentId":"test.step@215","startTime":117151.682,"class":"Test","method":"step","apiName":"locator.waitFor","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> table >> nth=0","strict":"true","omitReturnValue":"true","timeout":"60000","state":"visible"},"stack":[{"file":"/pages/basePage.js","line":319,"column":38,"function":"DialerPage.wait_for_locator"}]}
{"type":"after","callId":"pw:api@229","endTime":117166.357,"attachments":[]}
{"type":"before","callId":"pw:api@230","parentId":"test.step@215","startTime":117167.274,"class":"Test","method":"step","apiName":"locator.waitFor","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> table >> thead >> nth=0","strict":"true","omitReturnValue":"true","timeout":"60000","state":"visible"},"stack":[{"file":"/pages/basePage.js","line":319,"column":38,"function":"DialerPage.wait_for_locator"}]}
{"type":"after","callId":"pw:api@230","endTime":117181.509,"attachments":[]}
{"type":"before","callId":"pw:api@231","parentId":"test.step@215","startTime":117182.417,"class":"Test","method":"step","apiName":"locator.waitFor","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> table >> //tbody/tr[1] >> //td[1] >> nth=0","strict":"true","omitReturnValue":"true","timeout":"60000","state":"visible"},"stack":[{"file":"/pages/basePage.js","line":319,"column":38,"function":"DialerPage.wait_for_locator"}]}
{"type":"after","callId":"pw:api@231","endTime":117197.658,"attachments":[]}
{"type":"before","callId":"pw:api@232","parentId":"test.step@215","startTime":117198.521,"class":"Test","method":"step","apiName":"locator.click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> table >> //tbody/tr[1] >> //td[1]","strict":"true"},"stack":[{"file":"/pages/basePage.js","line":581,"column":30,"function":"DialerPage.clickTabelCellByIndex"}]}
{"type":"after","callId":"pw:api@232","endTime":117372.744,"attachments":[]}
{"type":"before","callId":"pw:api@233","parentId":"test.step@215","startTime":117373.698,"class":"Test","method":"step","apiName":"page.waitForTimeout","params":{"timeout":"1000"},"stack":[{"file":"/pages/basePage.js","line":356,"column":25,"function":"DialerPage.wait_for_timeout"}]}
{"type":"after","callId":"pw:api@233","endTime":118385.549,"attachments":[]}
{"type":"before","callId":"pw:api@234","parentId":"test.step@215","startTime":118386.401,"class":"Test","method":"step","apiName":"page.screenshot","params":{"mask":"undefined","type":"undefined"},"stack":[{"file":"/pages/basePage.js","line":103,"column":60,"function":"DialerPage.saveImage"}]}
{"type":"after","callId":"pw:api@234","endTime":118483.785,"attachments":[]}
{"type":"before","callId":"test.step@235","parentId":"test.step@215","startTime":118484.32,"class":"Test","method":"step","apiName":"allureattach_a8f5511c-a179-48e4-ada5-04a34491095d_capture","params":{},"stack":[{"file":"/node_modules/allure-playwright/src/helpers.ts","line":21,"column":17,"function":"Function.step"}]}
{"type":"before","callId":"attach@236","parentId":"test.step@235","startTime":118484.75,"class":"Test","method":"step","apiName":"attach \"allureattach_a8f5511c-a179-48e4-ada5-04a34491095d_capture\"","params":{},"stack":[{"file":"/node_modules/allure-playwright/src/helpers.ts","line":33,"column":7}]}
{"type":"after","callId":"attach@236","endTime":118485.068,"attachments":[{"name":"allureattach_a8f5511c-a179-48e4-ada5-04a34491095d_capture","contentType":"image/png","sha1":"e6bdd7c80ca45f271f448279fbb0a7fd45fb5c45"}]}
{"type":"after","callId":"test.step@235","endTime":118485.106,"attachments":[]}
{"type":"after","callId":"test.step@215","endTime":118485.123,"attachments":[]}
{"type":"before","callId":"test.step@237","parentId":"test.step@7","startTime":118485.397,"class":"Test","method":"step","apiName":"Step32-Click on the next button","params":{},"stack":[{"file":"/tests/smoke/test_campaign_record_flow_clone.spec.js","line":789,"column":20}]}
{"type":"stdout","timestamp":118485.545,"text":"======1308-Step32======\n"}
{"type":"before","callId":"pw:api@238","parentId":"test.step@237","startTime":118486.395,"class":"Test","method":"step","apiName":"locator.waitFor","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> table >> nth=0","strict":"true","omitReturnValue":"true","timeout":"60000","state":"visible"},"stack":[{"file":"/pages/basePage.js","line":319,"column":38,"function":"DialerPage.wait_for_locator"}]}
{"type":"after","callId":"pw:api@238","endTime":118501.447,"attachments":[]}
{"type":"before","callId":"pw:api@239","parentId":"test.step@237","startTime":118502.34,"class":"Test","method":"step","apiName":"locator.click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> table >> //tbody/tr[1] >> button","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1392,"column":37,"function":"DialerPage.clickAssignmentDetails"}]}
{"type":"after","callId":"pw:api@239","endTime":118695.465,"attachments":[]}
{"type":"before","callId":"pw:api@240","parentId":"test.step@237","startTime":118696.412,"class":"Test","method":"step","apiName":"locator.click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> [data-testid=\"closeButton\"]","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1396,"column":28,"function":"DialerPage.close_assignmentDetailPanel"}]}
{"type":"after","callId":"pw:api@240","endTime":119239.004,"attachments":[]}
{"type":"before","callId":"pw:api@241","parentId":"test.step@237","startTime":119240.05,"class":"Test","method":"step","apiName":"locator.count","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> table >> tbody>tr"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":955,"column":50,"function":"DialerPage.getListSize"}]}
{"type":"after","callId":"pw:api@241","endTime":119244.105,"attachments":[]}
{"type":"before","callId":"expect@242","parentId":"test.step@237","startTime":119244.95,"class":"Test","method":"step","apiName":"expect.toEqual","params":{"expected":"1"},"stack":[{"file":"/tests/smoke/test_campaign_record_flow_clone.spec.js","line":797,"column":36}]}
{"type":"after","callId":"expect@242","endTime":119245.09,"attachments":[]}
{"type":"before","callId":"pw:api@243","parentId":"test.step@237","startTime":119245.855,"class":"Test","method":"step","apiName":"locator.click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> [data-testid=\"nextButton\"]","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":963,"column":22,"function":"DialerPage.saveAndNext"}]}
{"type":"after","callId":"pw:api@243","endTime":119803.403,"attachments":[]}
{"type":"before","callId":"expect@244","parentId":"test.step@237","startTime":119804.156,"class":"Test","method":"step","apiName":"expect.toBeVisible","params":{},"stack":[{"file":"/tests/smoke/test_campaign_record_flow_clone.spec.js","line":799,"column":48}]}
{"type":"after","callId":"expect@244","endTime":119819.342,"attachments":[]}
{"type":"before","callId":"pw:api@245","parentId":"test.step@237","startTime":119820.166,"class":"Test","method":"step","apiName":"page.screenshot","params":{"mask":"undefined","type":"undefined"},"stack":[{"file":"/pages/basePage.js","line":103,"column":60,"function":"DialerPage.saveImage"}]}
{"type":"after","callId":"pw:api@245","endTime":119920.222,"attachments":[]}
{"type":"before","callId":"test.step@246","parentId":"test.step@237","startTime":119920.682,"class":"Test","method":"step","apiName":"allureattach_8c28547e-8171-4ff1-ab13-86937b45accd_capture","params":{},"stack":[{"file":"/node_modules/allure-playwright/src/helpers.ts","line":21,"column":17,"function":"Function.step"}]}
{"type":"before","callId":"attach@247","parentId":"test.step@246","startTime":119921.097,"class":"Test","method":"step","apiName":"attach \"allureattach_8c28547e-8171-4ff1-ab13-86937b45accd_capture\"","params":{},"stack":[{"file":"/node_modules/allure-playwright/src/helpers.ts","line":33,"column":7}]}
{"type":"after","callId":"attach@247","endTime":119921.288,"attachments":[{"name":"allureattach_8c28547e-8171-4ff1-ab13-86937b45accd_capture","contentType":"image/png","sha1":"366ccf0ccc7416874cfd18c8d0b4dc737034b50e"}]}
{"type":"after","callId":"test.step@246","endTime":119921.314,"attachments":[]}
{"type":"after","callId":"test.step@237","endTime":119921.33,"attachments":[]}
{"type":"before","callId":"test.step@248","parentId":"test.step@7","startTime":119921.601,"class":"Test","method":"step","apiName":"Step36-Select 2 record lists to associated to the campaign and click on the apply button","params":{},"stack":[{"file":"/tests/smoke/test_campaign_record_flow_clone.spec.js","line":804,"column":20}]}
{"type":"stdout","timestamp":119921.742,"text":"======1308-Step36======\n"}
{"type":"before","callId":"pw:api@249","parentId":"test.step@248","startTime":119922.83,"class":"Test","method":"step","apiName":"page.waitForResponse","params":{"info":"Object"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1486,"column":14,"function":"DialerPage.addRecordLists"}]}
{"type":"before","callId":"pw:api@250","parentId":"test.step@248","startTime":119923.802,"class":"Test","method":"step","apiName":"locator.click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-section__content:has(h5:text(\"Record lists\")) button>span:text(\"Add record lists\")","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1753,"column":35,"function":"DialerPage.clickAddRecordsListButton"}]}
{"type":"after","callId":"pw:api@249","endTime":120060.721,"attachments":[]}
{"type":"after","callId":"pw:api@250","endTime":120134.008,"attachments":[]}
{"type":"before","callId":"pw:api@251","parentId":"test.step@248","startTime":120134.975,"class":"Test","method":"step","apiName":"response.json","params":{},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1489,"column":32,"function":"DialerPage.addRecordLists"}]}
{"type":"after","callId":"pw:api@251","endTime":120135.481,"attachments":[]}
{"type":"before","callId":"pw:api@252","parentId":"test.step@248","startTime":120136.364,"class":"Test","method":"step","apiName":"locator.waitFor","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> nth=0","strict":"true","omitReturnValue":"true","timeout":"60000","state":"visible"},"stack":[{"file":"/pages/basePage.js","line":319,"column":38,"function":"DialerPage.wait_for_locator"}]}
{"type":"after","callId":"pw:api@252","endTime":120158.831,"attachments":[]}
{"type":"before","callId":"pw:api@253","parentId":"test.step@248","startTime":120160.245,"class":"Test","method":"step","apiName":"locator.waitFor","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> nth=0","strict":"true","omitReturnValue":"true","timeout":"60000","state":"visible"},"stack":[{"file":"/pages/basePage.js","line":319,"column":38,"function":"DialerPage.wait_for_locator"}]}
{"type":"after","callId":"pw:api@253","endTime":120178.168,"attachments":[]}
{"type":"before","callId":"pw:api@254","parentId":"test.step@248","startTime":120179.124,"class":"Test","method":"step","apiName":"locator.waitFor","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> //tbody/tr[1]/td[1] >> div.co-placeholder--animated","strict":"true","omitReturnValue":"true","timeout":"10000","state":"hidden"},"stack":[{"file":"/pages/basePage.js","line":395,"column":40,"function":"DialerPage.wait_for_animated"}]}
{"type":"after","callId":"pw:api@254","endTime":120428.056,"attachments":[]}
{"type":"before","callId":"pw:api@255","parentId":"test.step@248","startTime":120429.173,"class":"Test","method":"step","apiName":"page.waitForTimeout","params":{"timeout":"1000"},"stack":[{"file":"/pages/basePage.js","line":356,"column":25,"function":"DialerPage.wait_for_timeout"}]}
{"type":"after","callId":"pw:api@255","endTime":121443.969,"attachments":[]}
{"type":"before","callId":"expect@256","parentId":"test.step@248","startTime":121444.757,"class":"Test","method":"step","apiName":"expect.toBeVisible","params":{},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1495,"column":35,"function":"DialerPage.addRecordLists"}]}
{"type":"after","callId":"expect@256","endTime":121460.494,"attachments":[]}
{"type":"before","callId":"pw:api@257","parentId":"test.step@248","startTime":121461.522,"class":"Test","method":"step","apiName":"locator.waitFor","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> nth=0","strict":"true","omitReturnValue":"true","timeout":"60000","state":"visible"},"stack":[{"file":"/pages/basePage.js","line":319,"column":38,"function":"DialerPage.wait_for_locator"}]}
{"type":"after","callId":"pw:api@257","endTime":121476.787,"attachments":[]}
{"type":"before","callId":"pw:api@258","parentId":"test.step@248","startTime":121477.684,"class":"Test","method":"step","apiName":"locator.waitFor","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> thead >> nth=0","strict":"true","omitReturnValue":"true","timeout":"60000","state":"visible"},"stack":[{"file":"/pages/basePage.js","line":319,"column":38,"function":"DialerPage.wait_for_locator"}]}
{"type":"after","callId":"pw:api@258","endTime":121492.326,"attachments":[]}
{"type":"before","callId":"pw:api@259","parentId":"test.step@248","startTime":121493.244,"class":"Test","method":"step","apiName":"locator.waitFor","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> //tbody/tr[1] >> //td[1] >> input >> nth=0","strict":"true","omitReturnValue":"true","timeout":"60000","state":"visible"},"stack":[{"file":"/pages/basePage.js","line":319,"column":38,"function":"DialerPage.wait_for_locator"}]}
{"type":"after","callId":"pw:api@259","endTime":121509.216,"attachments":[]}
{"type":"before","callId":"pw:api@260","parentId":"test.step@248","startTime":121510.089,"class":"Test","method":"step","apiName":"locator.click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> //tbody/tr[1] >> //td[1] >> input","strict":"true"},"stack":[{"file":"/pages/basePage.js","line":577,"column":30,"function":"DialerPage.clickTabelCellByIndex"}]}
{"type":"after","callId":"pw:api@260","endTime":121657.037,"attachments":[]}
{"type":"before","callId":"pw:api@261","parentId":"test.step@248","startTime":121657.982,"class":"Test","method":"step","apiName":"locator.waitFor","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> nth=0","strict":"true","omitReturnValue":"true","timeout":"60000","state":"visible"},"stack":[{"file":"/pages/basePage.js","line":319,"column":38,"function":"DialerPage.wait_for_locator"}]}
{"type":"after","callId":"pw:api@261","endTime":121674.182,"attachments":[]}
{"type":"before","callId":"pw:api@262","parentId":"test.step@248","startTime":121675.196,"class":"Test","method":"step","apiName":"locator.waitFor","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> thead >> nth=0","strict":"true","omitReturnValue":"true","timeout":"60000","state":"visible"},"stack":[{"file":"/pages/basePage.js","line":319,"column":38,"function":"DialerPage.wait_for_locator"}]}
{"type":"after","callId":"pw:api@262","endTime":121693.532,"attachments":[]}
{"type":"before","callId":"pw:api@263","parentId":"test.step@248","startTime":121694.496,"class":"Test","method":"step","apiName":"locator.textContent","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> //tbody/tr[1] >> //td[2]","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1789,"column":23,"function":"DialerPage.clickRecordsListByIndex"}]}
{"type":"after","callId":"pw:api@263","endTime":121713.829,"attachments":[]}
{"type":"before","callId":"pw:api@264","parentId":"test.step@248","startTime":121715.506,"class":"Test","method":"step","apiName":"locator.waitFor","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> nth=0","strict":"true","omitReturnValue":"true","timeout":"60000","state":"visible"},"stack":[{"file":"/pages/basePage.js","line":319,"column":38,"function":"DialerPage.wait_for_locator"}]}
{"type":"after","callId":"pw:api@264","endTime":121734.101,"attachments":[]}
{"type":"before","callId":"pw:api@265","parentId":"test.step@248","startTime":121735.158,"class":"Test","method":"step","apiName":"locator.waitFor","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> thead >> nth=0","strict":"true","omitReturnValue":"true","timeout":"60000","state":"visible"},"stack":[{"file":"/pages/basePage.js","line":319,"column":38,"function":"DialerPage.wait_for_locator"}]}
{"type":"after","callId":"pw:api@265","endTime":121753.862,"attachments":[]}
{"type":"before","callId":"pw:api@266","parentId":"test.step@248","startTime":121754.898,"class":"Test","method":"step","apiName":"locator.waitFor","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> //tbody/tr[2] >> //td[1] >> input >> nth=0","strict":"true","omitReturnValue":"true","timeout":"60000","state":"visible"},"stack":[{"file":"/pages/basePage.js","line":319,"column":38,"function":"DialerPage.wait_for_locator"}]}
{"type":"after","callId":"pw:api@266","endTime":121772.927,"attachments":[]}
{"type":"before","callId":"pw:api@267","parentId":"test.step@248","startTime":121774.299,"class":"Test","method":"step","apiName":"locator.click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> //tbody/tr[2] >> //td[1] >> input","strict":"true"},"stack":[{"file":"/pages/basePage.js","line":577,"column":30,"function":"DialerPage.clickTabelCellByIndex"}]}
{"type":"after","callId":"pw:api@267","endTime":121945.07,"attachments":[]}
{"type":"before","callId":"pw:api@268","parentId":"test.step@248","startTime":121946.053,"class":"Test","method":"step","apiName":"locator.waitFor","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> nth=0","strict":"true","omitReturnValue":"true","timeout":"60000","state":"visible"},"stack":[{"file":"/pages/basePage.js","line":319,"column":38,"function":"DialerPage.wait_for_locator"}]}
{"type":"after","callId":"pw:api@268","endTime":121964.625,"attachments":[]}
{"type":"before","callId":"pw:api@269","parentId":"test.step@248","startTime":121965.562,"class":"Test","method":"step","apiName":"locator.waitFor","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> thead >> nth=0","strict":"true","omitReturnValue":"true","timeout":"60000","state":"visible"},"stack":[{"file":"/pages/basePage.js","line":319,"column":38,"function":"DialerPage.wait_for_locator"}]}
{"type":"after","callId":"pw:api@269","endTime":121982.886,"attachments":[]}
{"type":"before","callId":"pw:api@270","parentId":"test.step@248","startTime":121983.844,"class":"Test","method":"step","apiName":"locator.textContent","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> //tbody/tr[2] >> //td[2]","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1789,"column":23,"function":"DialerPage.clickRecordsListByIndex"}]}
{"type":"after","callId":"pw:api@270","endTime":122002.031,"attachments":[]}
{"type":"before","callId":"pw:api@271","parentId":"test.step@248","startTime":122003.873,"class":"Test","method":"step","apiName":"locator.waitFor","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> nth=0","strict":"true","omitReturnValue":"true","timeout":"60000","state":"visible"},"stack":[{"file":"/pages/basePage.js","line":319,"column":38,"function":"DialerPage.wait_for_locator"}]}
{"type":"after","callId":"pw:api@271","endTime":122025.353,"attachments":[]}
{"type":"before","callId":"pw:api@272","parentId":"test.step@248","startTime":122027.571,"class":"Test","method":"step","apiName":"locator.waitFor","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> thead >> nth=0","strict":"true","omitReturnValue":"true","timeout":"60000","state":"visible"},"stack":[{"file":"/pages/basePage.js","line":319,"column":38,"function":"DialerPage.wait_for_locator"}]}
{"type":"after","callId":"pw:api@272","endTime":122049.903,"attachments":[]}
{"type":"before","callId":"pw:api@273","parentId":"test.step@248","startTime":122051.662,"class":"Test","method":"step","apiName":"locator.waitFor","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> //tbody/tr[3] >> //td[1] >> input >> nth=0","strict":"true","omitReturnValue":"true","timeout":"60000","state":"visible"},"stack":[{"file":"/pages/basePage.js","line":319,"column":38,"function":"DialerPage.wait_for_locator"}]}
{"type":"after","callId":"pw:api@273","endTime":122069.694,"attachments":[]}
{"type":"before","callId":"pw:api@274","parentId":"test.step@248","startTime":122070.714,"class":"Test","method":"step","apiName":"locator.click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> //tbody/tr[3] >> //td[1] >> input","strict":"true"},"stack":[{"file":"/pages/basePage.js","line":577,"column":30,"function":"DialerPage.clickTabelCellByIndex"}]}
{"type":"after","callId":"pw:api@274","endTime":122241.653,"attachments":[]}
{"type":"before","callId":"pw:api@275","parentId":"test.step@248","startTime":122242.625,"class":"Test","method":"step","apiName":"locator.waitFor","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> nth=0","strict":"true","omitReturnValue":"true","timeout":"60000","state":"visible"},"stack":[{"file":"/pages/basePage.js","line":319,"column":38,"function":"DialerPage.wait_for_locator"}]}
{"type":"after","callId":"pw:api@275","endTime":122259.472,"attachments":[]}
{"type":"before","callId":"pw:api@276","parentId":"test.step@248","startTime":122260.413,"class":"Test","method":"step","apiName":"locator.waitFor","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> thead >> nth=0","strict":"true","omitReturnValue":"true","timeout":"60000","state":"visible"},"stack":[{"file":"/pages/basePage.js","line":319,"column":38,"function":"DialerPage.wait_for_locator"}]}
{"type":"after","callId":"pw:api@276","endTime":122275.996,"attachments":[]}
{"type":"before","callId":"pw:api@277","parentId":"test.step@248","startTime":122276.874,"class":"Test","method":"step","apiName":"locator.textContent","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> //tbody/tr[3] >> //td[2]","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1789,"column":23,"function":"DialerPage.clickRecordsListByIndex"}]}
{"type":"after","callId":"pw:api@277","endTime":122292.128,"attachments":[]}
{"type":"before","callId":"pw:api@278","parentId":"test.step@248","startTime":122293.007,"class":"Test","method":"step","apiName":"locator.isVisible","params":{"selector":"div[data-testid=\"toaster\"] div[data-co-name=\"Message\"]:nth-child(1) div[data-testid]","strict":"true"},"stack":[{"file":"/pages/basePage.js","line":522,"column":37,"function":"DialerPage.waitAndClearToast"}]}
{"type":"after","callId":"pw:api@278","endTime":122306.917,"attachments":[]}
{"type":"before","callId":"pw:api@279","parentId":"test.step@248","startTime":122307.892,"class":"Test","method":"step","apiName":"locator.isVisible","params":{"selector":"div[data-testid=\"toaster\"] div[data-co-name=\"Message\"]:nth-child(1) div[data-testid]","strict":"true"},"stack":[{"file":"/pages/basePage.js","line":522,"column":37,"function":"DialerPage.waitAndClearToast"}]}
{"type":"after","callId":"pw:api@279","endTime":122321.142,"attachments":[]}
{"type":"before","callId":"pw:api@280","parentId":"test.step@248","startTime":122322.013,"class":"Test","method":"step","apiName":"locator.click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> button>*:text(\"Apply\")","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1828,"column":37,"function":"DialerPage.clickRecordsListApplyButton"}]}
{"type":"after","callId":"pw:api@280","endTime":122503.201,"attachments":[]}
{"type":"before","callId":"pw:api@281","parentId":"test.step@248","startTime":122504.285,"class":"Test","method":"step","apiName":"locator.count","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-section__content:has(h5:text(\"Record lists\")) table >> tbody>tr"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":955,"column":50,"function":"DialerPage.getListSize"}]}
{"type":"after","callId":"pw:api@281","endTime":122508.686,"attachments":[]}
{"type":"before","callId":"pw:api@282","parentId":"test.step@248","startTime":122509.492,"class":"Test","method":"step","apiName":"locator.waitFor","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-section__content:has(h5:text(\"Record lists\")) table >> nth=0","strict":"true","omitReturnValue":"true","timeout":"60000","state":"visible"},"stack":[{"file":"/pages/basePage.js","line":319,"column":38,"function":"DialerPage.wait_for_locator"}]}
{"type":"after","callId":"pw:api@282","endTime":122524.831,"attachments":[]}
{"type":"before","callId":"pw:api@283","parentId":"test.step@248","startTime":122525.736,"class":"Test","method":"step","apiName":"locator.waitFor","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-section__content:has(h5:text(\"Record lists\")) table >> thead >> nth=0","strict":"true","omitReturnValue":"true","timeout":"60000","state":"visible"},"stack":[{"file":"/pages/basePage.js","line":319,"column":38,"function":"DialerPage.wait_for_locator"}]}
{"type":"after","callId":"pw:api@283","endTime":122539.505,"attachments":[]}
{"type":"before","callId":"pw:api@284","parentId":"test.step@248","startTime":122540.339,"class":"Test","method":"step","apiName":"locator.textContent","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-section__content:has(h5:text(\"Record lists\")) table >> //tbody/tr[1] >> //td[1]","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1718,"column":42,"function":"DialerPage.checkSelectedRecordList"}]}
{"type":"after","callId":"pw:api@284","endTime":122553.083,"attachments":[]}
{"type":"before","callId":"expect@285","parentId":"test.step@248","startTime":122554.103,"class":"Test","method":"step","apiName":"expect.toContain","params":{"expected":"metoto_search_list_bwva"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1719,"column":35,"function":"DialerPage.checkSelectedRecordList"}]}
{"type":"after","callId":"expect@285","endTime":122554.198,"attachments":[]}
{"type":"before","callId":"pw:api@286","parentId":"test.step@248","startTime":122555.039,"class":"Test","method":"step","apiName":"locator.waitFor","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-section__content:has(h5:text(\"Record lists\")) table >> nth=0","strict":"true","omitReturnValue":"true","timeout":"60000","state":"visible"},"stack":[{"file":"/pages/basePage.js","line":319,"column":38,"function":"DialerPage.wait_for_locator"}]}
{"type":"after","callId":"pw:api@286","endTime":122568.413,"attachments":[]}
{"type":"before","callId":"pw:api@287","parentId":"test.step@248","startTime":122569.403,"class":"Test","method":"step","apiName":"locator.waitFor","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-section__content:has(h5:text(\"Record lists\")) table >> thead >> nth=0","strict":"true","omitReturnValue":"true","timeout":"60000","state":"visible"},"stack":[{"file":"/pages/basePage.js","line":319,"column":38,"function":"DialerPage.wait_for_locator"}]}
{"type":"after","callId":"pw:api@287","endTime":122584.039,"attachments":[]}
{"type":"before","callId":"pw:api@288","parentId":"test.step@248","startTime":122584.898,"class":"Test","method":"step","apiName":"locator.textContent","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-section__content:has(h5:text(\"Record lists\")) table >> //tbody/tr[2] >> //td[1]","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1718,"column":42,"function":"DialerPage.checkSelectedRecordList"}]}
{"type":"after","callId":"pw:api@288","endTime":122600.338,"attachments":[]}
{"type":"before","callId":"expect@289","parentId":"test.step@248","startTime":122601.101,"class":"Test","method":"step","apiName":"expect.toContain","params":{"expected":"metoto_records_list_r08p"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1719,"column":35,"function":"DialerPage.checkSelectedRecordList"}]}
{"type":"after","callId":"expect@289","endTime":122601.182,"attachments":[]}
{"type":"before","callId":"pw:api@290","parentId":"test.step@248","startTime":122601.965,"class":"Test","method":"step","apiName":"locator.waitFor","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-section__content:has(h5:text(\"Record lists\")) table >> nth=0","strict":"true","omitReturnValue":"true","timeout":"60000","state":"visible"},"stack":[{"file":"/pages/basePage.js","line":319,"column":38,"function":"DialerPage.wait_for_locator"}]}
{"type":"after","callId":"pw:api@290","endTime":122615.993,"attachments":[]}
{"type":"before","callId":"pw:api@291","parentId":"test.step@248","startTime":122616.874,"class":"Test","method":"step","apiName":"locator.waitFor","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-section__content:has(h5:text(\"Record lists\")) table >> thead >> nth=0","strict":"true","omitReturnValue":"true","timeout":"60000","state":"visible"},"stack":[{"file":"/pages/basePage.js","line":319,"column":38,"function":"DialerPage.wait_for_locator"}]}
{"type":"after","callId":"pw:api@291","endTime":122632.555,"attachments":[]}
{"type":"before","callId":"pw:api@292","parentId":"test.step@248","startTime":122633.418,"class":"Test","method":"step","apiName":"locator.textContent","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-section__content:has(h5:text(\"Record lists\")) table >> //tbody/tr[3] >> //td[1]","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1718,"column":42,"function":"DialerPage.checkSelectedRecordList"}]}
{"type":"after","callId":"pw:api@292","endTime":122649.108,"attachments":[]}
{"type":"before","callId":"expect@293","parentId":"test.step@248","startTime":122649.854,"class":"Test","method":"step","apiName":"expect.toContain","params":{"expected":"metoto_search_list_2vw1"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1719,"column":35,"function":"DialerPage.checkSelectedRecordList"}]}
{"type":"after","callId":"expect@293","endTime":122649.924,"attachments":[]}
{"type":"before","callId":"pw:api@294","parentId":"test.step@248","startTime":122650.729,"class":"Test","method":"step","apiName":"page.screenshot","params":{"mask":"undefined","type":"undefined"},"stack":[{"file":"/pages/basePage.js","line":103,"column":60,"function":"DialerPage.saveImage"}]}
{"type":"after","callId":"pw:api@294","endTime":122753.291,"attachments":[]}
{"type":"before","callId":"test.step@295","parentId":"test.step@248","startTime":122753.755,"class":"Test","method":"step","apiName":"allureattach_7ecd37db-3856-4352-a597-95cd1bec9b9b_capture","params":{},"stack":[{"file":"/node_modules/allure-playwright/src/helpers.ts","line":21,"column":17,"function":"Function.step"}]}
{"type":"before","callId":"attach@296","parentId":"test.step@295","startTime":122754.17,"class":"Test","method":"step","apiName":"attach \"allureattach_7ecd37db-3856-4352-a597-95cd1bec9b9b_capture\"","params":{},"stack":[{"file":"/node_modules/allure-playwright/src/helpers.ts","line":33,"column":7}]}
{"type":"after","callId":"attach@296","endTime":122754.555,"attachments":[{"name":"allureattach_7ecd37db-3856-4352-a597-95cd1bec9b9b_capture","contentType":"image/png","sha1":"7b5c4db51ded6a8fd227a1b1829021ddf46a22b7"}]}
{"type":"after","callId":"test.step@295","endTime":122754.62,"attachments":[]}
{"type":"after","callId":"test.step@248","endTime":122754.642,"attachments":[]}
{"type":"before","callId":"test.step@297","parentId":"test.step@7","startTime":122754.917,"class":"Test","method":"step","apiName":"Step37-Remove one of the RL clicking on the bin button","params":{},"stack":[{"file":"/tests/smoke/test_campaign_record_flow_clone.spec.js","line":815,"column":20}]}
{"type":"stdout","timestamp":122755.113,"text":"======1308-Step37======\n"}
{"type":"before","callId":"pw:api@298","parentId":"test.step@297","startTime":122756.106,"class":"Test","method":"step","apiName":"locator.waitFor","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-section__content:has(h5:text(\"Record lists\")) table >> nth=0","strict":"true","omitReturnValue":"true","timeout":"60000","state":"visible"},"stack":[{"file":"/pages/basePage.js","line":319,"column":38,"function":"DialerPage.wait_for_locator"}]}
{"type":"after","callId":"pw:api@298","endTime":122771.915,"attachments":[]}
{"type":"before","callId":"pw:api@299","parentId":"test.step@297","startTime":122772.861,"class":"Test","method":"step","apiName":"locator.waitFor","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-section__content:has(h5:text(\"Record lists\")) table >> thead >> nth=0","strict":"true","omitReturnValue":"true","timeout":"60000","state":"visible"},"stack":[{"file":"/pages/basePage.js","line":319,"column":38,"function":"DialerPage.wait_for_locator"}]}
{"type":"after","callId":"pw:api@299","endTime":122789.676,"attachments":[]}
{"type":"before","callId":"pw:api@300","parentId":"test.step@297","startTime":122791.113,"class":"Test","method":"step","apiName":"locator.textContent","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-section__content:has(h5:text(\"Record lists\")) table >> //tbody/tr[3] >> //td[1]","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1731,"column":44,"function":"DialerPage.removeSelectedRecordListByIndex"}]}
{"type":"after","callId":"pw:api@300","endTime":122810.257,"attachments":[]}
{"type":"before","callId":"pw:api@301","parentId":"test.step@297","startTime":122811.146,"class":"Test","method":"step","apiName":"locator.click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-section__content:has(h5:text(\"Record lists\")) table >> //tbody/tr[3] >> td>button[aria-label=\"trash\"]","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1733,"column":20,"function":"DialerPage.removeSelectedRecordListByIndex"}]}
{"type":"after","callId":"pw:api@301","endTime":123096.967,"attachments":[]}
{"type":"before","callId":"pw:api@302","parentId":"test.step@297","startTime":123097.89,"class":"Test","method":"step","apiName":"locator.click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-modal__dialog >> [data-pendo-campaignform-lists-removerlistbutton-confirm]","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1734,"column":46,"function":"DialerPage.removeSelectedRecordListByIndex"}]}
{"type":"after","callId":"pw:api@302","endTime":124157.321,"attachments":[]}
{"type":"before","callId":"pw:api@303","parentId":"test.step@297","startTime":124158.246,"class":"Test","method":"step","apiName":"locator.waitFor","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-section__content:has(h5:text(\"Record lists\")) table >> nth=0","strict":"true","omitReturnValue":"true","timeout":"60000","state":"visible"},"stack":[{"file":"/pages/basePage.js","line":319,"column":38,"function":"DialerPage.wait_for_locator"}]}
{"type":"after","callId":"pw:api@303","endTime":124173.04,"attachments":[]}
{"type":"before","callId":"pw:api@304","parentId":"test.step@297","startTime":124173.932,"class":"Test","method":"step","apiName":"locator.waitFor","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-section__content:has(h5:text(\"Record lists\")) table >> thead >> nth=0","strict":"true","omitReturnValue":"true","timeout":"60000","state":"visible"},"stack":[{"file":"/pages/basePage.js","line":319,"column":38,"function":"DialerPage.wait_for_locator"}]}
{"type":"after","callId":"pw:api@304","endTime":124188.326,"attachments":[]}
{"type":"before","callId":"pw:api@305","parentId":"test.step@297","startTime":124189.138,"class":"Test","method":"step","apiName":"locator.textContent","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-section__content:has(h5:text(\"Record lists\")) table >> //tbody/tr[2] >> //td[1]","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1731,"column":44,"function":"DialerPage.removeSelectedRecordListByIndex"}]}
{"type":"after","callId":"pw:api@305","endTime":124202.383,"attachments":[]}
{"type":"before","callId":"pw:api@306","parentId":"test.step@297","startTime":124203.229,"class":"Test","method":"step","apiName":"locator.click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-section__content:has(h5:text(\"Record lists\")) table >> //tbody/tr[2] >> td>button[aria-label=\"trash\"]","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1733,"column":20,"function":"DialerPage.removeSelectedRecordListByIndex"}]}
{"type":"after","callId":"pw:api@306","endTime":124359.013,"attachments":[]}
{"type":"before","callId":"pw:api@307","parentId":"test.step@297","startTime":124359.87,"class":"Test","method":"step","apiName":"locator.click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-modal__dialog >> [data-pendo-campaignform-lists-removerlistbutton-confirm]","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1734,"column":46,"function":"DialerPage.removeSelectedRecordListByIndex"}]}
{"type":"after","callId":"pw:api@307","endTime":124906.863,"attachments":[]}
{"type":"before","callId":"pw:api@308","parentId":"test.step@297","startTime":124907.801,"class":"Test","method":"step","apiName":"locator.waitFor","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-section__content:has(h5:text(\"Record lists\")) table >> nth=0","strict":"true","omitReturnValue":"true","timeout":"60000","state":"visible"},"stack":[{"file":"/pages/basePage.js","line":319,"column":38,"function":"DialerPage.wait_for_locator"}]}
{"type":"after","callId":"pw:api@308","endTime":124922.442,"attachments":[]}
{"type":"before","callId":"pw:api@309","parentId":"test.step@297","startTime":124923.718,"class":"Test","method":"step","apiName":"locator.waitFor","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-section__content:has(h5:text(\"Record lists\")) table >> thead >> nth=0","strict":"true","omitReturnValue":"true","timeout":"60000","state":"visible"},"stack":[{"file":"/pages/basePage.js","line":319,"column":38,"function":"DialerPage.wait_for_locator"}]}
{"type":"after","callId":"pw:api@309","endTime":124936.716,"attachments":[]}
{"type":"before","callId":"pw:api@310","parentId":"test.step@297","startTime":124937.531,"class":"Test","method":"step","apiName":"locator.textContent","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-section__content:has(h5:text(\"Record lists\")) table >> //tbody/tr[1] >> //td[1]","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1731,"column":44,"function":"DialerPage.removeSelectedRecordListByIndex"}]}
{"type":"after","callId":"pw:api@310","endTime":124950.288,"attachments":[]}
{"type":"before","callId":"pw:api@311","parentId":"test.step@297","startTime":124951.113,"class":"Test","method":"step","apiName":"locator.click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-section__content:has(h5:text(\"Record lists\")) table >> //tbody/tr[1] >> td>button[aria-label=\"trash\"]","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1733,"column":20,"function":"DialerPage.removeSelectedRecordListByIndex"}]}
{"type":"after","callId":"pw:api@311","endTime":125109.645,"attachments":[]}
{"type":"before","callId":"pw:api@312","parentId":"test.step@297","startTime":125110.545,"class":"Test","method":"step","apiName":"locator.click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-modal__dialog >> [data-pendo-campaignform-lists-removerlistbutton-confirm]","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1734,"column":46,"function":"DialerPage.removeSelectedRecordListByIndex"}]}
{"type":"after","callId":"pw:api@312","endTime":126155.674,"attachments":[]}
{"type":"before","callId":"expect@313","parentId":"test.step@297","startTime":126156.481,"class":"Test","method":"step","apiName":"expect.toBeHidden","params":{},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1745,"column":47,"function":"DialerPage.removeSelectedRecordListByIndex"}]}
{"type":"after","callId":"expect@313","endTime":126170.214,"attachments":[]}
{"type":"stdout","timestamp":126170.424,"text":"the record_list_name now for po_automation on chromium browser is metoto_records_list_b6lb\n"}
{"type":"before","callId":"pw:api@314","parentId":"test.step@297","startTime":126171.375,"class":"Test","method":"step","apiName":"page.waitForResponse","params":{"info":"Object"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1486,"column":14,"function":"DialerPage.addRecordLists"}]}
{"type":"before","callId":"pw:api@315","parentId":"test.step@297","startTime":126172.264,"class":"Test","method":"step","apiName":"locator.click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div.co-section__content:has(h5:text(\"Record lists\")) button>span:text(\"Add record lists\")","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1753,"column":35,"function":"DialerPage.clickAddRecordsListButton"}]}
{"type":"after","callId":"pw:api@315","endTime":126336.128,"attachments":[]}
{"type":"after","callId":"pw:api@314","endTime":126588.173,"attachments":[]}
{"type":"before","callId":"pw:api@316","parentId":"test.step@297","startTime":126589.077,"class":"Test","method":"step","apiName":"response.json","params":{},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1489,"column":32,"function":"DialerPage.addRecordLists"}]}
{"type":"after","callId":"pw:api@316","endTime":126592.231,"attachments":[]}
{"type":"before","callId":"pw:api@317","parentId":"test.step@297","startTime":126593.111,"class":"Test","method":"step","apiName":"locator.waitFor","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> nth=0","strict":"true","omitReturnValue":"true","timeout":"60000","state":"visible"},"stack":[{"file":"/pages/basePage.js","line":319,"column":38,"function":"DialerPage.wait_for_locator"}]}
{"type":"after","callId":"pw:api@317","endTime":126627.046,"attachments":[]}
{"type":"before","callId":"pw:api@318","parentId":"test.step@297","startTime":126628.167,"class":"Test","method":"step","apiName":"locator.waitFor","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> nth=0","strict":"true","omitReturnValue":"true","timeout":"60000","state":"visible"},"stack":[{"file":"/pages/basePage.js","line":319,"column":38,"function":"DialerPage.wait_for_locator"}]}
{"type":"after","callId":"pw:api@318","endTime":126653.235,"attachments":[]}
{"type":"before","callId":"pw:api@319","parentId":"test.step@297","startTime":126654.143,"class":"Test","method":"step","apiName":"locator.waitFor","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> //tbody/tr[1]/td[1] >> div.co-placeholder--animated","strict":"true","omitReturnValue":"true","timeout":"10000","state":"hidden"},"stack":[{"file":"/pages/basePage.js","line":395,"column":40,"function":"DialerPage.wait_for_animated"}]}
{"type":"after","callId":"pw:api@319","endTime":126670.427,"attachments":[]}
{"type":"before","callId":"pw:api@320","parentId":"test.step@297","startTime":126671.577,"class":"Test","method":"step","apiName":"page.waitForTimeout","params":{"timeout":"1000"},"stack":[{"file":"/pages/basePage.js","line":356,"column":25,"function":"DialerPage.wait_for_timeout"}]}
{"type":"after","callId":"pw:api@320","endTime":127690.07,"attachments":[]}
{"type":"before","callId":"expect@321","parentId":"test.step@297","startTime":127691.009,"class":"Test","method":"step","apiName":"expect.toBeVisible","params":{},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1495,"column":35,"function":"DialerPage.addRecordLists"}]}
{"type":"after","callId":"expect@321","endTime":127707.344,"attachments":[]}
{"type":"before","callId":"pw:api@322","parentId":"test.step@297","startTime":127708.333,"class":"Test","method":"step","apiName":"page.waitForResponse","params":{"info":"Object"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1772,"column":15,"function":"DialerPage.clickRecordsListByIndex"}]}
{"type":"before","callId":"pw:api@323","parentId":"test.step@297","startTime":127709.206,"class":"Test","method":"step","apiName":"locator.fill","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> input[placeholder=\"Search by list name\"]","value":"metoto_records_list_b6lb","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1773,"column":39,"function":"DialerPage.clickRecordsListByIndex"}]}
{"type":"after","callId":"pw:api@323","endTime":127844.028,"attachments":[]}
{"type":"after","callId":"pw:api@322","endTime":128383.5,"attachments":[]}
{"type":"before","callId":"pw:api@324","parentId":"test.step@297","startTime":128384.525,"class":"Test","method":"step","apiName":"response.json","params":{},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":1775,"column":37,"function":"DialerPage.clickRecordsListByIndex"}]}
{"type":"after","callId":"pw:api@324","endTime":128387.19,"attachments":[]}
{"type":"before","callId":"pw:api@325","parentId":"test.step@297","startTime":128388.125,"class":"Test","method":"step","apiName":"locator.waitFor","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div#panels-layout-content-id >> table >> nth=0","strict":"true","omitReturnValue":"true","timeout":"60000","state":"visible"},"stack":[{"file":"/pages/basePage.js","line":319,"column":38,"function":"DialerPage.wait_for_locator"}]}
{"type":"after","callId":"pw:api@325","endTime":188405.851,"attachments":[],"error":{"name":"","message":"TimeoutError: locator.waitFor: Timeout 60000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div#panels-layout-content-id').locator('table').first() to be visible\u001b[22m\n","stack":"TimeoutError: locator.waitFor: Timeout 60000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div#panels-layout-content-id').locator('table').first() to be visible\u001b[22m\n\n    at DialerPage.wait_for_locator (/pages/basePage.js:319:38)\n    at DialerPage.getTableItem (/pages/basePage.js:551:20)\n    at DialerPage.clickTabelCellByIndex (/pages/basePage.js:572:38)\n    at DialerPage.clickRecordsListByIndex (/pages/outbound_dialer/dialerPage.js:1786:14)\n    at DialerPage.addRecordLists (/pages/outbound_dialer/dialerPage.js:1497:28)\n    at /tests/smoke/test_campaign_record_flow_clone.spec.js:841:13\n    at /tests/smoke/test_campaign_record_flow_clone.spec.js:815:9\n    at /tests/smoke/test_campaign_record_flow_clone.spec.js:578:5"}}
{"type":"after","callId":"test.step@297","endTime":188405.998,"attachments":[],"error":{"name":"","message":"TimeoutError: locator.waitFor: Timeout 60000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div#panels-layout-content-id').locator('table').first() to be visible\u001b[22m\n","stack":"TimeoutError: locator.waitFor: Timeout 60000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div#panels-layout-content-id').locator('table').first() to be visible\u001b[22m\n\n    at DialerPage.wait_for_locator (/pages/basePage.js:319:38)\n    at DialerPage.getTableItem (/pages/basePage.js:551:20)\n    at DialerPage.clickTabelCellByIndex (/pages/basePage.js:572:38)\n    at DialerPage.clickRecordsListByIndex (/pages/outbound_dialer/dialerPage.js:1786:14)\n    at DialerPage.addRecordLists (/pages/outbound_dialer/dialerPage.js:1497:28)\n    at /tests/smoke/test_campaign_record_flow_clone.spec.js:841:13\n    at /tests/smoke/test_campaign_record_flow_clone.spec.js:815:9\n    at /tests/smoke/test_campaign_record_flow_clone.spec.js:578:5"}}
{"type":"after","callId":"test.step@7","endTime":188406.071,"attachments":[],"error":{"name":"","message":"TimeoutError: locator.waitFor: Timeout 60000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div#panels-layout-content-id').locator('table').first() to be visible\u001b[22m\n","stack":"TimeoutError: locator.waitFor: Timeout 60000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div#panels-layout-content-id').locator('table').first() to be visible\u001b[22m\n\n    at DialerPage.wait_for_locator (/pages/basePage.js:319:38)\n    at DialerPage.getTableItem (/pages/basePage.js:551:20)\n    at DialerPage.clickTabelCellByIndex (/pages/basePage.js:572:38)\n    at DialerPage.clickRecordsListByIndex (/pages/outbound_dialer/dialerPage.js:1786:14)\n    at DialerPage.addRecordLists (/pages/outbound_dialer/dialerPage.js:1497:28)\n    at /tests/smoke/test_campaign_record_flow_clone.spec.js:841:13\n    at /tests/smoke/test_campaign_record_flow_clone.spec.js:815:9\n    at /tests/smoke/test_campaign_record_flow_clone.spec.js:578:5"}}
{"type":"error","message":"TimeoutError: locator.waitFor: Timeout 60000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div#panels-layout-content-id').locator('table').first() to be visible\u001b[22m\n","stack":[{"file":"/pages/basePage.js","line":319,"column":38,"function":"DialerPage.wait_for_locator"},{"file":"/pages/basePage.js","line":551,"column":20,"function":"DialerPage.getTableItem"},{"file":"/pages/basePage.js","line":572,"column":38,"function":"DialerPage.clickTabelCellByIndex"},{"file":"/pages/outbound_dialer/dialerPage.js","line":1786,"column":14,"function":"DialerPage.clickRecordsListByIndex"},{"file":"/pages/outbound_dialer/dialerPage.js","line":1497,"column":28,"function":"DialerPage.addRecordLists"},{"file":"/tests/smoke/test_campaign_record_flow_clone.spec.js","line":841,"column":13},{"file":"/tests/smoke/test_campaign_record_flow_clone.spec.js","line":815,"column":9},{"file":"/tests/smoke/test_campaign_record_flow_clone.spec.js","line":578,"column":5}]}
{"type":"before","callId":"hook@326","startTime":188406.84,"class":"Test","method":"step","apiName":"After Hooks","params":{},"stack":[]}
{"type":"before","callId":"pw:api@327","parentId":"hook@326","startTime":188408.06,"class":"Test","method":"step","apiName":"page.screenshot","params":{"mode":"only-on-failure","fullPage":"true","timeout":"5000","path":"/test-results/smoke-test_campaign_record-d6feb--Resume-View-Edit-Check-Dup-Chrome-eu-prod/test-failed-1.png","caret":"initial","mask":"undefined","type":"png"},"stack":[]}
{"type":"after","callId":"pw:api@327","endTime":188502.326,"attachments":[]}
{"type":"before","callId":"attach@328","parentId":"hook@326","startTime":188503.118,"class":"Test","method":"step","apiName":"attach \"screenshot\"","params":{},"stack":[]}
{"type":"after","callId":"attach@328","endTime":188503.145,"attachments":[{"name":"screenshot","contentType":"image/png","sha1":"93b605d565da7d9d6e8b616f89338a1d62c3406b"}]}
{"type":"before","callId":"hook@329","parentId":"hook@326","startTime":188503.647,"class":"Test","method":"step","apiName":"afterEach hook","params":{},"stack":[{"file":"/tests/smoke/test_campaign_record_flow_clone.spec.js","line":63,"column":6}]}
{"type":"stdout","timestamp":188503.968,"text":"==== 【po-automation】【failed】Finished test Smoke-Predicative Campaign Create/Start/Pause/Resume/View/Edit/Check/Dup on chromium with status failed=====\n"}
{"type":"after","callId":"hook@329","endTime":188504.02,"attachments":[]}
{"type":"before","callId":"fixture@330","parentId":"hook@326","startTime":188504.362,"class":"Test","method":"step","apiName":"fixture: page","params":{},"stack":[]}
{"type":"after","callId":"fixture@330","endTime":188504.434,"attachments":[]}
{"type":"before","callId":"fixture@331","parentId":"hook@326","startTime":188504.649,"class":"Test","method":"step","apiName":"fixture: context","params":{},"stack":[]}
{"type":"after","callId":"fixture@331","endTime":188504.697,"attachments":[]}
{"type":"before","callId":"pw:api@332","parentId":"hook@326","startTime":190411.406,"class":"Test","method":"step","apiName":"video.saveAs","params":{"path":"/test-results/smoke-test_campaign_record-d6feb--Resume-View-Edit-Check-Dup-Chrome-eu-prod/video.webm"},"stack":[]}
{"type":"after","callId":"pw:api@332","endTime":190414.489,"attachments":[]}
{"type":"before","callId":"attach@333","parentId":"hook@326","startTime":190414.966,"class":"Test","method":"step","apiName":"attach \"video\"","params":{},"stack":[]}
{"type":"after","callId":"attach@333","endTime":190414.987,"attachments":[{"name":"video","contentType":"video/webm","sha1":"b506bb6df044d3a404e95b6b69bea5d2a6c494b2"}]}
{"type":"after","callId":"hook@326","endTime":190415.539,"attachments":[]}
{"type":"before","callId":"hook@334","startTime":190415.871,"class":"Test","method":"step","apiName":"Worker Cleanup","params":{},"stack":[]}
{"type":"before","callId":"fixture@335","parentId":"hook@334","startTime":190416.239,"class":"Test","method":"step","apiName":"fixture: browser","params":{},"stack":[]}
{"type":"after","callId":"fixture@335","endTime":190462.001,"attachments":[]}
{"type":"after","callId":"hook@334","endTime":190462.195,"attachments":[]}