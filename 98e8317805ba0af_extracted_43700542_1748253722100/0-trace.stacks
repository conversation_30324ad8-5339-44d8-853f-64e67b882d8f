{"files": ["/pages/basePage.js", "/pages/outbound_dialer/dialerPage.js", "/tests/smoke/test_campaign_record_flow_clone.spec.js"], "stacks": [[496, [[0, 228, 23, "DialerPage.retry_goto"], [1, 328, 14, "DialerPage.waitForCampaignPage"], [2, 584, 30, ""], [2, 582, 20, ""], [2, 578, 16, ""]]], [498, [[0, 229, 29, "DialerPage.retry_goto"], [1, 328, 14, "DialerPage.waitForCampaignPage"], [2, 584, 30, ""], [2, 582, 20, ""], [2, 578, 16, ""]]], [512, [[0, 356, 25, "DialerPage.wait_for_timeout"], [0, 278, 20, "DialerPage.waitForError_new"], [0, 231, 24, "DialerPage.retry_goto"], [1, 328, 3, "DialerPage.waitForCampaignPage"], [2, 584, 13, ""], [2, 582, 9, ""], [2, 578, 5, ""]]], [514, [[0, 283, 37, "DialerPage.waitForError_new"], [0, 231, 13, "DialerPage.retry_goto"], [1, 328, 3, "DialerPage.waitForCampaignPage"], [2, 584, 13, ""], [2, 582, 9, ""], [2, 578, 5, ""]]], [516, [[0, 356, 25, "DialerPage.wait_for_timeout"], [0, 284, 24, "DialerPage.waitForError_new"], [0, 231, 13, "DialerPage.retry_goto"], [1, 328, 3, "DialerPage.waitForCampaignPage"], [2, 584, 13, ""], [2, 582, 9, ""], [2, 578, 5, ""]]], [518, [[0, 356, 25, "DialerPage.wait_for_timeout"], [0, 278, 20, "DialerPage.waitForError_new"], [0, 285, 24, "DialerPage.waitForError_new"], [0, 231, 13, "DialerPage.retry_goto"], [1, 328, 3, "DialerPage.waitForCampaignPage"], [2, 584, 13, ""], [2, 582, 9, ""], [2, 578, 5, ""]]], [520, [[0, 283, 37, "DialerPage.waitForError_new"], [0, 285, 13, "DialerPage.waitForError_new"], [0, 231, 13, "DialerPage.retry_goto"], [1, 328, 3, "DialerPage.waitForCampaignPage"], [2, 584, 13, ""], [2, 582, 9, ""], [2, 578, 5, ""]]], [522, [[0, 486, 25, "DialerPage.hiddenConflictToast"], [0, 232, 24, "DialerPage.retry_goto"], [1, 328, 3, "DialerPage.waitForCampaignPage"], [2, 584, 13, ""], [2, 582, 9, ""], [2, 578, 5, ""]]], [524, [[1, 329, 38, "DialerPage.waitForCampaignPage"], [2, 584, 13, ""], [2, 582, 9, ""], [2, 578, 5, ""]]], [526, [[1, 412, 31, "DialerPage.getPageTitle"], [1, 330, 27, "DialerPage.waitForCampaignPage"], [2, 584, 13, ""], [2, 582, 9, ""], [2, 578, 5, ""]]], [528, [[0, 292, 47, "DialerPage.waitForVisibleLocator"], [1, 346, 38, "DialerPage.waitForFrameLoad"], [1, 331, 14, "DialerPage.waitForCampaignPage"], [2, 584, 13, ""], [2, 582, 9, ""], [2, 578, 5, ""]]], [530, [[1, 386, 44, "DialerPage.waitForFrameLoad"], [1, 331, 3, "DialerPage.waitForCampaignPage"], [2, 584, 13, ""], [2, 582, 9, ""], [2, 578, 5, ""]]], [532, [[1, 388, 34, "DialerPage.waitForFrameLoad"], [1, 331, 3, "DialerPage.waitForCampaignPage"], [2, 584, 13, ""], [2, 582, 9, ""], [2, 578, 5, ""]]], [534, [[1, 395, 42, "DialerPage.waitForFrameLoad"], [1, 331, 3, "DialerPage.waitForCampaignPage"], [2, 584, 13, ""], [2, 582, 9, ""], [2, 578, 5, ""]]], [536, [[0, 522, 37, "DialerPage.waitAndClearToast"], [1, 332, 14, "DialerPage.waitForCampaignPage"], [2, 584, 13, ""], [2, 582, 9, ""], [2, 578, 5, ""]]], [538, [[0, 524, 74, "DialerPage.waitAndClearToast"], [1, 332, 3, "DialerPage.waitForCampaignPage"], [2, 584, 13, ""], [2, 582, 9, ""], [2, 578, 5, ""]]], [540, [[0, 526, 78, "DialerPage.waitAndClearToast"], [1, 332, 3, "DialerPage.waitForCampaignPage"], [2, 584, 13, ""], [2, 582, 9, ""], [2, 578, 5, ""]]], [542, [[0, 356, 25, "DialerPage.wait_for_timeout"], [0, 533, 24, "DialerPage.waitAndClearToast"], [1, 332, 3, "DialerPage.waitForCampaignPage"], [2, 584, 13, ""], [2, 582, 9, ""], [2, 578, 5, ""]]], [544, [[0, 522, 37, "DialerPage.waitAndClearToast"], [1, 332, 3, "DialerPage.waitForCampaignPage"], [2, 584, 13, ""], [2, 582, 9, ""], [2, 578, 5, ""]]], [546, [[1, 982, 32, "DialerPage.clickCreateCampaign"], [1, 973, 14, "DialerPage.createCampaign"], [2, 597, 30, ""], [2, 588, 20, ""], [2, 578, 5, ""]]], [548, [[1, 975, 14, "DialerPage.createCampaign"], [2, 597, 13, ""], [2, 588, 9, ""], [2, 578, 5, ""]]], [552, [[1, 986, 32, "DialerPage.inputCampaignName"], [1, 976, 15, "DialerPage.createCampaign"], [2, 597, 13, ""], [2, 588, 9, ""], [2, 578, 5, ""]]], [560, [[1, 990, 25, "DialerPage.confirmCreateCampaign"], [1, 978, 14, "DialerPage.createCampaign"], [2, 597, 13, ""], [2, 588, 9, ""], [2, 578, 5, ""]]], [562, [[2, 598, 49, ""], [2, 588, 9, ""], [2, 578, 5, ""]]], [564, [[2, 599, 50, ""], [2, 588, 9, ""], [2, 578, 5, ""]]], [566, [[2, 600, 54, ""], [2, 588, 9, ""], [2, 578, 5, ""]]], [568, [[2, 601, 46, ""], [2, 588, 9, ""], [2, 578, 5, ""]]], [570, [[2, 602, 50, ""], [2, 588, 9, ""], [2, 578, 5, ""]]], [572, [[0, 103, 60, "DialerPage.saveImage"], [2, 603, 30, ""], [2, 588, 9, ""], [2, 578, 5, ""]]], [574, [[0, 522, 37, "DialerPage.waitAndClearToast"], [2, 612, 30, ""], [2, 606, 20, ""], [2, 578, 5, ""]]], [576, [[1, 926, 45, "DialerPage.getSettingTitle"], [2, 613, 43, ""], [2, 606, 9, ""], [2, 578, 5, ""]]], [578, [[1, 1054, 32, "DialerPage.setDialingMode"], [2, 615, 30, ""], [2, 606, 9, ""], [2, 578, 5, ""]]], [580, [[1, 1055, 124, "DialerPage.setDialingMode"], [2, 615, 13, ""], [2, 606, 9, ""], [2, 578, 5, ""]]], [582, [[2, 616, 64, ""], [2, 606, 9, ""], [2, 578, 5, ""]]], [584, [[1, 1034, 78, "DialerPage.setCampaignPriority"], [2, 617, 30, ""], [2, 606, 9, ""], [2, 578, 5, ""]]], [586, [[1, 1042, 31, "DialerPage.checkPriorityValue"], [2, 618, 13, ""], [2, 606, 9, ""], [2, 578, 5, ""]]], [588, [[1, 1042, 31, "DialerPage.checkPriorityValue"], [2, 618, 13, ""], [2, 606, 9, ""], [2, 578, 5, ""]]], [590, [[1, 1042, 31, "DialerPage.checkPriorityValue"], [2, 618, 13, ""], [2, 606, 9, ""], [2, 578, 5, ""]]], [592, [[1, 1042, 31, "DialerPage.checkPriorityValue"], [2, 618, 13, ""], [2, 606, 9, ""], [2, 578, 5, ""]]], [594, [[1, 1042, 31, "DialerPage.checkPriorityValue"], [2, 618, 13, ""], [2, 606, 9, ""], [2, 578, 5, ""]]], [596, [[1, 1042, 31, "DialerPage.checkPriorityValue"], [2, 618, 13, ""], [2, 606, 9, ""], [2, 578, 5, ""]]], [598, [[1, 1042, 31, "DialerPage.checkPriorityValue"], [2, 618, 13, ""], [2, 606, 9, ""], [2, 578, 5, ""]]], [600, [[1, 1042, 31, "DialerPage.checkPriorityValue"], [2, 618, 13, ""], [2, 606, 9, ""], [2, 578, 5, ""]]], [602, [[1, 1042, 31, "DialerPage.checkPriorityValue"], [2, 618, 13, ""], [2, 606, 9, ""], [2, 578, 5, ""]]], [604, [[0, 103, 60, "DialerPage.saveImage"], [2, 619, 30, ""], [2, 606, 9, ""], [2, 578, 5, ""]]], [606, [[1, 1059, 32, "DialerPage.setDialingRadio"], [2, 627, 30, ""], [2, 622, 20, ""], [2, 578, 5, ""]]], [608, [[0, 356, 25, "DialerPage.wait_for_timeout"], [1, 1060, 14, "DialerPage.setDialingRadio"], [2, 627, 13, ""], [2, 622, 9, ""], [2, 578, 5, ""]]], [610, [[2, 628, 71, ""], [2, 622, 9, ""], [2, 578, 5, ""]]], [612, [[0, 103, 60, "DialerPage.saveImage"], [2, 629, 30, ""], [2, 622, 9, ""], [2, 578, 5, ""]]], [614, [[1, 1064, 35, "DialerPage.setAbandonmentRate"], [2, 638, 30, ""], [2, 633, 20, ""], [2, 578, 5, ""]]], [616, [[0, 356, 25, "DialerPage.wait_for_timeout"], [1, 1065, 14, "DialerPage.setAbandonmentRate"], [2, 638, 13, ""], [2, 633, 9, ""], [2, 578, 5, ""]]], [618, [[2, 639, 71, ""], [2, 633, 9, ""], [2, 578, 5, ""]]], [620, [[0, 103, 60, "DialerPage.saveImage"], [2, 640, 30, ""], [2, 633, 9, ""], [2, 578, 5, ""]]], [622, [[1, 1069, 38, "DialerPage.setAbandonmentTimeout"], [2, 646, 30, ""], [2, 644, 20, ""], [2, 578, 5, ""]]], [624, [[0, 356, 25, "DialerPage.wait_for_timeout"], [1, 1070, 14, "DialerPage.setAbandonmentTimeout"], [2, 646, 13, ""], [2, 644, 9, ""], [2, 578, 5, ""]]], [626, [[2, 647, 74, ""], [2, 644, 9, ""], [2, 578, 5, ""]]], [628, [[0, 103, 60, "DialerPage.saveImage"], [2, 648, 30, ""], [2, 644, 9, ""], [2, 578, 5, ""]]], [630, [[1, 1074, 31, "DialerPage.setMaxRingtime"], [2, 653, 30, ""], [2, 651, 20, ""], [2, 578, 5, ""]]], [632, [[0, 356, 25, "DialerPage.wait_for_timeout"], [1, 1075, 14, "DialerPage.setMaxRingtime"], [2, 653, 13, ""], [2, 651, 9, ""], [2, 578, 5, ""]]], [634, [[2, 654, 67, ""], [2, 651, 9, ""], [2, 578, 5, ""]]], [636, [[0, 103, 60, "DialerPage.saveImage"], [2, 655, 30, ""], [2, 651, 9, ""], [2, 578, 5, ""]]], [638, [[1, 963, 22, "DialerPage.saveAndNext"], [2, 663, 30, ""], [2, 658, 20, ""], [2, 578, 5, ""]]], [640, [[1, 926, 45, "DialerPage.getSettingTitle"], [2, 664, 43, ""], [2, 658, 9, ""], [2, 578, 5, ""]]], [642, [[0, 103, 60, "DialerPage.saveImage"], [2, 665, 30, ""], [2, 658, 9, ""], [2, 578, 5, ""]]], [644, [[1, 1105, 14, "DialerPage.selectCallIDs"], [2, 675, 30, ""], [2, 668, 20, ""], [2, 578, 5, ""]]], [648, [[1, 1124, 26, "DialerPage.clickCallIDsLink"], [1, 1106, 15, "DialerPage.selectCallIDs"], [2, 675, 30, ""], [2, 668, 20, ""], [2, 578, 5, ""]]], [656, [[1, 1108, 36, "DialerPage.selectCallIDs"], [2, 675, 13, ""], [2, 668, 9, ""], [2, 578, 5, ""]]], [658, [[0, 319, 38, "DialerPage.wait_for_locator"], [1, 1111, 15, "DialerPage.selectCallIDs"], [2, 675, 13, ""], [2, 668, 9, ""], [2, 578, 5, ""]]], [660, [[0, 319, 38, "DialerPage.wait_for_locator"], [0, 392, 24, "DialerPage.wait_for_animated"], [1, 1112, 24, "DialerPage.selectCallIDs"], [2, 675, 13, ""], [2, 668, 9, ""], [2, 578, 5, ""]]], [662, [[0, 395, 40, "DialerPage.wait_for_animated"], [1, 1112, 13, "DialerPage.selectCallIDs"], [2, 675, 13, ""], [2, 668, 9, ""], [2, 578, 5, ""]]], [664, [[0, 356, 25, "DialerPage.wait_for_timeout"], [0, 396, 28, "DialerPage.wait_for_animated"], [1, 1112, 13, "DialerPage.selectCallIDs"], [2, 675, 13, ""], [2, 668, 9, ""], [2, 578, 5, ""]]], [666, [[0, 356, 25, "DialerPage.wait_for_timeout"], [1, 1130, 15, "DialerPage.selectCallIDsByIndex"], [1, 1118, 14, "DialerPage.selectCallIDs"], [2, 675, 13, ""], [2, 668, 9, ""], [2, 578, 5, ""]]], [668, [[0, 319, 38, "DialerPage.wait_for_locator"], [0, 551, 20, "DialerPage.getTableItem"], [0, 572, 38, "DialerPage.clickTabelCellByIndex"], [1, 1131, 14, "DialerPage.selectCallIDsByIndex"], [1, 1118, 3, "DialerPage.selectCallIDs"], [2, 675, 13, ""], [2, 668, 9, ""], [2, 578, 5, ""]]], [670, [[0, 319, 38, "DialerPage.wait_for_locator"], [0, 562, 20, "DialerPage.getTabelCellByIndex"], [0, 573, 38, "DialerPage.clickTabelCellByIndex"], [1, 1131, 3, "DialerPage.selectCallIDsByIndex"], [1, 1118, 3, "DialerPage.selectCallIDs"], [2, 675, 13, ""], [2, 668, 9, ""], [2, 578, 5, ""]]], [672, [[0, 319, 38, "DialerPage.wait_for_locator"], [0, 576, 24, "DialerPage.clickTabelCellByIndex"], [1, 1131, 3, "DialerPage.selectCallIDsByIndex"], [1, 1118, 3, "DialerPage.selectCallIDs"], [2, 675, 13, ""], [2, 668, 9, ""], [2, 578, 5, ""]]], [674, [[0, 577, 30, "DialerPage.clickTabelCellByIndex"], [1, 1131, 3, "DialerPage.selectCallIDsByIndex"], [1, 1118, 3, "DialerPage.selectCallIDs"], [2, 675, 13, ""], [2, 668, 9, ""], [2, 578, 5, ""]]], [676, [[0, 522, 37, "DialerPage.waitAndClearToast"], [1, 1119, 14, "DialerPage.selectCallIDs"], [2, 675, 13, ""], [2, 668, 9, ""], [2, 578, 5, ""]]], [678, [[1, 1135, 36, "DialerPage.clickConfirmSelectButton"], [1, 1120, 14, "DialerPage.selectCallIDs"], [2, 675, 13, ""], [2, 668, 9, ""], [2, 578, 5, ""]]], [680, [[0, 103, 60, "DialerPage.saveImage"], [2, 676, 30, ""], [2, 668, 9, ""], [2, 578, 5, ""]]], [682, [[1, 1141, 67, "DialerPage.setCallingDays"], [2, 684, 30, ""], [2, 679, 20, ""], [2, 578, 5, ""]]], [684, [[1, 1141, 67, "DialerPage.setCallingDays"], [2, 684, 13, ""], [2, 679, 9, ""], [2, 578, 5, ""]]], [686, [[0, 103, 60, "DialerPage.saveImage"], [2, 685, 30, ""], [2, 679, 9, ""], [2, 578, 5, ""]]], [688, [[1, 1150, 33, "DialerPage.setCallingFromHour"], [2, 693, 30, ""], [2, 688, 20, ""], [2, 578, 5, ""]]], [690, [[1, 1154, 29, "DialerPage.setFromHour"], [2, 694, 30, ""], [2, 688, 9, ""], [2, 578, 5, ""]]], [692, [[1, 1158, 31, "DialerPage.setFromMinute"], [2, 695, 30, ""], [2, 688, 9, ""], [2, 578, 5, ""]]], [694, [[1, 1174, 57, "DialerPage.setFromMeridian"], [2, 696, 30, ""], [2, 688, 9, ""], [2, 578, 5, ""]]], [696, [[1, 1146, 29, "DialerPage.blur"], [2, 697, 30, ""], [2, 688, 9, ""], [2, 578, 5, ""]]], [698, [[1, 1170, 31, "DialerPage.setCallingToHour"], [2, 699, 30, ""], [2, 688, 9, ""], [2, 578, 5, ""]]], [700, [[1, 1162, 27, "DialerPage.setToHour"], [2, 700, 30, ""], [2, 688, 9, ""], [2, 578, 5, ""]]], [702, [[1, 1166, 29, "DialerPage.setToMinute"], [2, 701, 30, ""], [2, 688, 9, ""], [2, 578, 5, ""]]], [704, [[1, 1183, 53, "DialerPage.setToMeridian"], [2, 702, 30, ""], [2, 688, 9, ""], [2, 578, 5, ""]]], [706, [[1, 1146, 29, "DialerPage.blur"], [2, 703, 30, ""], [2, 688, 9, ""], [2, 578, 5, ""]]], [708, [[0, 103, 60, "DialerPage.saveImage"], [2, 704, 30, ""], [2, 688, 9, ""], [2, 578, 5, ""]]], [710, [[1, 1192, 37, "DialerPage.setGlobalMaxAttempts"], [2, 712, 30, ""], [2, 707, 20, ""], [2, 578, 5, ""]]], [712, [[0, 356, 25, "DialerPage.wait_for_timeout"], [1, 1193, 14, "DialerPage.setGlobalMaxAttempts"], [2, 712, 13, ""], [2, 707, 9, ""], [2, 578, 5, ""]]], [714, [[2, 713, 76, ""], [2, 707, 9, ""], [2, 578, 5, ""]]], [716, [[0, 103, 60, "DialerPage.saveImage"], [2, 714, 30, ""], [2, 707, 9, ""], [2, 578, 5, ""]]], [718, [[1, 1213, 36, "DialerPage.setRetryPeriod"], [2, 720, 30, ""], [2, 718, 20, ""], [2, 578, 5, ""]]], [720, [[1, 1215, 71, "DialerPage.setRetryPeriod"], [2, 720, 13, ""], [2, 718, 9, ""], [2, 578, 5, ""]]], [722, [[1, 1226, 31, "DialerPage.setRetryPeriod"], [2, 720, 13, ""], [2, 718, 9, ""], [2, 578, 5, ""]]], [724, [[0, 356, 25, "DialerPage.wait_for_timeout"], [1, 1227, 14, "DialerPage.setRetryPeriod"], [2, 720, 13, ""], [2, 718, 9, ""], [2, 578, 5, ""]]], [726, [[2, 721, 70, ""], [2, 718, 9, ""], [2, 578, 5, ""]]], [728, [[0, 103, 60, "DialerPage.saveImage"], [2, 722, 30, ""], [2, 718, 9, ""], [2, 578, 5, ""]]], [730, [[0, 103, 60, "DialerPage.saveImage"], [2, 743, 30, ""], [2, 726, 20, ""], [2, 578, 5, ""]]], [732, [[1, 1312, 14, "DialerPage.transiteToAgentStep"], [2, 751, 30, ""], [2, 746, 20, ""], [2, 578, 5, ""]]], [736, [[1, 963, 22, "DialerPage.saveAndNext"], [1, 1313, 15, "DialerPage.transiteToAgentStep"], [2, 751, 30, ""], [2, 746, 20, ""], [2, 578, 5, ""]]], [744, [[1, 926, 45, "DialerPage.getSettingTitle"], [2, 752, 43, ""], [2, 746, 9, ""], [2, 578, 5, ""]]], [746, [[0, 103, 60, "DialerPage.saveImage"], [2, 753, 30, ""], [2, 746, 9, ""], [2, 578, 5, ""]]], [748, [[1, 1340, 14, "DialerPage.selectRingGroups"], [2, 767, 30, ""], [2, 756, 20, ""], [2, 578, 5, ""]]], [752, [[1, 1341, 25, "DialerPage.selectRingGroups"], [2, 767, 30, ""], [2, 756, 20, ""], [2, 578, 5, ""]]], [760, [[0, 356, 25, "DialerPage.wait_for_timeout"], [1, 1343, 14, "DialerPage.selectRingGroups"], [2, 767, 13, ""], [2, 756, 9, ""], [2, 578, 5, ""]]], [762, [[1, 1344, 27, "DialerPage.selectRingGroups"], [2, 767, 13, ""], [2, 756, 9, ""], [2, 578, 5, ""]]], [764, [[1, 1353, 16, "DialerPage.selectRingGroups"], [2, 767, 13, ""], [2, 756, 9, ""], [2, 578, 5, ""]]], [768, [[1, 1354, 38, "DialerPage.selectRingGroups"], [2, 767, 13, ""], [2, 756, 9, ""], [2, 578, 5, ""]]], [776, [[1, 1356, 32, "DialerPage.selectRingGroups"], [2, 767, 13, ""], [2, 756, 9, ""], [2, 578, 5, ""]]], [778, [[1, 1361, 85, "DialerPage.selectRingGroups"], [2, 767, 13, ""], [2, 756, 9, ""], [2, 578, 5, ""]]], [780, [[1, 1364, 16, "DialerPage.selectRingGroups"], [2, 767, 13, ""], [2, 756, 9, ""], [2, 578, 5, ""]]], [784, [[1, 1365, 31, "DialerPage.selectRingGroups"], [2, 767, 13, ""], [2, 756, 9, ""], [2, 578, 5, ""]]], [792, [[1, 1380, 40, "DialerPage.clickClearFilterSettings"], [2, 768, 30, ""], [2, 756, 9, ""], [2, 578, 5, ""]]], [794, [[1, 1340, 14, "DialerPage.selectRingGroups"], [2, 769, 30, ""], [2, 756, 9, ""], [2, 578, 5, ""]]], [798, [[1, 1341, 25, "DialerPage.selectRingGroups"], [2, 769, 30, ""], [2, 756, 9, ""], [2, 578, 5, ""]]], [806, [[0, 356, 25, "DialerPage.wait_for_timeout"], [1, 1343, 14, "DialerPage.selectRingGroups"], [2, 769, 13, ""], [2, 756, 9, ""], [2, 578, 5, ""]]], [808, [[1, 1344, 27, "DialerPage.selectRingGroups"], [2, 769, 13, ""], [2, 756, 9, ""], [2, 578, 5, ""]]], [810, [[1, 1353, 16, "DialerPage.selectRingGroups"], [2, 769, 13, ""], [2, 756, 9, ""], [2, 578, 5, ""]]], [814, [[1, 1354, 38, "DialerPage.selectRingGroups"], [2, 769, 13, ""], [2, 756, 9, ""], [2, 578, 5, ""]]], [822, [[1, 1356, 32, "DialerPage.selectRingGroups"], [2, 769, 13, ""], [2, 756, 9, ""], [2, 578, 5, ""]]], [824, [[1, 1361, 85, "DialerPage.selectRingGroups"], [2, 769, 13, ""], [2, 756, 9, ""], [2, 578, 5, ""]]], [826, [[1, 1353, 16, "DialerPage.selectRingGroups"], [2, 769, 13, ""], [2, 756, 9, ""], [2, 578, 5, ""]]], [830, [[1, 1354, 38, "DialerPage.selectRingGroups"], [2, 769, 13, ""], [2, 756, 9, ""], [2, 578, 5, ""]]], [838, [[1, 1356, 32, "DialerPage.selectRingGroups"], [2, 769, 13, ""], [2, 756, 9, ""], [2, 578, 5, ""]]], [840, [[1, 1361, 85, "DialerPage.selectRingGroups"], [2, 769, 13, ""], [2, 756, 9, ""], [2, 578, 5, ""]]], [842, [[1, 1364, 16, "DialerPage.selectRingGroups"], [2, 769, 13, ""], [2, 756, 9, ""], [2, 578, 5, ""]]], [846, [[1, 1365, 31, "DialerPage.selectRingGroups"], [2, 769, 13, ""], [2, 756, 9, ""], [2, 578, 5, ""]]], [854, [[1, 1380, 40, "DialerPage.clickClearFilterSettings"], [2, 770, 30, ""], [2, 756, 9, ""], [2, 578, 5, ""]]], [856, [[1, 1340, 14, "DialerPage.selectRingGroups"], [2, 771, 30, ""], [2, 756, 9, ""], [2, 578, 5, ""]]], [860, [[1, 1341, 25, "DialerPage.selectRingGroups"], [2, 771, 30, ""], [2, 756, 9, ""], [2, 578, 5, ""]]], [868, [[0, 356, 25, "DialerPage.wait_for_timeout"], [1, 1343, 14, "DialerPage.selectRingGroups"], [2, 771, 13, ""], [2, 756, 9, ""], [2, 578, 5, ""]]], [870, [[1, 1344, 27, "DialerPage.selectRingGroups"], [2, 771, 13, ""], [2, 756, 9, ""], [2, 578, 5, ""]]], [872, [[1, 1346, 72, "DialerPage.selectRingGroups"], [2, 771, 13, ""], [2, 756, 9, ""], [2, 578, 5, ""]]], [874, [[1, 1347, 29, "DialerPage.selectRingGroups"], [2, 771, 13, ""], [2, 756, 9, ""], [2, 578, 5, ""]]], [876, [[1, 1380, 40, "DialerPage.clickClearFilterSettings"], [2, 772, 30, ""], [2, 756, 9, ""], [2, 578, 5, ""]]], [878, [[1, 1384, 24, "DialerPage.clickFilterButton"], [2, 773, 30, ""], [2, 756, 9, ""], [2, 578, 5, ""]]], [880, [[1, 1388, 29, "DialerPage.cancelFilterPanel"], [2, 774, 30, ""], [2, 756, 9, ""], [2, 578, 5, ""]]], [882, [[2, 775, 51, ""], [2, 756, 9, ""], [2, 578, 5, ""]]], [884, [[0, 103, 60, "DialerPage.saveImage"], [2, 776, 30, ""], [2, 756, 9, ""], [2, 578, 5, ""]]], [886, [[1, 1403, 15, "DialerPage.selectAgents"], [2, 785, 30, ""], [2, 780, 20, ""], [2, 578, 5, ""]]], [890, [[1, 1404, 33, "DialerPage.selectAgents"], [2, 785, 30, ""], [2, 780, 20, ""], [2, 578, 5, ""]]], [898, [[1, 1408, 37, "DialerPage.selectAgents"], [2, 785, 13, ""], [2, 780, 9, ""], [2, 578, 5, ""]]], [900, [[0, 356, 25, "DialerPage.wait_for_timeout"], [1, 1416, 15, "DialerPage.selectAgents"], [2, 785, 13, ""], [2, 780, 9, ""], [2, 578, 5, ""]]], [902, [[0, 319, 38, "DialerPage.wait_for_locator"], [0, 551, 20, "DialerPage.getTableItem"], [0, 572, 38, "DialerPage.clickTabelCellByIndex"], [1, 1417, 15, "DialerPage.selectAgents"], [2, 785, 13, ""], [2, 780, 9, ""], [2, 578, 5, ""]]], [904, [[0, 319, 38, "DialerPage.wait_for_locator"], [0, 562, 20, "DialerPage.getTabelCellByIndex"], [0, 573, 38, "DialerPage.clickTabelCellByIndex"], [1, 1417, 4, "DialerPage.selectAgents"], [2, 785, 13, ""], [2, 780, 9, ""], [2, 578, 5, ""]]], [906, [[0, 319, 38, "DialerPage.wait_for_locator"], [0, 580, 24, "DialerPage.clickTabelCellByIndex"], [1, 1417, 4, "DialerPage.selectAgents"], [2, 785, 13, ""], [2, 780, 9, ""], [2, 578, 5, ""]]], [908, [[0, 581, 30, "DialerPage.clickTabelCellByIndex"], [1, 1417, 4, "DialerPage.selectAgents"], [2, 785, 13, ""], [2, 780, 9, ""], [2, 578, 5, ""]]], [910, [[0, 356, 25, "DialerPage.wait_for_timeout"], [1, 1418, 15, "DialerPage.selectAgents"], [2, 785, 13, ""], [2, 780, 9, ""], [2, 578, 5, ""]]], [912, [[1, 1403, 15, "DialerPage.selectAgents"], [2, 785, 13, ""], [2, 780, 9, ""], [2, 578, 5, ""]]], [916, [[1, 1404, 33, "DialerPage.selectAgents"], [2, 785, 13, ""], [2, 780, 9, ""], [2, 578, 5, ""]]], [924, [[1, 1408, 37, "DialerPage.selectAgents"], [2, 785, 13, ""], [2, 780, 9, ""], [2, 578, 5, ""]]], [926, [[0, 356, 25, "DialerPage.wait_for_timeout"], [1, 1416, 15, "DialerPage.selectAgents"], [2, 785, 13, ""], [2, 780, 9, ""], [2, 578, 5, ""]]], [928, [[0, 319, 38, "DialerPage.wait_for_locator"], [0, 551, 20, "DialerPage.getTableItem"], [0, 572, 38, "DialerPage.clickTabelCellByIndex"], [1, 1417, 15, "DialerPage.selectAgents"], [2, 785, 13, ""], [2, 780, 9, ""], [2, 578, 5, ""]]], [930, [[0, 319, 38, "DialerPage.wait_for_locator"], [0, 562, 20, "DialerPage.getTabelCellByIndex"], [0, 573, 38, "DialerPage.clickTabelCellByIndex"], [1, 1417, 4, "DialerPage.selectAgents"], [2, 785, 13, ""], [2, 780, 9, ""], [2, 578, 5, ""]]], [932, [[0, 319, 38, "DialerPage.wait_for_locator"], [0, 580, 24, "DialerPage.clickTabelCellByIndex"], [1, 1417, 4, "DialerPage.selectAgents"], [2, 785, 13, ""], [2, 780, 9, ""], [2, 578, 5, ""]]], [934, [[0, 581, 30, "DialerPage.clickTabelCellByIndex"], [1, 1417, 4, "DialerPage.selectAgents"], [2, 785, 13, ""], [2, 780, 9, ""], [2, 578, 5, ""]]], [936, [[0, 356, 25, "DialerPage.wait_for_timeout"], [1, 1418, 15, "DialerPage.selectAgents"], [2, 785, 13, ""], [2, 780, 9, ""], [2, 578, 5, ""]]], [938, [[0, 103, 60, "DialerPage.saveImage"], [2, 786, 30, ""], [2, 780, 9, ""], [2, 578, 5, ""]]], [940, [[0, 319, 38, "DialerPage.wait_for_locator"], [0, 551, 20, "DialerPage.getTableItem"], [2, 794, 70, ""], [2, 789, 20, ""], [2, 578, 5, ""]]], [942, [[1, 1392, 37, "DialerPage.clickAssignmentDetails"], [2, 794, 30, ""], [2, 789, 9, ""], [2, 578, 5, ""]]], [944, [[1, 1396, 28, "DialerPage.close_assignmentDetailPanel"], [2, 795, 30, ""], [2, 789, 9, ""], [2, 578, 5, ""]]], [946, [[1, 955, 50, "DialerPage.getListSize"], [2, 796, 47, ""], [2, 789, 9, ""], [2, 578, 5, ""]]], [948, [[1, 963, 22, "DialerPage.saveAndNext"], [2, 798, 30, ""], [2, 789, 9, ""], [2, 578, 5, ""]]], [950, [[2, 799, 48, ""], [2, 789, 9, ""], [2, 578, 5, ""]]], [952, [[0, 103, 60, "DialerPage.saveImage"], [2, 800, 30, ""], [2, 789, 9, ""], [2, 578, 5, ""]]], [954, [[1, 1486, 14, "DialerPage.addRecordLists"], [2, 810, 55, ""], [2, 804, 20, ""], [2, 578, 5, ""]]], [958, [[1, 1753, 35, "DialerPage.clickAddRecordsListButton"], [1, 1487, 15, "DialerPage.addRecordLists"], [2, 810, 55, ""], [2, 804, 20, ""], [2, 578, 5, ""]]], [966, [[1, 1489, 32, "DialerPage.addRecordLists"], [2, 810, 38, ""], [2, 804, 9, ""], [2, 578, 5, ""]]], [968, [[0, 319, 38, "DialerPage.wait_for_locator"], [1, 1492, 15, "DialerPage.addRecordLists"], [2, 810, 38, ""], [2, 804, 9, ""], [2, 578, 5, ""]]], [970, [[0, 319, 38, "DialerPage.wait_for_locator"], [0, 392, 24, "DialerPage.wait_for_animated"], [1, 1493, 15, "DialerPage.addRecordLists"], [2, 810, 38, ""], [2, 804, 9, ""], [2, 578, 5, ""]]], [972, [[0, 395, 40, "DialerPage.wait_for_animated"], [1, 1493, 4, "DialerPage.addRecordLists"], [2, 810, 38, ""], [2, 804, 9, ""], [2, 578, 5, ""]]], [974, [[0, 356, 25, "DialerPage.wait_for_timeout"], [0, 396, 28, "DialerPage.wait_for_animated"], [1, 1493, 4, "DialerPage.addRecordLists"], [2, 810, 38, ""], [2, 804, 9, ""], [2, 578, 5, ""]]], [976, [[1, 1495, 35, "DialerPage.addRecordLists"], [2, 810, 38, ""], [2, 804, 9, ""], [2, 578, 5, ""]]], [978, [[0, 319, 38, "DialerPage.wait_for_locator"], [0, 551, 20, "DialerPage.getTableItem"], [0, 572, 38, "DialerPage.clickTabelCellByIndex"], [1, 1786, 14, "DialerPage.clickRecordsListByIndex"], [1, 1497, 39, "DialerPage.addRecordLists"], [2, 810, 38, ""], [2, 804, 9, ""], [2, 578, 5, ""]]], [980, [[0, 319, 38, "DialerPage.wait_for_locator"], [0, 562, 20, "DialerPage.getTabelCellByIndex"], [0, 573, 38, "DialerPage.clickTabelCellByIndex"], [1, 1786, 3, "DialerPage.clickRecordsListByIndex"], [1, 1497, 28, "DialerPage.addRecordLists"], [2, 810, 38, ""], [2, 804, 9, ""], [2, 578, 5, ""]]], [982, [[0, 319, 38, "DialerPage.wait_for_locator"], [0, 576, 24, "DialerPage.clickTabelCellByIndex"], [1, 1786, 3, "DialerPage.clickRecordsListByIndex"], [1, 1497, 28, "DialerPage.addRecordLists"], [2, 810, 38, ""], [2, 804, 9, ""], [2, 578, 5, ""]]], [984, [[0, 577, 30, "DialerPage.clickTabelCellByIndex"], [1, 1786, 3, "DialerPage.clickRecordsListByIndex"], [1, 1497, 28, "DialerPage.addRecordLists"], [2, 810, 38, ""], [2, 804, 9, ""], [2, 578, 5, ""]]], [986, [[0, 319, 38, "DialerPage.wait_for_locator"], [0, 551, 20, "DialerPage.getTableItem"], [1, 1787, 32, "DialerPage.clickRecordsListByIndex"], [1, 1497, 28, "DialerPage.addRecordLists"], [2, 810, 38, ""], [2, 804, 9, ""], [2, 578, 5, ""]]], [988, [[0, 319, 38, "DialerPage.wait_for_locator"], [0, 562, 20, "DialerPage.getTabelCellByIndex"], [1, 1788, 29, "DialerPage.clickRecordsListByIndex"], [1, 1497, 28, "DialerPage.addRecordLists"], [2, 810, 38, ""], [2, 804, 9, ""], [2, 578, 5, ""]]], [990, [[1, 1789, 23, "DialerPage.clickRecordsListByIndex"], [1, 1497, 28, "DialerPage.addRecordLists"], [2, 810, 38, ""], [2, 804, 9, ""], [2, 578, 5, ""]]], [992, [[0, 319, 38, "DialerPage.wait_for_locator"], [0, 551, 20, "DialerPage.getTableItem"], [0, 572, 38, "DialerPage.clickTabelCellByIndex"], [1, 1786, 14, "DialerPage.clickRecordsListByIndex"], [1, 1497, 39, "DialerPage.addRecordLists"], [2, 810, 38, ""], [2, 804, 9, ""], [2, 578, 5, ""]]], [994, [[0, 319, 38, "DialerPage.wait_for_locator"], [0, 562, 20, "DialerPage.getTabelCellByIndex"], [0, 573, 38, "DialerPage.clickTabelCellByIndex"], [1, 1786, 3, "DialerPage.clickRecordsListByIndex"], [1, 1497, 28, "DialerPage.addRecordLists"], [2, 810, 38, ""], [2, 804, 9, ""], [2, 578, 5, ""]]], [996, [[0, 319, 38, "DialerPage.wait_for_locator"], [0, 576, 24, "DialerPage.clickTabelCellByIndex"], [1, 1786, 3, "DialerPage.clickRecordsListByIndex"], [1, 1497, 28, "DialerPage.addRecordLists"], [2, 810, 38, ""], [2, 804, 9, ""], [2, 578, 5, ""]]], [998, [[0, 577, 30, "DialerPage.clickTabelCellByIndex"], [1, 1786, 3, "DialerPage.clickRecordsListByIndex"], [1, 1497, 28, "DialerPage.addRecordLists"], [2, 810, 38, ""], [2, 804, 9, ""], [2, 578, 5, ""]]], [1000, [[0, 319, 38, "DialerPage.wait_for_locator"], [0, 551, 20, "DialerPage.getTableItem"], [1, 1787, 32, "DialerPage.clickRecordsListByIndex"], [1, 1497, 28, "DialerPage.addRecordLists"], [2, 810, 38, ""], [2, 804, 9, ""], [2, 578, 5, ""]]], [1002, [[0, 319, 38, "DialerPage.wait_for_locator"], [0, 562, 20, "DialerPage.getTabelCellByIndex"], [1, 1788, 29, "DialerPage.clickRecordsListByIndex"], [1, 1497, 28, "DialerPage.addRecordLists"], [2, 810, 38, ""], [2, 804, 9, ""], [2, 578, 5, ""]]], [1004, [[1, 1789, 23, "DialerPage.clickRecordsListByIndex"], [1, 1497, 28, "DialerPage.addRecordLists"], [2, 810, 38, ""], [2, 804, 9, ""], [2, 578, 5, ""]]], [1006, [[0, 319, 38, "DialerPage.wait_for_locator"], [0, 551, 20, "DialerPage.getTableItem"], [0, 572, 38, "DialerPage.clickTabelCellByIndex"], [1, 1786, 14, "DialerPage.clickRecordsListByIndex"], [1, 1497, 39, "DialerPage.addRecordLists"], [2, 810, 38, ""], [2, 804, 9, ""], [2, 578, 5, ""]]], [1008, [[0, 319, 38, "DialerPage.wait_for_locator"], [0, 562, 20, "DialerPage.getTabelCellByIndex"], [0, 573, 38, "DialerPage.clickTabelCellByIndex"], [1, 1786, 3, "DialerPage.clickRecordsListByIndex"], [1, 1497, 28, "DialerPage.addRecordLists"], [2, 810, 38, ""], [2, 804, 9, ""], [2, 578, 5, ""]]], [1010, [[0, 319, 38, "DialerPage.wait_for_locator"], [0, 576, 24, "DialerPage.clickTabelCellByIndex"], [1, 1786, 3, "DialerPage.clickRecordsListByIndex"], [1, 1497, 28, "DialerPage.addRecordLists"], [2, 810, 38, ""], [2, 804, 9, ""], [2, 578, 5, ""]]], [1012, [[0, 577, 30, "DialerPage.clickTabelCellByIndex"], [1, 1786, 3, "DialerPage.clickRecordsListByIndex"], [1, 1497, 28, "DialerPage.addRecordLists"], [2, 810, 38, ""], [2, 804, 9, ""], [2, 578, 5, ""]]], [1014, [[0, 319, 38, "DialerPage.wait_for_locator"], [0, 551, 20, "DialerPage.getTableItem"], [1, 1787, 32, "DialerPage.clickRecordsListByIndex"], [1, 1497, 28, "DialerPage.addRecordLists"], [2, 810, 38, ""], [2, 804, 9, ""], [2, 578, 5, ""]]], [1016, [[0, 319, 38, "DialerPage.wait_for_locator"], [0, 562, 20, "DialerPage.getTabelCellByIndex"], [1, 1788, 29, "DialerPage.clickRecordsListByIndex"], [1, 1497, 28, "DialerPage.addRecordLists"], [2, 810, 38, ""], [2, 804, 9, ""], [2, 578, 5, ""]]], [1018, [[1, 1789, 23, "DialerPage.clickRecordsListByIndex"], [1, 1497, 28, "DialerPage.addRecordLists"], [2, 810, 38, ""], [2, 804, 9, ""], [2, 578, 5, ""]]], [1020, [[0, 522, 37, "DialerPage.waitAndClearToast"], [1, 1500, 15, "DialerPage.addRecordLists"], [2, 810, 38, ""], [2, 804, 9, ""], [2, 578, 5, ""]]], [1022, [[0, 522, 37, "DialerPage.waitAndClearToast"], [1, 1827, 14, "DialerPage.clickRecordsListApplyButton"], [1, 1501, 15, "DialerPage.addRecordLists"], [2, 810, 38, ""], [2, 804, 9, ""], [2, 578, 5, ""]]], [1024, [[1, 1828, 37, "DialerPage.clickRecordsListApplyButton"], [1, 1501, 4, "DialerPage.addRecordLists"], [2, 810, 38, ""], [2, 804, 9, ""], [2, 578, 5, ""]]], [1026, [[1, 955, 50, "DialerPage.getListSize"], [1, 1714, 37, "DialerPage.checkSelectedRecordList"], [2, 811, 30, ""], [2, 804, 9, ""], [2, 578, 5, ""]]], [1028, [[0, 319, 38, "DialerPage.wait_for_locator"], [0, 551, 20, "DialerPage.getTableItem"], [1, 1716, 33, "DialerPage.checkSelectedRecordList"], [2, 811, 13, ""], [2, 804, 9, ""], [2, 578, 5, ""]]], [1030, [[0, 319, 38, "DialerPage.wait_for_locator"], [0, 562, 20, "DialerPage.getTabelCellByIndex"], [1, 1717, 35, "DialerPage.checkSelectedRecordList"], [2, 811, 13, ""], [2, 804, 9, ""], [2, 578, 5, ""]]], [1032, [[1, 1718, 42, "DialerPage.checkSelectedRecordList"], [2, 811, 13, ""], [2, 804, 9, ""], [2, 578, 5, ""]]], [1034, [[0, 319, 38, "DialerPage.wait_for_locator"], [0, 551, 20, "DialerPage.getTableItem"], [1, 1716, 33, "DialerPage.checkSelectedRecordList"], [2, 811, 13, ""], [2, 804, 9, ""], [2, 578, 5, ""]]], [1036, [[0, 319, 38, "DialerPage.wait_for_locator"], [0, 562, 20, "DialerPage.getTabelCellByIndex"], [1, 1717, 35, "DialerPage.checkSelectedRecordList"], [2, 811, 13, ""], [2, 804, 9, ""], [2, 578, 5, ""]]], [1038, [[1, 1718, 42, "DialerPage.checkSelectedRecordList"], [2, 811, 13, ""], [2, 804, 9, ""], [2, 578, 5, ""]]], [1040, [[0, 319, 38, "DialerPage.wait_for_locator"], [0, 551, 20, "DialerPage.getTableItem"], [1, 1716, 33, "DialerPage.checkSelectedRecordList"], [2, 811, 13, ""], [2, 804, 9, ""], [2, 578, 5, ""]]], [1042, [[0, 319, 38, "DialerPage.wait_for_locator"], [0, 562, 20, "DialerPage.getTabelCellByIndex"], [1, 1717, 35, "DialerPage.checkSelectedRecordList"], [2, 811, 13, ""], [2, 804, 9, ""], [2, 578, 5, ""]]], [1044, [[1, 1718, 42, "DialerPage.checkSelectedRecordList"], [2, 811, 13, ""], [2, 804, 9, ""], [2, 578, 5, ""]]], [1046, [[0, 103, 60, "DialerPage.saveImage"], [2, 812, 30, ""], [2, 804, 9, ""], [2, 578, 5, ""]]], [1048, [[0, 319, 38, "DialerPage.wait_for_locator"], [0, 551, 20, "DialerPage.getTableItem"], [1, 1729, 33, "DialerPage.removeSelectedRecordListByIndex"], [2, 820, 30, ""], [2, 815, 20, ""], [2, 578, 5, ""]]], [1050, [[0, 319, 38, "DialerPage.wait_for_locator"], [0, 562, 20, "DialerPage.getTabelCellByIndex"], [1, 1730, 34, "DialerPage.removeSelectedRecordListByIndex"], [2, 820, 13, ""], [2, 815, 9, ""], [2, 578, 5, ""]]], [1052, [[1, 1731, 44, "DialerPage.removeSelectedRecordListByIndex"], [2, 820, 13, ""], [2, 815, 9, ""], [2, 578, 5, ""]]], [1054, [[1, 1733, 20, "DialerPage.removeSelectedRecordListByIndex"], [2, 820, 13, ""], [2, 815, 9, ""], [2, 578, 5, ""]]], [1056, [[1, 1734, 46, "DialerPage.removeSelectedRecordListByIndex"], [2, 820, 13, ""], [2, 815, 9, ""], [2, 578, 5, ""]]], [1058, [[0, 319, 38, "DialerPage.wait_for_locator"], [0, 551, 20, "DialerPage.getTableItem"], [1, 1729, 33, "DialerPage.removeSelectedRecordListByIndex"], [2, 820, 13, ""], [2, 815, 9, ""], [2, 578, 5, ""]]], [1060, [[0, 319, 38, "DialerPage.wait_for_locator"], [0, 562, 20, "DialerPage.getTabelCellByIndex"], [1, 1730, 34, "DialerPage.removeSelectedRecordListByIndex"], [2, 820, 13, ""], [2, 815, 9, ""], [2, 578, 5, ""]]], [1062, [[1, 1731, 44, "DialerPage.removeSelectedRecordListByIndex"], [2, 820, 13, ""], [2, 815, 9, ""], [2, 578, 5, ""]]], [1064, [[1, 1733, 20, "DialerPage.removeSelectedRecordListByIndex"], [2, 820, 13, ""], [2, 815, 9, ""], [2, 578, 5, ""]]], [1066, [[1, 1734, 46, "DialerPage.removeSelectedRecordListByIndex"], [2, 820, 13, ""], [2, 815, 9, ""], [2, 578, 5, ""]]], [1068, [[0, 319, 38, "DialerPage.wait_for_locator"], [0, 551, 20, "DialerPage.getTableItem"], [1, 1729, 33, "DialerPage.removeSelectedRecordListByIndex"], [2, 820, 13, ""], [2, 815, 9, ""], [2, 578, 5, ""]]], [1070, [[0, 319, 38, "DialerPage.wait_for_locator"], [0, 562, 20, "DialerPage.getTabelCellByIndex"], [1, 1730, 34, "DialerPage.removeSelectedRecordListByIndex"], [2, 820, 13, ""], [2, 815, 9, ""], [2, 578, 5, ""]]], [1072, [[1, 1731, 44, "DialerPage.removeSelectedRecordListByIndex"], [2, 820, 13, ""], [2, 815, 9, ""], [2, 578, 5, ""]]], [1074, [[1, 1733, 20, "DialerPage.removeSelectedRecordListByIndex"], [2, 820, 13, ""], [2, 815, 9, ""], [2, 578, 5, ""]]], [1076, [[1, 1734, 46, "DialerPage.removeSelectedRecordListByIndex"], [2, 820, 13, ""], [2, 815, 9, ""], [2, 578, 5, ""]]], [1078, [[1, 1745, 47, "DialerPage.removeSelectedRecordListByIndex"], [2, 820, 13, ""], [2, 815, 9, ""], [2, 578, 5, ""]]], [1080, [[1, 1486, 14, "DialerPage.addRecordLists"], [2, 841, 30, ""], [2, 815, 9, ""], [2, 578, 5, ""]]], [1084, [[1, 1753, 35, "DialerPage.clickAddRecordsListButton"], [1, 1487, 15, "DialerPage.addRecordLists"], [2, 841, 30, ""], [2, 815, 9, ""], [2, 578, 5, ""]]], [1092, [[1, 1489, 32, "DialerPage.addRecordLists"], [2, 841, 13, ""], [2, 815, 9, ""], [2, 578, 5, ""]]], [1094, [[0, 319, 38, "DialerPage.wait_for_locator"], [1, 1492, 15, "DialerPage.addRecordLists"], [2, 841, 13, ""], [2, 815, 9, ""], [2, 578, 5, ""]]], [1096, [[0, 319, 38, "DialerPage.wait_for_locator"], [0, 392, 24, "DialerPage.wait_for_animated"], [1, 1493, 15, "DialerPage.addRecordLists"], [2, 841, 13, ""], [2, 815, 9, ""], [2, 578, 5, ""]]], [1098, [[0, 395, 40, "DialerPage.wait_for_animated"], [1, 1493, 4, "DialerPage.addRecordLists"], [2, 841, 13, ""], [2, 815, 9, ""], [2, 578, 5, ""]]], [1100, [[0, 356, 25, "DialerPage.wait_for_timeout"], [0, 396, 28, "DialerPage.wait_for_animated"], [1, 1493, 4, "DialerPage.addRecordLists"], [2, 841, 13, ""], [2, 815, 9, ""], [2, 578, 5, ""]]], [1102, [[1, 1495, 35, "DialerPage.addRecordLists"], [2, 841, 13, ""], [2, 815, 9, ""], [2, 578, 5, ""]]], [1104, [[1, 1772, 15, "DialerPage.clickRecordsListByIndex"], [1, 1497, 39, "DialerPage.addRecordLists"], [2, 841, 13, ""], [2, 815, 9, ""], [2, 578, 5, ""]]], [1108, [[1, 1773, 39, "DialerPage.clickRecordsListByIndex"], [1, 1497, 39, "DialerPage.addRecordLists"], [2, 841, 13, ""], [2, 815, 9, ""], [2, 578, 5, ""]]], [1116, [[1, 1775, 37, "DialerPage.clickRecordsListByIndex"], [1, 1497, 28, "DialerPage.addRecordLists"], [2, 841, 13, ""], [2, 815, 9, ""], [2, 578, 5, ""]]], [1118, [[0, 319, 38, "DialerPage.wait_for_locator"], [0, 551, 20, "DialerPage.getTableItem"], [0, 572, 38, "DialerPage.clickTabelCellByIndex"], [1, 1786, 14, "DialerPage.clickRecordsListByIndex"], [1, 1497, 28, "DialerPage.addRecordLists"], [2, 841, 13, ""], [2, 815, 9, ""], [2, 578, 5, ""]]]]}