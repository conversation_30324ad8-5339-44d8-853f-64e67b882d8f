#!/usr/bin/env node

// 直接测试 MCP 工具的脚本
import fs from 'fs';
import path from 'path';
import AdmZip from 'adm-zip';

// 导入我们的工具函数
import { extractTraceZip, createFilteredTrace, createFilteredNetworkTrace } from './dist/server.js';

// 创建测试用的 trace.zip 文件
function createTestTraceZip() {
  const testDir = './test-trace-temp';
  const zipPath = './test-trace.zip';
  
  // 如果已经存在，先删除
  if (fs.existsSync(zipPath)) {
    fs.unlinkSync(zipPath);
  }
  
  // 创建测试目录结构
  if (fs.existsSync(testDir)) {
    fs.rmSync(testDir, { recursive: true, force: true });
  }
  fs.mkdirSync(testDir, { recursive: true });
  
  // 创建模拟的 trace 文件
  const mockTrace = {
    type: 'trace',
    entries: [
      {
        type: 'action',
        action: 'goto',
        url: 'https://example.com',
        timestamp: Date.now()
      },
      {
        type: 'action', 
        action: 'click',
        selector: 'button#submit',
        timestamp: Date.now() + 1000
      },
      {
        type: 'console',
        messageType: 'log',
        text: 'Button clicked successfully',
        timestamp: Date.now() + 1500
      },
      {
        type: 'console',
        messageType: 'error', 
        text: 'Network request failed',
        timestamp: Date.now() + 2000
      }
    ]
  };
  
  fs.writeFileSync(
    path.join(testDir, '0-trace.trace'), 
    JSON.stringify(mockTrace, null, 2)
  );
  
  // 创建模拟的网络 trace 文件
  const mockNetwork = {
    type: 'network',
    entries: [
      {
        type: 'request',
        url: 'https://example.com',
        method: 'GET',
        status: 200,
        headers: [
          { name: 'Content-Type', value: 'text/html' },
          { name: 'Cache-Control', value: 'no-cache' }
        ],
        timestamp: Date.now()
      },
      {
        type: 'request',
        url: 'https://example.com/api/data',
        method: 'POST', 
        status: 500,
        headers: [
          { name: 'Content-Type', value: 'application/json' }
        ],
        timestamp: Date.now() + 1000
      },
      {
        type: 'request',
        url: 'https://analytics.google.com/track',
        method: 'GET',
        status: 200,
        headers: [
          { name: 'Content-Type', value: 'image/gif' }
        ],
        timestamp: Date.now() + 2000
      }
    ]
  };
  
  fs.writeFileSync(
    path.join(testDir, '0-trace.network'),
    JSON.stringify(mockNetwork, null, 2)
  );
  
  // 创建 resources 目录和模拟截图
  const resourcesDir = path.join(testDir, 'resources');
  fs.mkdirSync(resourcesDir, { recursive: true });
  
  // 创建模拟的截图文件（简单的文本文件模拟）
  fs.writeFileSync(
    path.join(resourcesDir, 'screenshot-1.png'),
    'Mock PNG screenshot data - this would be binary image data in real trace'
  );
  
  fs.writeFileSync(
    path.join(resourcesDir, 'screenshot-2.png'), 
    'Mock PNG screenshot data - another screenshot'
  );
  
  // 创建 zip 文件
  const zip = new AdmZip();
  zip.addLocalFolder(testDir);
  zip.writeZip(zipPath);
  
  // 清理临时目录
  fs.rmSync(testDir, { recursive: true, force: true });
  
  console.log(`✅ 创建了测试用的 trace.zip 文件: ${path.resolve(zipPath)}`);
  return path.resolve(zipPath);
}

// 测试 extractTraceZip 函数
async function testExtractTraceZip(zipPath) {
  console.log('\n🧪 测试 extractTraceZip 函数...');
  
  try {
    const result = extractTraceZip(zipPath);
    console.log(`✅ 成功解压到: ${result.outputDir}`);
    
    // 检查文件是否存在
    const traceFile = path.join(result.outputDir, '0-trace.trace');
    const networkFile = path.join(result.outputDir, '0-trace.network');
    const resourcesDir = path.join(result.outputDir, 'resources');
    
    if (fs.existsSync(traceFile)) {
      console.log('✅ trace 文件存在');
    } else {
      console.log('❌ trace 文件不存在');
    }
    
    if (fs.existsSync(networkFile)) {
      console.log('✅ network 文件存在');
    } else {
      console.log('❌ network 文件不存在');
    }
    
    if (fs.existsSync(resourcesDir)) {
      const screenshots = fs.readdirSync(resourcesDir);
      console.log(`✅ 找到 ${screenshots.length} 个截图文件: ${screenshots.join(', ')}`);
    } else {
      console.log('❌ resources 目录不存在');
    }
    
    return result.outputDir;
    
  } catch (error) {
    console.error('❌ extractTraceZip 测试失败:', error.message);
    return null;
  }
}

// 测试过滤功能
async function testFiltering(outputDir) {
  console.log('\n🧪 测试过滤功能...');
  
  try {
    const traceFile = path.join(outputDir, '0-trace.trace');
    const networkFile = path.join(outputDir, '0-trace.network');
    
    // 测试 trace 过滤
    if (fs.existsSync(traceFile)) {
      const filteredTrace = await createFilteredTrace(traceFile);
      console.log(`✅ 创建了过滤后的 trace 文件: ${path.basename(filteredTrace)}`);
    }
    
    // 测试网络过滤
    if (fs.existsSync(networkFile)) {
      const filteredNetwork = await createFilteredNetworkTrace(networkFile);
      console.log(`✅ 创建了过滤后的 network 文件: ${path.basename(filteredNetwork)}`);
    }
    
  } catch (error) {
    console.error('❌ 过滤功能测试失败:', error.message);
  }
}

// 模拟 MCP 工具调用
async function simulateMCPTools(zipPath) {
  console.log('\n🧪 模拟 MCP 工具调用...');
  
  try {
    // 模拟 get-trace 工具
    console.log('\n📋 模拟 get-trace 工具:');
    const { outputDir } = extractTraceZip(zipPath);
    const traceFile = path.join(outputDir, '0-trace.trace');
    
    if (fs.existsSync(traceFile)) {
      const traceContent = fs.readFileSync(traceFile, 'utf8');
      const traceData = JSON.parse(traceContent);
      console.log(`✅ 读取到 ${traceData.entries.length} 个 trace 条目`);
      console.log('📝 前两个条目:');
      traceData.entries.slice(0, 2).forEach((entry, i) => {
        console.log(`   ${i + 1}. ${entry.type}: ${entry.action || entry.messageType} - ${entry.url || entry.text}`);
      });
    }
    
    // 模拟 get-network-log 工具
    console.log('\n🌐 模拟 get-network-log 工具:');
    const networkFile = path.join(outputDir, '0-trace.network');
    
    if (fs.existsSync(networkFile)) {
      const networkContent = fs.readFileSync(networkFile, 'utf8');
      const networkData = JSON.parse(networkContent);
      console.log(`✅ 读取到 ${networkData.entries.length} 个网络请求`);
      console.log('📝 网络请求列表:');
      networkData.entries.forEach((entry, i) => {
        console.log(`   ${i + 1}. ${entry.method} ${entry.url} - ${entry.status}`);
      });
    }
    
    // 模拟 get-screenshots 工具
    console.log('\n📸 模拟 get-screenshots 工具:');
    const resourcesDir = path.join(outputDir, 'resources');
    
    if (fs.existsSync(resourcesDir)) {
      const files = fs.readdirSync(resourcesDir);
      const imageFiles = files.filter(file => /\.(jpe?g|png)$/i.test(file));
      console.log(`✅ 找到 ${imageFiles.length} 个截图文件:`);
      imageFiles.forEach((file, i) => {
        const filePath = path.join(resourcesDir, file);
        const stats = fs.statSync(filePath);
        console.log(`   ${i + 1}. ${file} (${stats.size} bytes)`);
      });
    }
    
  } catch (error) {
    console.error('❌ MCP 工具模拟失败:', error.message);
  }
}

// 主测试函数
async function runTests() {
  console.log('🚀 开始测试 Playwright Trace Analyzer MCP\n');
  
  // 1. 创建测试文件
  const zipPath = createTestTraceZip();
  
  // 2. 测试解压功能
  const outputDir = await testExtractTraceZip(zipPath);
  
  if (outputDir) {
    // 3. 测试过滤功能
    await testFiltering(outputDir);
    
    // 4. 模拟 MCP 工具调用
    await simulateMCPTools(zipPath);
  }
  
  console.log('\n✅ 所有测试完成！');
  console.log(`\n📁 测试文件位置: ${zipPath}`);
  console.log('\n💡 你现在可以在 Cursor 或其他 MCP 客户端中使用这个 trace.zip 文件来测试 MCP 工具了！');
}

// 运行测试
runTests().catch(console.error);
