import fs from "fs";

/**
 * 过滤选项接口 - 定义了所有可用的过滤规则
 * 这些选项控制着哪些类型的 trace 数据会被移除或保留
 */
interface FilterOptions {
  filterConsoleLogs: boolean;        // 是否过滤冗余的控制台日志（保留错误和警告）
  removeFrameSnapshots: boolean;     // 是否移除页面框架快照（DOM树，通常是最大的数据源）
  removeScreencastFrames: boolean;   // 是否移除屏幕录制帧（视频帧数据）
  removeUIElements: boolean;         // 是否移除重复的UI元素快照（按钮、输入框等）
  truncateStackTraces: boolean;      // 是否截断过长的堆栈跟踪信息
}

/**
 * 过滤统计信息接口 - 记录过滤过程中的各种统计数据
 * 用于分析过滤效果和生成报告
 */
interface FilterStats {
  removedByType: Record<string, number>;  // 按类型统计移除的条目数量（如：frame-snapshot: 1500）
  removedEntries: number;                 // 总共移除的条目数量
  sizeAfter: number;                      // 过滤后的文件大小（字节）
  sizeBefore: number;                     // 过滤前的文件大小（字节）
  totalEntries: number;                   // 总共处理的条目数量
}

/**
 * Trace 条目接口 - 表示 trace 文件中的单个记录
 * Playwright trace 文件是由多个 JSON 对象组成的，每个对象代表一个操作或事件
 */
interface TraceEntry {
  [key: string]: unknown;  // 允许任意属性，因为不同类型的 trace 条目有不同的字段
  type: string;            // 条目类型，如：action, console, frame-snapshot 等
}

/**
 * Playwright Trace 过滤器主类
 * 这是整个过滤系统的核心，负责读取、分析、过滤和写入 trace 文件
 */
export class PlaywrightTraceFilter {
  // 私有统计信息对象，记录过滤过程中的所有统计数据
  private stats: FilterStats = {
    removedByType: {},      // 初始化为空对象，后续会按类型记录移除数量
    removedEntries: 0,      // 初始移除数量为0
    sizeAfter: 0,          // 初始过滤后大小为0
    sizeBefore: 0,         // 初始过滤前大小为0
    totalEntries: 0,       // 初始总条目数为0
  };

  /**
   * 主要的过滤方法 - 读取输入文件，应用过滤规则，写入输出文件
   * @param inputPath 输入的 trace 文件路径
   * @param outputPath 输出的过滤后文件路径
   * @param filterOptions 过滤选项配置
   * @returns 返回过滤统计信息
   */
  public async filterTraceFile(
    inputPath: string,
    outputPath: string,
    filterOptions: FilterOptions
  ): Promise<FilterStats> {
    // 重置统计信息 - 确保每次过滤都从干净的状态开始
    this.stats = {
      removedByType: {},
      removedEntries: 0,
      sizeAfter: 0,
      sizeBefore: 0,
      totalEntries: 0,
    };

    // 记录原始文件大小 - 用于计算压缩比例
    this.stats.sizeBefore = fs.statSync(inputPath).size;

    // 存储过滤后保留的条目
    const filteredEntries: TraceEntry[] = [];

    try {
      // 读取整个 trace 文件内容
      const content = fs.readFileSync(inputPath, "utf8");
      // 按行分割，因为 trace 文件是每行一个 JSON 对象的格式
      const lines = content.split("\n");

      // 逐行处理每个 trace 条目
      for (const [lineNum, line] of lines.entries()) {
        const trimmedLine = line.trim();
        // 跳过空行
        if (!trimmedLine) {
          continue;
        }

        try {
          // 尝试解析 JSON - 每行都应该是一个有效的 JSON 对象
          const entry: TraceEntry = JSON.parse(trimmedLine);
          this.stats.totalEntries += 1;  // 增加总条目计数

          // 检查这个条目是否应该被移除
          // 如果不应该移除，则添加到过滤后的条目列表中
          if (!this.shouldRemoveEntry(entry, filterOptions)) {
            filteredEntries.push(entry);
          }
        } catch (error) {
          // JSON 解析错误处理 - 保留格式错误的条目以避免数据丢失
          // 这种情况很少见，但可能发生在文件损坏或格式不正确时
          filteredEntries.push({
            error: String(error),      // 记录错误信息
            line: trimmedLine,         // 保留原始行内容
            type: "malformed",         // 标记为格式错误类型
          });
        }
      }
    } catch (error) {
      // 文件读取错误 - 抛出更具描述性的错误
      throw new Error(`Error reading file: ${error}`);
    }

    // 写入过滤后的 trace 文件
    try {
      // 将过滤后的条目转换回 JSON 字符串格式
      // 使用 null, 0 参数确保紧凑格式（不添加额外空格）
      const filteredContent = filteredEntries
        .map((entry) => JSON.stringify(entry, null, 0))
        .join("\n");  // 每个条目占一行

      // 写入文件，末尾添加换行符
      fs.writeFileSync(outputPath, filteredContent + "\n", "utf8");
    } catch (error) {
      // 文件写入错误处理
      throw new Error(`Error writing file: ${error}`);
    }

    // 记录过滤后的文件大小
    this.stats.sizeAfter = fs.statSync(outputPath).size;

    // 返回完整的统计信息
    return this.stats;
  }

  /**
   * 格式化文件大小为人类可读的格式
   * 将字节数转换为 B, KB, MB, GB 等单位
   * @param sizeBytes 文件大小（字节）
   * @returns 格式化后的大小字符串，如 "1.5 MB"
   */
  private formatSize(sizeBytes: number): string {
    const units = ["B", "KB", "MB", "GB"];  // 支持的单位
    let size = sizeBytes;
    let unitIndex = 0;

    // 循环除以1024直到找到合适的单位
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    // 返回保留一位小数的格式化字符串
    return `${size.toFixed(1)} ${units[unitIndex]}`;
  }

  /**
   * 检查控制台日志是否包含重要信息
   * 这是智能过滤的关键 - 不是简单地移除所有日志，而是保留重要的
   * @param entry trace 条目
   * @returns true 如果日志包含重要信息，false 如果是普通日志
   */
  private isImportantConsoleLog(entry: TraceEntry): boolean {
    // 获取日志文本内容并转为小写以便匹配
    const text = String(entry.text || "").toLowerCase();

    // 定义重要关键词列表 - 这些关键词表明日志可能包含调试信息
    const importantKeywords = [
      "error",        // 错误信息
      "warning",      // 警告信息
      "failed",       // 失败信息
      "exception",    // 异常信息
      "uncaught",     // 未捕获的错误
      "test",         // 测试相关
      "assertion",    // 断言相关
      "timeout",      // 超时相关
      "network",      // 网络相关
      "xhr",          // AJAX 请求
      "fetch",        // Fetch API 请求
    ];

    // 检查日志文本是否包含任何重要关键词
    return importantKeywords.some((keyword) => text.includes(keyword));
  }

  /**
   * 打印过滤摘要信息 - 显示详细的过滤统计
   * 这个方法在 MCP 环境中被禁用，但在开发调试时很有用
   */
  private printSummary(): void {
    // 计算文件大小减少量和减少百分比
    const sizeReduction = this.stats.sizeBefore - this.stats.sizeAfter;
    const sizeReductionPct =
      this.stats.sizeBefore > 0
        ? (sizeReduction / this.stats.sizeBefore) * 100
        : 0;

    // 打印格式化的统计报告
    console.log("\n" + "=".repeat(60));
    console.log("📊 FILTERING SUMMARY");
    console.log("=".repeat(60));
    console.log(
      `📋 Total entries processed: ${this.stats.totalEntries.toLocaleString()}`
    );
    console.log(
      `🗑️  Entries removed: ${this.stats.removedEntries.toLocaleString()}`
    );
    console.log(
      `✅ Entries kept: ${(this.stats.totalEntries - this.stats.removedEntries).toLocaleString()}`
    );
    console.log(
      `📉 Removal rate: ${((this.stats.removedEntries / this.stats.totalEntries) * 100).toFixed(1)}%`
    );
    console.log();
    console.log(`📦 Original size: ${this.formatSize(this.stats.sizeBefore)}`);
    console.log(`📦 Filtered size: ${this.formatSize(this.stats.sizeAfter)}`);
    console.log(
      `💾 Size reduction: ${this.formatSize(sizeReduction)} (${sizeReductionPct.toFixed(1)}%)`
    );
    console.log();

    // 如果有按类型的移除统计，则显示详细信息
    if (Object.keys(this.stats.removedByType).length > 0) {
      console.log("🏷️  Removed by type:");
      for (const [removalType, count] of Object.entries(
        this.stats.removedByType
      ).sort()) {
        console.log(`   • ${removalType}: ${count.toLocaleString()} entries`);
      }
    }
    console.log("=".repeat(60));
  }

  /**
   * 核心过滤逻辑 - 判断一个 trace 条目是否应该被移除
   * 这是整个过滤系统最重要的方法，包含了所有的过滤规则
   * @param entry 要检查的 trace 条目
   * @param filterOptions 过滤选项配置
   * @returns true 如果应该移除，false 如果应该保留
   */
  private shouldRemoveEntry(
    entry: TraceEntry,
    filterOptions: FilterOptions
  ): boolean {
    const entryType = entry.type || "";  // 获取条目类型

    // 规则1: 移除页面框架快照 (通常是最大的数据源)
    // frame-snapshot 包含完整的 HTML DOM 树，占用大量空间但调试价值有限
    if (filterOptions.removeFrameSnapshots && entryType === "frame-snapshot") {
      this.updateRemovalStats("frame-snapshot");  // 更新统计
      return true;  // 标记为移除
    }

    // 规则2: 移除屏幕录制帧 (视频帧数据)
    // screencast-frame 是录制过程中的视频帧，频率很高，占用空间大
    if (
      filterOptions.removeScreencastFrames &&
      entryType === "screencast-frame"
    ) {
      this.updateRemovalStats("screencast-frame");
      return true;
    }

    // 规则3: 智能过滤控制台日志 - 保留重要信息，移除冗余日志
    if (filterOptions.filterConsoleLogs && entryType === "console") {
      const messageType = String(entry.messageType || "");

      // 只移除 info 和 log 级别的消息，且不包含重要关键词的
      // 始终保留 error, warn 等重要级别的消息
      if (
        ["info", "log"].includes(messageType) &&
        !this.isImportantConsoleLog(entry)
      ) {
        this.updateRemovalStats("console-verbose");
        return true;
      }
    }

    // 规则4: 移除重复的UI元素快照
    // 这些元素的状态变化记录通常很频繁但调试价值有限
    if (
      filterOptions.removeUIElements &&
      ["button", "checkbox", "input", "text"].includes(entryType)
    ) {
      this.updateRemovalStats("ui-elements");
      return true;
    }

    // 规则5: 截断过长的堆栈跟踪 - 不完全移除，而是智能截断
    if (filterOptions.truncateStackTraces && entryType === "console") {
      if (
        entry.text &&
        typeof entry.text === "string" &&
        entry.text.length > 5000  // 超过5000字符的堆栈跟踪
      ) {
        const text = entry.text;
        // 检查是否是典型的堆栈跟踪格式
        if (text.includes("\n    at ")) {
          const lines = text.split("\n");
          if (lines.length > 20) {
            // 保留前10行和后5行，中间用省略号代替
            // 这样既减少了大小，又保留了最重要的信息
            entry.text = [
              ...lines.slice(0, 10),           // 前10行（通常是错误信息和最近的调用栈）
              "    ... [truncated stack trace] ...",  // 省略标记
              ...lines.slice(-5),              // 后5行（通常是最底层的调用栈）
            ].join("\n");
          }
        }
      }
    }

    // 如果没有匹配任何移除规则，则保留这个条目
    return false;
  }

  /**
   * 更新移除统计信息
   * 记录被移除的条目数量，按类型分类统计
   * @param removalType 移除类型（如 frame-snapshot, console-verbose 等）
   */
  private updateRemovalStats(removalType: string): void {
    this.stats.removedEntries += 1;  // 增加总移除数量

    // 如果这个移除类型还没有记录，初始化为0
    if (!this.stats.removedByType[removalType]) {
      this.stats.removedByType[removalType] = 0;
    }

    // 增加这个类型的移除数量
    this.stats.removedByType[removalType] += 1;
  }
}

/**
 * 创建预设的过滤配置
 * 提供三种不同强度的过滤预设，适应不同的使用场景
 * @returns 包含所有预设配置的对象
 */
export function createFilterPresets(): Record<string, FilterOptions> {
  return {
    // 保守模式 - 最小程度的过滤，主要移除最占空间的数据
    conservative: {
      filterConsoleLogs: false,        // 不过滤控制台日志，保留所有日志信息
      removeFrameSnapshots: true,      // 移除DOM快照（这是最大的空间节省）
      removeScreencastFrames: false,   // 保留视频帧（可能对调试有用）
      removeUIElements: false,         // 保留UI元素快照
      truncateStackTraces: true,       // 截断长堆栈跟踪（安全的优化）
    },

    // 最小模式 - 最激进的过滤，最大程度减少文件大小
    minimal: {
      filterConsoleLogs: true,         // 过滤冗余控制台日志
      removeFrameSnapshots: true,      // 移除DOM快照
      removeScreencastFrames: true,    // 移除视频帧
      removeUIElements: true,          // 移除UI元素快照
      truncateStackTraces: true,       // 截断长堆栈跟踪
    },

    // 中等模式 - 平衡过滤强度和信息保留
    moderate: {
      filterConsoleLogs: false,        // 保留控制台日志（可能包含重要调试信息）
      removeFrameSnapshots: true,      // 移除DOM快照
      removeScreencastFrames: true,    // 移除视频帧
      removeUIElements: true,          // 移除UI元素快照
      truncateStackTraces: true,       // 截断长堆栈跟踪
    },
  };
}

/**
 * 便捷函数 - 使用预设配置过滤 trace 文件
 * 这是最常用的接口，大多数用户会使用这个函数而不是直接使用类
 * @param inputPath 输入文件路径
 * @param outputPath 输出文件路径
 * @param preset 预设名称，默认为 "minimal"
 * @returns 过滤统计信息
 */
export async function filterTraceWithPreset(
  inputPath: string,
  outputPath: string,
  preset: "conservative" | "minimal" | "moderate" = "minimal"
): Promise<FilterStats> {
  // 获取所有预设配置
  const presets = createFilterPresets();
  const filterOptions = presets[preset];

  // 验证预设名称是否有效
  if (!filterOptions) {
    throw new Error(`Unknown preset: ${preset}`);
  }

  // 创建过滤器实例并执行过滤
  const filter = new PlaywrightTraceFilter();
  return filter.filterTraceFile(inputPath, outputPath, filterOptions);
}
