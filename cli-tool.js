#!/usr/bin/env node

// 命令行工具，可以直接使用 trace analyzer 功能
import fs from 'fs';
import path from 'path';
import { extractTraceZip, createFilteredTrace, createFilteredNetworkTrace } from './dist/server.js';

// 解析命令行参数
const args = process.argv.slice(2);
const command = args[0];
const traceZipPath = args[1];
const options = {};

// 解析选项
for (let i = 2; i < args.length; i++) {
  if (args[i] === '--raw') {
    options.raw = true;
  }
}

if (!command || !traceZipPath) {
  console.log(`
🎭 Playwright Trace Analyzer CLI

用法:
  node cli-tool.js <command> <trace.zip路径> [选项]

命令:
  trace       - 分析 trace 文件
  network     - 分析网络日志
  screenshots - 提取截图
  all         - 执行所有分析

选项:
  --raw       - 显示原始未过滤的数据

示例:
  node cli-tool.js trace ./test-trace.zip
  node cli-tool.js network ./test-trace.zip --raw
  node cli-tool.js screenshots ./test-trace.zip
  node cli-tool.js all ./test-trace.zip
`);
  process.exit(1);
}

// 检查文件是否存在
if (!fs.existsSync(traceZipPath)) {
  console.error(`❌ 错误: 找不到文件 ${traceZipPath}`);
  process.exit(1);
}

console.log(`🎭 分析 trace 文件: ${traceZipPath}\n`);

try {
  // 解压 trace 文件
  const { outputDir } = extractTraceZip(traceZipPath);
  console.log(`📁 解压到: ${outputDir}\n`);

  // 执行相应的命令
  switch (command) {
    case 'trace':
      await analyzeTrace(outputDir, options);
      break;
    case 'network':
      await analyzeNetwork(outputDir, options);
      break;
    case 'screenshots':
      await analyzeScreenshots(outputDir);
      break;
    case 'all':
      await analyzeTrace(outputDir, options);
      console.log('\n' + '='.repeat(50) + '\n');
      await analyzeNetwork(outputDir, options);
      console.log('\n' + '='.repeat(50) + '\n');
      await analyzeScreenshots(outputDir);
      break;
    default:
      console.error(`❌ 未知命令: ${command}`);
      process.exit(1);
  }

} catch (error) {
  console.error(`❌ 错误: ${error.message}`);
  process.exit(1);
}

// 分析 trace 文件
async function analyzeTrace(outputDir, options) {
  console.log('📋 分析 Trace 文件...\n');
  
  const originalTraceFilePath = `${outputDir}/0-trace.trace`;
  
  if (!fs.existsSync(originalTraceFilePath)) {
    console.log('❌ 未找到 trace 文件');
    return;
  }

  try {
    let traceContent;
    let filePath;
    
    if (options.raw) {
      // 显示原始 trace
      traceContent = fs.readFileSync(originalTraceFilePath, 'utf8');
      filePath = originalTraceFilePath;
      console.log('📄 原始 trace 内容:\n');
    } else {
      // 显示过滤后的 trace
      const filteredTraceFilePath = await createFilteredTrace(originalTraceFilePath);
      traceContent = fs.readFileSync(filteredTraceFilePath, 'utf8');
      filePath = filteredTraceFilePath;
      console.log('📄 过滤后的 trace 内容:\n');
    }
    
    // 显示文件大小信息
    const stats = fs.statSync(filePath);
    console.log(`📊 文件大小: ${(stats.size / 1024).toFixed(2)} KB\n`);
    
    // 显示内容（截取前1000字符）
    if (traceContent.length > 1000) {
      console.log(traceContent.substring(0, 1000) + '\n...(内容已截取)');
    } else {
      console.log(traceContent);
    }
    
  } catch (error) {
    console.error(`❌ 读取 trace 文件失败: ${error.message}`);
  }
}

// 分析网络日志
async function analyzeNetwork(outputDir, options) {
  console.log('🌐 分析网络日志...\n');
  
  const originalNetworkFilePath = `${outputDir}/0-trace.network`;
  
  if (!fs.existsSync(originalNetworkFilePath)) {
    console.log('❌ 未找到网络日志文件');
    return;
  }

  try {
    let networkContent;
    let filePath;
    
    if (options.raw) {
      // 显示原始网络日志
      networkContent = fs.readFileSync(originalNetworkFilePath, 'utf8');
      filePath = originalNetworkFilePath;
      console.log('📄 原始网络日志:\n');
    } else {
      // 显示过滤后的网络日志
      const filteredNetworkFilePath = await createFilteredNetworkTrace(originalNetworkFilePath);
      networkContent = fs.readFileSync(filteredNetworkFilePath, 'utf8');
      filePath = filteredNetworkFilePath;
      console.log('📄 过滤后的网络日志 (已移除分析和第三方服务):\n');
    }
    
    // 显示文件大小信息
    const stats = fs.statSync(filePath);
    console.log(`📊 文件大小: ${(stats.size / 1024).toFixed(2)} KB\n`);
    
    // 显示内容（截取前1000字符）
    if (networkContent.length > 1000) {
      console.log(networkContent.substring(0, 1000) + '\n...(内容已截取)');
    } else {
      console.log(networkContent);
    }
    
  } catch (error) {
    console.error(`❌ 读取网络日志失败: ${error.message}`);
  }
}

// 分析截图
async function analyzeScreenshots(outputDir) {
  console.log('📸 分析截图文件...\n');
  
  const resourcesDir = `${outputDir}/resources`;
  
  if (!fs.existsSync(resourcesDir)) {
    console.log('❌ 未找到 resources 目录');
    return;
  }

  try {
    const files = fs.readdirSync(resourcesDir);
    const imageFiles = files.filter(file => /\.(jpe?g|png)$/i.test(file));

    if (imageFiles.length === 0) {
      console.log('❌ 未找到截图文件');
      return;
    }
    
    console.log(`✅ 找到 ${imageFiles.length} 个截图文件:\n`);
    
    imageFiles.forEach((file, i) => {
      const fullPath = `${resourcesDir}/${file}`;
      const stats = fs.statSync(fullPath);
      console.log(`${i + 1}. ${file}`);
      console.log(`   📁 路径: ${fullPath}`);
      console.log(`   📊 大小: ${(stats.size / 1024).toFixed(2)} KB`);
      console.log(`   📅 修改时间: ${stats.mtime.toLocaleString()}\n`);
    });
    
  } catch (error) {
    console.error(`❌ 读取截图失败: ${error.message}`);
  }
}
