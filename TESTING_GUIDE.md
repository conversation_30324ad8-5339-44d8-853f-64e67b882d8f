# Playwright Trace Analyzer MCP - 测试指南

## 🚀 快速测试方法

### 方法1: 使用内置测试脚本（推荐）

1. **运行测试脚本**：
   ```bash
   node test-mcp-tools.js
   ```
   
   这个脚本会：
   - 创建一个模拟的 trace.zip 文件
   - 测试所有核心功能
   - 显示详细的测试结果

2. **查看测试结果**：
   脚本会创建 `test-trace.zip` 文件，你可以用这个文件来测试 MCP 工具。

### 方法2: 在 Cursor 中测试

1. **确保 MCP 配置正确**：
   检查 `.cursor/mcp.json` 文件：
   ```json
   {
     "mcpServers": {
       "playwright-trace-analyzer": {
         "command": "npx",
         "args": [
           "tsx",
           "/path/to/your/project/src/server.ts"
         ]
       }
     }
   }
   ```

2. **重启 Cursor** 以加载 MCP 配置

3. **使用 MCP 工具**：
   - 在 Cursor 中，你应该能看到三个可用的工具：
     - `get-trace`
     - `get-network-log` 
     - `get-screenshots`

4. **测试工具**：
   使用测试脚本生成的 trace.zip 文件路径：
   ```
   /path/to/your/project/test-trace.zip
   ```

### 方法3: 使用 FastMCP 开发模式

1. **启动开发模式**：
   ```bash
   npm run dev
   ```

2. **选择工具进行测试**：
   - 使用箭头键选择工具
   - 输入 trace.zip 文件路径
   - 查看结果

## 📋 测试用例

### 测试 get-trace 工具

**输入参数**：
```json
{
  "traceZipPath": "/path/to/test-trace.zip",
  "raw": false
}
```

**期望输出**：
- 显示过滤后的 trace 内容
- 包含执行步骤和控制台日志
- 文件大小显著减少

### 测试 get-network-log 工具

**输入参数**：
```json
{
  "traceZipPath": "/path/to/test-trace.zip", 
  "raw": false
}
```

**期望输出**：
- 显示过滤后的网络请求
- 移除分析和第三方服务请求
- 保留应用相关的网络活动

### 测试 get-screenshots 工具

**输入参数**：
```json
{
  "traceZipPath": "/path/to/test-trace.zip"
}
```

**期望输出**：
- 显示所有截图文件列表
- 包含文件名和大小信息

## 🔧 故障排除

### 常见问题

1. **"Trace file not found" 错误**：
   - 检查 trace.zip 文件路径是否正确
   - 确保文件存在且可读

2. **"Resources directory not found" 错误**：
   - 这是正常的，如果 trace 文件中没有截图
   - 真实的 Playwright trace 文件通常包含截图

3. **JSON 解析警告**：
   - 这些警告是正常的，因为过滤器期望真实的 Playwright trace 格式
   - 不影响核心功能

### 使用真实的 Playwright trace 文件

如果你有真实的 Playwright trace.zip 文件：

1. **找到 trace 文件**：
   通常在 `test-results` 目录下：
   ```
   test-results/
   ├── test-name-chromium/
   │   └── trace.zip
   ```

2. **使用完整路径**：
   ```
   /absolute/path/to/test-results/test-name-chromium/trace.zip
   ```

## 📊 性能验证

测试时注意以下指标：

1. **文件大小减少**：
   - 过滤后的 trace 应该减少 80-95%
   - 过滤后的 network 应该减少 80%+

2. **处理速度**：
   - 小文件（<10MB）应该在几秒内处理完成
   - 大文件可能需要更长时间

3. **内容保留**：
   - 重要的调试信息应该保留
   - 错误和关键日志应该可见

## 🎯 成功标准

测试成功的标志：

- ✅ 所有三个工具都能正常调用
- ✅ 能够成功解压和分析 trace.zip 文件
- ✅ 过滤功能正常工作，文件大小显著减少
- ✅ 输出内容格式正确，包含有用的调试信息
- ✅ 错误处理正常，给出清晰的错误信息

## 📝 测试报告模板

测试完成后，可以使用以下模板记录结果：

```
测试日期: [日期]
测试环境: [操作系统/Node.js版本]

✅ get-trace 工具: [通过/失败]
✅ get-network-log 工具: [通过/失败] 
✅ get-screenshots 工具: [通过/失败]

性能指标:
- Trace 文件大小减少: [百分比]
- Network 文件大小减少: [百分比]
- 处理时间: [秒数]

问题记录:
[记录遇到的任何问题]
```
