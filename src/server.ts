#!/usr/bin/env node
import AdmZip from "adm-zip";
import { Content, FastMCP } from "fastmcp";
import fs from "fs";
import path from "path";
import { z } from "zod";

import { filterNetworkTraceWithPreset } from "./networkTraceFilter.js";
import { filterTraceWithPreset } from "./traceFilter.js";

// 日志函数
function logError(context: string, message: any, isError: boolean = true): string {
  const timestamp = new Date().toISOString();
  const messageStr = message instanceof Error ? message.message : String(message);
  const stack = message instanceof Error ? message.stack : undefined;

  const logMessage = `[${timestamp}] ${isError ? 'ERROR' : 'INFO'} in ${context}: ${messageStr}`;

  // 在开发环境下输出到 stderr（不会干扰 MCP 协议）
  if (process.env.NODE_ENV === 'development') {
    if (isError) {
      console.error(logMessage);
      if (stack) console.error('Stack trace:', stack);
    } else {
      console.error(`[INFO] ${logMessage}`); // 使用 stderr 避免干扰 MCP
    }
  }

  if (isError) {
    return `❌ Error in ${context}: ${messageStr}`;
  } else {
    return messageStr;
  }
}

// 便捷的信息日志函数
function logInfo(context: string, message: string): void {
  logError(context, message, false);
}

// 限制返回内容的最大长度（字符数）
const MAX_CONTENT_LENGTH = 50000;

/**
 * 智能分析内容，提取关键信息而不是简单截断
 */
function analyzeTraceContent(content: string, type: 'trace' | 'network'): string {
  if (content.length <= MAX_CONTENT_LENGTH) {
    return content;
  }

  const lines = content.split('\n').filter(line => line.trim());

  if (type === 'trace') {
    return analyzeTraceLines(lines);
  } else {
    return analyzeNetworkLines(lines);
  }
}

/**
 * 分析 trace 内容，提取关键信息
 */
function analyzeTraceLines(lines: string[]): string {
  const entries = lines.map(line => {
    try {
      return JSON.parse(line);
    } catch {
      return null;
    }
  }).filter(Boolean);

  const actions = entries.filter(entry => entry.type === 'action');
  const consoleEntries = entries.filter(entry => entry.type === 'console');
  const errors = consoleEntries.filter(entry => entry.messageType === 'error');
  const warnings = consoleEntries.filter(entry => entry.messageType === 'warning');

  let summary = `📊 TRACE SUMMARY\n`;
  summary += `Total entries: ${entries.length}\n`;
  summary += `Actions: ${actions.length}\n`;
  summary += `Console messages: ${consoleEntries.length}\n`;
  summary += `Errors: ${errors.length}\n`;
  summary += `Warnings: ${warnings.length}\n\n`;

  // 显示所有错误（最重要）
  if (errors.length > 0) {
    summary += `🚨 ERRORS (${errors.length}):\n`;
    errors.forEach((error, i) => {
      summary += `${i + 1}. ${error.text}\n`;
    });
    summary += '\n';
  }

  // 显示所有警告
  if (warnings.length > 0) {
    summary += `⚠️ WARNINGS (${warnings.length}):\n`;
    warnings.forEach((warning, i) => {
      summary += `${i + 1}. ${warning.text}\n`;
    });
    summary += '\n';
  }

  // 显示关键动作序列
  if (actions.length > 0) {
    summary += `🎬 ACTION SEQUENCE (${actions.length}):\n`;
    actions.forEach((action, i) => {
      summary += `${i + 1}. ${action.action}`;
      if (action.url) summary += ` → ${action.url}`;
      if (action.selector) summary += ` (${action.selector})`;
      if (action.value) summary += ` = "${action.value}"`;
      summary += '\n';
    });
    summary += '\n';
  }

  // 如果还有空间，显示部分原始内容
  if (summary.length < MAX_CONTENT_LENGTH * 0.7) {
    const remainingSpace = MAX_CONTENT_LENGTH - summary.length - 200;
    summary += `📝 RECENT ENTRIES:\n`;
    summary += lines.slice(-Math.min(10, Math.floor(remainingSpace / 100))).join('\n');
  }

  return summary;
}

/**
 * 分析网络内容，提取关键信息
 */
function analyzeNetworkLines(lines: string[]): string {
  const entries = lines.map(line => {
    try {
      return JSON.parse(line);
    } catch {
      return null;
    }
  }).filter(Boolean);

  const requests = entries.filter(entry => entry.snapshot?.request);
  const failedRequests = requests.filter(entry => {
    const status = entry.snapshot?.response?.status;
    return status && (status >= 400 || status === 0);
  });

  const slowRequests = requests.filter(entry => {
    const timings = entry.snapshot?.timings;
    return timings && timings.total > 1000; // > 1秒
  });

  let summary = `🌐 NETWORK SUMMARY\n`;
  summary += `Total requests: ${requests.length}\n`;
  summary += `Failed requests: ${failedRequests.length}\n`;
  summary += `Slow requests (>1s): ${slowRequests.length}\n\n`;

  // 显示所有失败请求
  if (failedRequests.length > 0) {
    summary += `❌ FAILED REQUESTS (${failedRequests.length}):\n`;
    failedRequests.forEach((req, i) => {
      const url = req.snapshot?.request?.url || 'Unknown URL';
      const status = req.snapshot?.response?.status || 'Unknown';
      const method = req.snapshot?.request?.method || 'Unknown';
      summary += `${i + 1}. ${method} ${url} → ${status}\n`;
    });
    summary += '\n';
  }

  // 显示慢请求
  if (slowRequests.length > 0) {
    summary += `🐌 SLOW REQUESTS (${slowRequests.length}):\n`;
    slowRequests.forEach((req, i) => {
      const url = req.snapshot?.request?.url || 'Unknown URL';
      const time = req.snapshot?.timings?.total || 0;
      const method = req.snapshot?.request?.method || 'Unknown';
      summary += `${i + 1}. ${method} ${url} → ${time}ms\n`;
    });
    summary += '\n';
  }

  // 按域名分组显示请求统计
  const domainStats = new Map<string, number>();
  requests.forEach(req => {
    try {
      const url = new URL(req.snapshot?.request?.url || '');
      const domain = url.hostname;
      domainStats.set(domain, (domainStats.get(domain) || 0) + 1);
    } catch {
      // 忽略无效URL
    }
  });

  if (domainStats.size > 0) {
    summary += `📊 REQUESTS BY DOMAIN:\n`;
    Array.from(domainStats.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .forEach(([domain, count]) => {
        summary += `${domain}: ${count} requests\n`;
      });
  }

  return summary;
}

/**
 * 验证 trace 文件的有效性
 */
function validateTraceFile(traceZipPath: string): void {
  if (!fs.existsSync(traceZipPath)) {
    throw new Error(`Trace file not found: ${traceZipPath}`);
  }

  const stats = fs.statSync(traceZipPath);
  if (!stats.isFile()) {
    throw new Error(`Path is not a file: ${traceZipPath}`);
  }

  if (stats.size === 0) {
    throw new Error(`Trace file is empty: ${traceZipPath}`);
  }

  if (!traceZipPath.toLowerCase().endsWith('.zip')) {
    throw new Error(`File must be a .zip file: ${traceZipPath}`);
  }
}

/**
 * Creates a filtered version of a network trace file to reduce size and remove bloated data
 * @param networkTraceFilePath The path to the original network trace file
 * @returns The path to the filtered network trace file
 */
async function createFilteredNetworkTrace(
  networkTraceFilePath: string
): Promise<string> {
  const filteredPath = networkTraceFilePath.replace(
    ".network",
    "_filtered.network"
  );

  // Check if filtered network trace already exists and is newer than original
  if (fs.existsSync(filteredPath)) {
    const originalStat = fs.statSync(networkTraceFilePath);
    const filteredStat = fs.statSync(filteredPath);
    if (filteredStat.mtime > originalStat.mtime) {
      return filteredPath;
    }
  }

  try {
    // Use the TypeScript network filtering function with minimal preset
    await filterNetworkTraceWithPreset(
      networkTraceFilePath,
      filteredPath,
      "minimal"
    );
    // console.log(`Created filtered network trace: ${filteredPath}`);
    return filteredPath;
  } catch (error) {
    // console.warn(`Error filtering network trace: ${error}`);
    return networkTraceFilePath;
  }
}

/**
 * Creates a filtered version of a trace file to reduce size and remove bloated data
 * @param traceFilePath The path to the original trace file
 * @returns The path to the filtered trace file
 */
async function createFilteredTrace(traceFilePath: string): Promise<string> {
  const filteredPath = traceFilePath.replace(".trace", "_filtered.trace");

  // Check if filtered trace already exists and is newer than original
  if (fs.existsSync(filteredPath)) {
    const originalStat = fs.statSync(traceFilePath);
    const filteredStat = fs.statSync(filteredPath);
    if (filteredStat.mtime > originalStat.mtime) {
      return filteredPath;
    }
  }

  try {
    // Use the TypeScript filtering function
    await filterTraceWithPreset(traceFilePath, filteredPath, "minimal");
    // console.log(`Created filtered trace: ${filteredPath}`);
    return filteredPath;
  } catch (error) {
    // console.warn(`Error filtering trace: ${error}`);
    return traceFilePath;
  }
}

// 缓存已解压的目录，避免重复解压
const extractedDirCache = new Map<string, string>();
// 缓存过滤处理状态，避免重复过滤
const filteringCache = new Map<string, boolean>();

/**
 * Extracts trace.zip file to a directory (with caching)
 * @param traceZipPath The path to the trace.zip file
 * @returns An object with the output directory path
 */
async function extractTraceZip(traceZipPath: string): Promise<{
  outputDir: string;
}> {
  // 验证文件
  validateTraceFile(traceZipPath);

  // 检查缓存
  const absolutePath = path.resolve(traceZipPath);
  if (extractedDirCache.has(absolutePath)) {
    const cachedDir = extractedDirCache.get(absolutePath)!;
    // 验证缓存的目录仍然存在且有效
    if (fs.existsSync(cachedDir) && fs.existsSync(path.join(cachedDir, '0-trace.trace'))) {
      logInfo('extractTraceZip-cache-hit', `Using cached extraction: ${cachedDir}`);
      return { outputDir: cachedDir };
    } else {
      // 缓存失效，清除
      extractedDirCache.delete(absolutePath);
      filteringCache.delete(absolutePath);
    }
  }

  // 创建基于文件内容哈希的目录名（更稳定）
  const zipBaseName = path.basename(traceZipPath, '.zip');
  const zipStats = fs.statSync(traceZipPath);
  const dirHash = `${zipStats.size}_${zipStats.mtime.getTime()}`;
  const outputDir = path.join(path.dirname(traceZipPath), `${zipBaseName}_extracted_${dirHash}`);

  // 如果目录已存在且有效，直接使用
  if (fs.existsSync(outputDir) && fs.existsSync(path.join(outputDir, '0-trace.trace'))) {
    extractedDirCache.set(absolutePath, outputDir);
    logInfo('extractTraceZip-dir-exists', `Using existing extraction: ${outputDir}`);

    // 检查是否需要创建过滤文件（只在第一次或缓存失效时执行）
    if (!filteringCache.has(absolutePath)) {
      await ensureFilteredFiles(outputDir, absolutePath);
    }

    return { outputDir };
  }

  logInfo('extractTraceZip-start', `Extracting ${traceZipPath} to ${outputDir}`);

  // 解压文件
  const zip = new AdmZip(traceZipPath);
  zip.extractAllTo(outputDir, true);

  // 缓存结果
  extractedDirCache.set(absolutePath, outputDir);

  // 创建过滤文件
  await ensureFilteredFiles(outputDir, absolutePath);

  logInfo('extractTraceZip-complete', `Extraction and filtering complete: ${outputDir}`);

  return {
    outputDir,
  };
}

/**
 * 确保过滤文件存在，避免重复创建
 */
async function ensureFilteredFiles(outputDir: string, cacheKey: string): Promise<void> {
  if (filteringCache.has(cacheKey)) {
    logInfo('ensureFilteredFiles-skip', `Filtering already done for: ${cacheKey}`);
    return;
  }

  logInfo('ensureFilteredFiles-start', `Creating filtered files for: ${outputDir}`);

  try {
    // Create filtered trace file if the main trace exists
    const mainTraceFile = `${outputDir}/0-trace.trace`;
    if (fs.existsSync(mainTraceFile)) {
      await createFilteredTrace(mainTraceFile);
      logInfo('ensureFilteredFiles-trace', `Created filtered trace file`);
    }

    // Create filtered network trace file if the network trace exists
    const networkTraceFile = `${outputDir}/0-trace.network`;
    if (fs.existsSync(networkTraceFile)) {
      await createFilteredNetworkTrace(networkTraceFile);
      logInfo('ensureFilteredFiles-network', `Created filtered network file`);
    }

    // 标记过滤完成
    filteringCache.set(cacheKey, true);
    logInfo('ensureFilteredFiles-complete', `Filtering complete for: ${cacheKey}`);
  } catch (error) {
    logError('ensureFilteredFiles-error', `Error creating filtered files: ${error}`);
    // 不抛出错误，静默处理过滤错误，不影响主要功能
  }
}

const server = new FastMCP({
  name: "Playwright Trace Analyzer",
  version: "1.0.0",
});

server.addTool({
  annotations: {
    openWorldHint: false,
    readOnlyHint: true,
    title: "Get Network Log from Trace",
  },
  description:
    "Get browser network logs from a trace.zip file. By default returns a filtered version that removes analytics, third-party services, and verbose metadata while preserving essential debugging information. Use raw=true to get unfiltered logs.",
  execute: async (args) => {
    const { outputDir } = await extractTraceZip(args.traceZipPath);
    let output = "";
    const originalNetworkFilePath = `${outputDir}/0-trace.network`;

    try {
      if (args.raw) {
        // Return raw unfiltered network log
        const networkContent = fs.readFileSync(originalNetworkFilePath, "utf8");
        output += `Raw network log (unfiltered):\n\n${analyzeTraceContent(networkContent, 'network')}`;
      } else {
        // Return filtered network log - use existing filtered file if available
        const filteredNetworkFilePath = originalNetworkFilePath.replace('.network', '_filtered.network');
        let networkContent: string;

        if (fs.existsSync(filteredNetworkFilePath)) {
          // Use existing filtered file
          logInfo('get-network-log-cached', `Using existing filtered file: ${filteredNetworkFilePath}`);
          networkContent = fs.readFileSync(filteredNetworkFilePath, "utf8");
        } else {
          // Create filtered file if it doesn't exist
          logInfo('get-network-log-create', `Creating filtered file: ${filteredNetworkFilePath}`);
          const createdPath = await createFilteredNetworkTrace(originalNetworkFilePath);
          networkContent = fs.readFileSync(createdPath, "utf8");
        }

        if (filteredNetworkFilePath.includes("_filtered.network")) {
          output += `Filtered network log (80%+ size reduction, third-party services removed):\n\n${analyzeTraceContent(networkContent, 'network')}`;
        } else {
          output += `Network log:\n\n${analyzeTraceContent(networkContent, 'network')}`;
        }
      }
    } catch (error) {
      return logError('get-network-log', error);
    }
    return output;
  },
  name: "get-network-log",
  parameters: z.object({
    raw: z
      .boolean()
      .optional()
      .default(false)
      .describe(
        "Return raw unfiltered network log including all analytics, third-party services, and verbose metadata. Default is false (filtered)."
      ),
    traceZipPath: z
      .string()
      .describe(
        "The full path to the trace.zip file (e.g. '/path/to/trace.zip')"
      ),
  }),
});

server.addTool({
  annotations: {
    openWorldHint: false,
    readOnlyHint: true,
    title: "Get Trace from Trace File",
  },
  description:
    "Get the trace from a trace.zip file. This includes step-by-step playwright test execution info along with console logs. By default returns a filtered version that removes bloated data like DOM snapshots while preserving essential debugging information. Use raw=true to get unfiltered traces.",
  execute: async (args) => {
    const { outputDir } = await extractTraceZip(args.traceZipPath);
    let output = "";
    const originalTraceFilePath = `${outputDir}/0-trace.trace`;

    try {
      if (args.raw) {
        // Return raw unfiltered trace
        const traceContent = fs.readFileSync(originalTraceFilePath, "utf8");
        output += `Raw trace content (unfiltered):\n\n${analyzeTraceContent(traceContent, 'trace')}`;
      } else {
        // Return filtered trace - use existing filtered file if available
        const filteredTraceFilePath = originalTraceFilePath.replace('.trace', '_filtered.trace');
        let traceContent: string;

        if (fs.existsSync(filteredTraceFilePath)) {
          // Use existing filtered file
          logInfo('get-trace-cached', `Using existing filtered file: ${filteredTraceFilePath}`);
          traceContent = fs.readFileSync(filteredTraceFilePath, "utf8");
        } else {
          // Create filtered file if it doesn't exist
          logInfo('get-trace-create', `Creating filtered file: ${filteredTraceFilePath}`);
          const createdPath = await createFilteredTrace(originalTraceFilePath);
          traceContent = fs.readFileSync(createdPath, "utf8");
        }

        if (filteredTraceFilePath.includes("_filtered.trace")) {
          output += `Filtered trace content (95%+ size reduction applied):\n\n${analyzeTraceContent(traceContent, 'trace')}`;
        } else {
          output += `Trace content:\n\n${analyzeTraceContent(traceContent, 'trace')}`;
        }
      }
    } catch (error) {
      return logError('get-trace', error);
    }
    return output;
  },
  name: "get-trace",
  parameters: z.object({
    raw: z
      .boolean()
      .optional()
      .default(false)
      .describe(
        "Return raw unfiltered trace including all DOM snapshots and verbose data. Default is false (filtered)."
      ),
    traceZipPath: z
      .string()
      .describe(
        "The full path to the trace.zip file (e.g. '/path/to/trace.zip')"
      ),
  }),
});

server.addTool({
  annotations: {
    openWorldHint: false,
    readOnlyHint: true,
    title: "Get Screenshots from Trace",
  },
  description:
    "Get all available screenshots from a trace.zip file. Useful for debugging.",
  execute: async (args) => {
    try {
      logInfo('get-screenshots-start', `Starting screenshot analysis for: ${args.traceZipPath}`);

      const { outputDir } = await extractTraceZip(args.traceZipPath);
      logInfo('get-screenshots-extracted', `Extracted to: ${outputDir}`);

      const resourcesDir = `${outputDir}/resources`;
      if (!fs.existsSync(resourcesDir)) {
        logInfo('get-screenshots-no-resources', `No resources directory found: ${resourcesDir}`);
        return `📸 No resources directory found in the trace file (path: ${resourcesDir})`;
      }

      logInfo('get-screenshots-reading', `Reading directory: ${resourcesDir}`);
      const files = fs.readdirSync(resourcesDir);
      logInfo('get-screenshots-files', `Found ${files.length} files: ${files.join(', ')}`);

      const imageFiles = files.filter((file) => /\.(jpe?g|png)$/i.test(file));
      logInfo('get-screenshots-images', `Found ${imageFiles.length} image files: ${imageFiles.join(', ')}`);

      if (imageFiles.length === 0) {
        logInfo('get-screenshots-no-images', 'No image files found');
        return "📸 No screenshots found in the trace file";
      }

      // 返回截图信息而不是实际图片内容，避免不支持图片的客户端出错
      let output = `📸 SCREENSHOTS FOUND (${imageFiles.length})\n`;
      output += "=".repeat(40) + "\n\n";

      imageFiles.forEach((file, i) => {
        try {
          const fullPath = `${outputDir}/resources/${file}`;
          logInfo('get-screenshots-stat', `Getting stats for: ${fullPath}`);

          if (!fs.existsSync(fullPath)) {
            logError('get-screenshots-missing', `File not found: ${fullPath}`);
            output += `${i + 1}. ${file} - ❌ File not found\n\n`;
            return;
          }

          const stats = fs.statSync(fullPath);
          output += `${i + 1}. ${file}\n`;
          output += `   📁 Path: ${fullPath}\n`;
          output += `   📊 Size: ${(stats.size / 1024).toFixed(2)} KB\n`;
          output += `   📅 Modified: ${stats.mtime.toLocaleString()}\n\n`;

          logInfo('get-screenshots-processed', `Successfully processed: ${file}`);
        } catch (fileError) {
          logError('get-screenshots-file-error', `Error processing file ${file}: ${fileError}`);
          output += `${i + 1}. ${file} - ❌ Error reading file: ${fileError instanceof Error ? fileError.message : String(fileError)}\n\n`;
        }
      });

      output += "💡 Note: Screenshot files are available at the paths above.\n";
      output += "Use a file viewer or Playwright trace viewer to view the images.\n";

      logInfo('get-screenshots-success', 'Screenshot analysis completed successfully');
      return output;

    } catch (error) {
      return logError('get-screenshots', error);
    }
  },
  name: "get-screenshots",
  parameters: z.object({
    traceZipPath: z
      .string()
      .describe(
        "The full path to the trace.zip file (e.g. '/path/to/trace.zip')"
      ),
  }),
});

// 注释掉图片查看工具，避免 imageContent 相关的错误
// server.addTool({
//   annotations: {
//     openWorldHint: false,
//     readOnlyHint: true,
//     title: "View Screenshot (Image Content) - DISABLED",
//   },
//   description:
//     "This tool is disabled to avoid imageContent compatibility issues. Use get-screenshots to see file paths and open images externally.",
//   execute: async (args) => {
//     return "❌ This tool is disabled to avoid compatibility issues. Use get-screenshots to see file paths and open images in an external viewer.";
//   },
//   name: "view-screenshot-disabled",
//   parameters: z.object({
//     traceZipPath: z.string(),
//     filename: z.string(),
//   }),
// });

server.addTool({
  annotations: {
    openWorldHint: false,
    readOnlyHint: true,
    title: "Analyze Complete Trace",
  },
  description:
    "Perform a comprehensive analysis of a trace.zip file, including trace execution, network logs, and screenshots. This is the recommended tool for complete trace analysis.",
  execute: async (args) => {
    try {
      const { outputDir } = await extractTraceZip(args.traceZipPath);
      let output = "";

      // 分析基本信息
      output += "🎭 PLAYWRIGHT TRACE ANALYSIS\n";
      output += "=" .repeat(50) + "\n\n";

    // 1. 分析 trace 执行
    output += "📋 TRACE EXECUTION ANALYSIS\n";
    output += "-".repeat(30) + "\n";

    const originalTraceFilePath = `${outputDir}/0-trace.trace`;
    if (fs.existsSync(originalTraceFilePath)) {
      try {
        const filteredTraceFilePath = await createFilteredTrace(originalTraceFilePath);
        const traceContent = fs.readFileSync(filteredTraceFilePath, "utf8");

        // 解析 trace 内容获取统计信息
        const traceLines = traceContent.split('\n').filter(line => line.trim());
        const traceEntries = traceLines.map(line => {
          try {
            return JSON.parse(line);
          } catch {
            return null;
          }
        }).filter(Boolean);

        const actions = traceEntries.filter(entry => entry.type === 'action');
        const consoleEntries = traceEntries.filter(entry => entry.type === 'console');
        const errors = consoleEntries.filter(entry => entry.messageType === 'error');

        output += `📊 Total trace entries: ${traceEntries.length}\n`;
        output += `🎬 Actions performed: ${actions.length}\n`;
        output += `📝 Console messages: ${consoleEntries.length}\n`;
        output += `❌ Errors found: ${errors.length}\n\n`;

        if (errors.length > 0) {
          output += "🚨 ERRORS DETECTED:\n";
          errors.slice(0, 3).forEach((error, i) => {
            output += `${i + 1}. ${error.text}\n`;
          });
          if (errors.length > 3) {
            output += `... and ${errors.length - 3} more errors\n`;
          }
          output += "\n";
        }

        if (actions.length > 0) {
          output += "🎬 KEY ACTIONS:\n";
          actions.slice(0, 5).forEach((action, i) => {
            output += `${i + 1}. ${action.action}`;
            if (action.url) output += ` → ${action.url}`;
            if (action.selector) output += ` (${action.selector})`;
            output += "\n";
          });
          if (actions.length > 5) {
            output += `... and ${actions.length - 5} more actions\n`;
          }
          output += "\n";
        }

      } catch (error) {
        output += `❌ Error analyzing trace: ${error}\n\n`;
      }
    } else {
      output += "❌ No trace file found\n\n";
    }

    // 2. 分析网络请求
    output += "🌐 NETWORK ANALYSIS\n";
    output += "-".repeat(30) + "\n";

    const originalNetworkFilePath = `${outputDir}/0-trace.network`;
    if (fs.existsSync(originalNetworkFilePath)) {
      try {
        const filteredNetworkFilePath = await createFilteredNetworkTrace(originalNetworkFilePath);
        const networkContent = fs.readFileSync(filteredNetworkFilePath, "utf8");

        const networkLines = networkContent.split('\n').filter(line => line.trim());
        const networkEntries = networkLines.map(line => {
          try {
            return JSON.parse(line);
          } catch {
            return null;
          }
        }).filter(Boolean);

        const requests = networkEntries.filter(entry => entry.snapshot?.request);
        const failedRequests = requests.filter(entry => {
          const status = entry.snapshot?.response?.status;
          return status && (status >= 400 || status === 0);
        });

        output += `📊 Total network requests: ${requests.length}\n`;
        output += `❌ Failed requests: ${failedRequests.length}\n\n`;

        if (failedRequests.length > 0) {
          output += "🚨 FAILED REQUESTS:\n";
          failedRequests.slice(0, 5).forEach((req, i) => {
            const url = req.snapshot?.request?.url || 'Unknown URL';
            const status = req.snapshot?.response?.status || 'Unknown';
            const method = req.snapshot?.request?.method || 'Unknown';
            output += `${i + 1}. ${method} ${url} → ${status}\n`;
          });
          if (failedRequests.length > 5) {
            output += `... and ${failedRequests.length - 5} more failed requests\n`;
          }
          output += "\n";
        }

      } catch (error) {
        output += `❌ Error analyzing network: ${error}\n\n`;
      }
    } else {
      output += "❌ No network trace found\n\n";
    }

    // 3. 分析截图
    output += "📸 SCREENSHOTS ANALYSIS\n";
    output += "-".repeat(30) + "\n";

    const resourcesDir = `${outputDir}/resources`;
    if (fs.existsSync(resourcesDir)) {
      try {
        const files = fs.readdirSync(resourcesDir);
        const imageFiles = files.filter((file) => /\.(jpe?g|png)$/i.test(file));

        output += `📊 Screenshots found: ${imageFiles.length}\n`;

        if (imageFiles.length > 0) {
          output += "📸 Available screenshots:\n";
          imageFiles.forEach((file, i) => {
            output += `${i + 1}. ${file}\n`;
          });
        }

      } catch (error) {
        output += `❌ Error analyzing screenshots: ${error}\n`;
      }
    } else {
      output += "❌ No screenshots found\n";
    }

      output += "\n" + "=".repeat(50) + "\n";
      output += "✅ Analysis complete! Use individual tools for detailed information.\n";

      return output; // analyze-trace 已经是智能摘要，不需要再截断
    } catch (error) {
      return `❌ Error analyzing trace: ${error instanceof Error ? error.message : String(error)}`;
    }
  },
  name: "analyze-trace",
  parameters: z.object({
    traceZipPath: z
      .string()
      .describe(
        "The full path to the trace.zip file (e.g. '/path/to/trace.zip')"
      ),
  }),
});

server.addTool({
  annotations: {
    openWorldHint: false,
    readOnlyHint: true,
    title: "Get Raw Trace Content (Paginated)",
  },
  description:
    "Get raw trace content in pages to avoid size limits. Use this when you need the complete unfiltered trace data.",
  execute: async (args) => {
    try {
      const { outputDir } = await extractTraceZip(args.traceZipPath);
      const originalTraceFilePath = `${outputDir}/0-trace.trace`;

      if (!fs.existsSync(originalTraceFilePath)) {
        return "❌ No trace file found";
      }

      const traceContent = fs.readFileSync(originalTraceFilePath, "utf8");
      const lines = traceContent.split('\n').filter(line => line.trim());

      const pageSize = args.pageSize || 50;
      const page = args.page || 1;
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const totalPages = Math.ceil(lines.length / pageSize);

      if (page > totalPages) {
        return `❌ Page ${page} does not exist. Total pages: ${totalPages}`;
      }

      const pageLines = lines.slice(startIndex, endIndex);

      let output = `📄 TRACE CONTENT - Page ${page}/${totalPages}\n`;
      output += `📊 Showing entries ${startIndex + 1}-${Math.min(endIndex, lines.length)} of ${lines.length}\n`;
      output += "=".repeat(50) + "\n\n";

      pageLines.forEach((line, i) => {
        output += `${startIndex + i + 1}: ${line}\n`;
      });

      output += "\n" + "=".repeat(50) + "\n";
      output += `📄 Page ${page}/${totalPages} | Use page=${page + 1} for next page`;

      return output;
    } catch (error) {
      return `❌ Error reading trace: ${error instanceof Error ? error.message : String(error)}`;
    }
  },
  name: "get-raw-trace-paginated",
  parameters: z.object({
    traceZipPath: z
      .string()
      .describe("The full path to the trace.zip file"),
    page: z
      .number()
      .optional()
      .default(1)
      .describe("Page number (default: 1)"),
    pageSize: z
      .number()
      .optional()
      .default(50)
      .describe("Number of entries per page (default: 50)"),
  }),
});

server.addTool({
  annotations: {
    openWorldHint: false,
    readOnlyHint: true,
    title: "Get Raw Network Content (Paginated)",
  },
  description:
    "Get raw network content in pages to avoid size limits. Use this when you need the complete unfiltered network data.",
  execute: async (args) => {
    try {
      const { outputDir } = await extractTraceZip(args.traceZipPath);
      const originalNetworkFilePath = `${outputDir}/0-trace.network`;

      if (!fs.existsSync(originalNetworkFilePath)) {
        return "❌ No network trace file found";
      }

      const networkContent = fs.readFileSync(originalNetworkFilePath, "utf8");
      const lines = networkContent.split('\n').filter(line => line.trim());

      const pageSize = args.pageSize || 20;
      const page = args.page || 1;
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const totalPages = Math.ceil(lines.length / pageSize);

      if (page > totalPages) {
        return `❌ Page ${page} does not exist. Total pages: ${totalPages}`;
      }

      const pageLines = lines.slice(startIndex, endIndex);

      let output = `🌐 NETWORK CONTENT - Page ${page}/${totalPages}\n`;
      output += `📊 Showing requests ${startIndex + 1}-${Math.min(endIndex, lines.length)} of ${lines.length}\n`;
      output += "=".repeat(50) + "\n\n";

      pageLines.forEach((line, i) => {
        output += `${startIndex + i + 1}: ${line}\n`;
      });

      output += "\n" + "=".repeat(50) + "\n";
      output += `📄 Page ${page}/${totalPages} | Use page=${page + 1} for next page`;

      return output;
    } catch (error) {
      return `❌ Error reading network trace: ${error instanceof Error ? error.message : String(error)}`;
    }
  },
  name: "get-raw-network-paginated",
  parameters: z.object({
    traceZipPath: z
      .string()
      .describe("The full path to the trace.zip file"),
    page: z
      .number()
      .optional()
      .default(1)
      .describe("Page number (default: 1)"),
    pageSize: z
      .number()
      .optional()
      .default(20)
      .describe("Number of requests per page (default: 20)"),
  }),
});

// 导出函数供测试使用
export { extractTraceZip, createFilteredTrace, createFilteredNetworkTrace };

// 只有在直接运行时才启动服务器
if (import.meta.url === `file://${process.argv[1]}`) {
  server.start({
    transportType: "stdio",
  });
}
