#!/usr/bin/env node

// Simple test script to verify the trace analyzer functionality
// This script simulates how the MCP tools would be called

import fs from 'fs';
import path from 'path';
import AdmZip from 'adm-zip';

// Create a mock trace.zip file for testing
function createMockTraceZip() {
  const testDir = './test-trace';
  const zipPath = './test-trace.zip';
  
  // Create test directory structure
  if (!fs.existsSync(testDir)) {
    fs.mkdirSync(testDir, { recursive: true });
  }
  
  // Create mock trace file
  const mockTrace = {
    type: 'trace',
    entries: [
      { type: 'action', action: 'goto', url: 'https://example.com' },
      { type: 'action', action: 'click', selector: 'button' },
      { type: 'console', messageType: 'log', text: 'Test message' }
    ]
  };
  
  fs.writeFileSync(path.join(testDir, '0-trace.trace'), JSON.stringify(mockTrace, null, 2));
  
  // Create mock network trace
  const mockNetwork = {
    type: 'network',
    entries: [
      {
        type: 'request',
        url: 'https://example.com',
        method: 'GET',
        status: 200
      }
    ]
  };
  
  fs.writeFileSync(path.join(testDir, '0-trace.network'), JSON.stringify(mockNetwork, null, 2));
  
  // Create resources directory with a mock screenshot
  const resourcesDir = path.join(testDir, 'resources');
  if (!fs.existsSync(resourcesDir)) {
    fs.mkdirSync(resourcesDir, { recursive: true });
  }
  
  // Create a simple mock image file (just text for testing)
  fs.writeFileSync(path.join(resourcesDir, 'screenshot-1.png'), 'Mock PNG data');
  
  // Create zip file
  const zip = new AdmZip();
  zip.addLocalFolder(testDir);
  zip.writeZip(zipPath);
  
  // Clean up test directory
  fs.rmSync(testDir, { recursive: true, force: true });
  
  console.log(`Created mock trace.zip file: ${zipPath}`);
  return zipPath;
}

// Test the trace analyzer
async function testTraceAnalyzer() {
  console.log('Testing Playwright Trace Analyzer...\n');
  
  // Create mock trace file
  const zipPath = createMockTraceZip();
  
  try {
    // Import the trace analyzer functions
    const { extractTraceZip } = await import('./dist/server.js');
    
    console.log('✅ Successfully imported trace analyzer functions');
    console.log(`✅ Mock trace file created: ${zipPath}`);
    console.log('✅ Trace analyzer is ready to use!');
    
    console.log('\nTo test the MCP tools, use the following trace.zip path:');
    console.log(`  ${path.resolve(zipPath)}`);
    
  } catch (error) {
    console.error('❌ Error testing trace analyzer:', error.message);
  }
}

testTraceAnalyzer().catch(console.error);
