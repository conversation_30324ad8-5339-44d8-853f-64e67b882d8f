{"version":7,"type":"context-options","origin":"library","browserName":"chromium","options":{"noDefaultViewport":false,"viewport":{"width":1280,"height":720},"ignoreHTTPSErrors":false,"javaScriptEnabled":true,"bypassCSP":false,"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.6778.33 Safari/537.36","locale":"en-US","permissions":["microphone","notifications","camera"],"offline":false,"deviceScaleFactor":1,"isMobile":false,"hasTouch":false,"colorScheme":"light","acceptDownloads":"accept","baseURL":"https://po-automationca.mytalkdeskca.com","recordVideo":{"dir":"/test-results/.playwright-artifacts-0","size":{"width":800,"height":450}},"serviceWorkers":"allow","storageState":{"cookies":[{"name":"SESSION","value":"ZmE3MDcwZmQtODVlOC00M2MzLWJkM2MtNDk3NTIyMWUxNTVl","domain":"tiger.mytalkdesk.com","path":"/atlas/","expires":-1,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"ATLAS_DEVICE_ID","value":"2fb891d0-846d-4540-ad44-1df370cff1c8","domain":".mytalkdesk.com","path":"/atlas","expires":1706669448.535065,"httpOnly":true,"secure":false,"sameSite":"Lax"},{"name":"X-Platform-Tid","value":"9e405752-1af2-4ff3-93e1-a73d04346035","domain":"tiger.talkdeskid.com","path":"/","expires":-1,"httpOnly":false,"secure":false,"sameSite":"Lax"},{"name":"JSESSIONID","value":"41674d71aa39e769","domain":".nr-data.net","path":"/","expires":-1,"httpOnly":false,"secure":true,"sameSite":"None"},{"name":"SESSION","value":"Y2VhOTg2YzktMmQwYi00ODk5LWFkZjQtZjY3ZDdiYWNhYzc0","domain":"tiger.talkdeskid.com","path":"/","expires":-1,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"SESSION","value":"MTI0YzA5MWUtNzc3Mi00YThjLWFiYTUtN2FmNzY2ODM0ZGQ3","domain":"jingwei.trytalkdesk.com","path":"/atlas/","expires":-1,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"ATLAS_DEVICE_ID","value":"de664737-17d8-4d13-8dd8-306a43cd587a","domain":".trytalkdesk.com","path":"/atlas","expires":1706669988.809223,"httpOnly":true,"secure":false,"sameSite":"Lax"},{"name":"X-Platform-Tid","value":"9cabe775-741f-442e-9f35-07a817c1359f","domain":"jingwei.talkdeskqaid.com","path":"/","expires":-1,"httpOnly":false,"secure":false,"sameSite":"Lax"},{"name":"SESSION","value":"OWRmYzRhMTItNGY2MC00YTljLTkxNTYtMmZlMzI5Mjc4ZWQ4","domain":"dialer-team-qa-aux.trytalkdesk.com","path":"/atlas/","expires":-1,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"SESSION","value":"OTI0NjcyNGYtODBkZS00MjI0LWI4YWMtYjdjMGM5ZTljZjgy","domain":"email-qa.trytalkdesk.com","path":"/atlas/","expires":-1,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"SESSION","value":"Yzg3YTQ1MGEtMDIzZi00NTU5LWI0MmUtYTQ0ZDhhYjgzYjk3","domain":"qa-yeva.trytalkdesk.com","path":"/atlas/","expires":-1,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"SESSION","value":"OTQ2MjhlOTAtNDAzMi00YTZmLWE5ZDItN2Y4NmU2YTU5ODZj","domain":"diting.trytalkdesk.com","path":"/atlas/","expires":-1,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"SESSION","value":"NTlkMGUwYzYtYTA5ZS00OTliLThlNDUtOWJkM2RlNzVlY2Y4","domain":"deeting-fe.gettalkdesk.com","path":"/atlas/","expires":-1,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"ATLAS_DEVICE_ID","value":"76f94700-228e-4a20-90d1-2f3f3e527aba","domain":".gettalkdesk.com","path":"/atlas","expires":1706671745.599724,"httpOnly":true,"secure":false,"sameSite":"Lax"},{"name":"X-Platform-Tid","value":"a176a748-81bb-435a-aa17-82187787d68c","domain":"deeting-fe.talkdeskstgid.com","path":"/","expires":-1,"httpOnly":false,"secure":false,"sameSite":"Lax"},{"name":"SESSION","value":"OTI5NDdjOTMtODU3MS00MmM2LTk5MzQtYzI4YWY5ZWU0YTg1","domain":"deeting-fe.talkdeskstgid.com","path":"/","expires":-1,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"SESSION","value":"OGE1M2VmNmItYWZkNi00YzRiLTk1OWYtNmFkZWNlZDQwZTg5","domain":"deeting.mytalkdesk.com","path":"/atlas/","expires":-1,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"SESSION","value":"ODRlZmRjNjgtMjMyMC00Yzg0LWJiNGEtZmVjNDNjNDNjNDFh","domain":"outbound-tiger.mytalkdesk.eu","path":"/atlas/","expires":-1,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"ATLAS_DEVICE_ID","value":"89164c0d-f554-4f6f-8dcf-dc5ea2a11f4d","domain":".mytalkdesk.eu","path":"/atlas","expires":1706672132.910514,"httpOnly":true,"secure":false,"sameSite":"Lax"},{"name":"X-Platform-Tid","value":"5b41f23e-7d3b-4ca5-a97c-bef0c7edaddb","domain":"outbound-tiger.talkdeskid.eu","path":"/","expires":-1,"httpOnly":false,"secure":false,"sameSite":"Lax"},{"name":"SESSION","value":"NDViYTlhZTktM2Y3ZC00OWQwLTkzOTktMDYyZGQ5ODMxMWM2","domain":"outbound-tiger.talkdeskid.eu","path":"/","expires":-1,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"SESSION","value":"YzEwN2VmMTAtZDllNS00OTBjLTlhYTgtZDgxYTdmZjMwMWVl","domain":"po-automationca.mytalkdeskca.com","path":"/atlas/","expires":-1,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"ATLAS_DEVICE_ID","value":"3d76c159-410d-4027-a77d-a2eb7926bb60","domain":".mytalkdeskca.com","path":"/atlas","expires":1706672232.625874,"httpOnly":true,"secure":false,"sameSite":"Lax"},{"name":"X-Platform-Tid","value":"14bd9c35-035d-4a05-a202-ec6d6bcde158","domain":"po-automationca.talkdeskidca.com","path":"/","expires":-1,"httpOnly":false,"secure":false,"sameSite":"Lax"},{"name":"SESSION","value":"YWMzMWQ5MTItZDZhMC00MWFiLWE1ZGUtOThmZWJhNTkzYWY1","domain":"po-automationca.talkdeskidca.com","path":"/","expires":-1,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"SESSION","value":"MDBmZDEyYzctMzI2Zi00YTVjLTk5MmMtODI0Njc0MGY3NzU4","domain":"po-automation.mytalkdesk.eu","path":"/atlas/","expires":-1,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"mongoMachineId","value":"3342665","domain":"diting.trytalkdesk.com","path":"/","expires":**********.520341,"httpOnly":false,"secure":false,"sameSite":"Lax"},{"name":"tdaccount","value":"diting","domain":"diting.trytalkdesk.com","path":"/","expires":-1,"httpOnly":false,"secure":true,"sameSite":"Lax"},{"name":"_talkdesk_session","value":"c2e6e48024c1e7898607f33debd70af1","domain":"diting.trytalkdesk.com","path":"/","expires":-1,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"X-Platform-Tid","value":"0de2b405-b438-4938-961a-7178b39010d6","domain":"diting.talkdeskqaid.com","path":"/","expires":-1,"httpOnly":false,"secure":false,"sameSite":"Lax"},{"name":"SESSION","value":"MWI5ZWY0YTQtZTE5Ni00NTZhLTgyMTMtNTczNWFkZTc1Njk2","domain":"diting.talkdeskqaid.com","path":"/","expires":-1,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"SESSION","value":"NmFkNWM4ZDgtZTI1Ny00MGYyLTkwYjYtZjkwNmExMjg2OTU4","domain":"jingwei.talkdeskqaid.com","path":"/","expires":-1,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"X-Platform-Tid","value":"8756c793-8b1b-4c84-8634-9069244a09cf","domain":"po-automation.talkdeskid.eu","path":"/","expires":-1,"httpOnly":false,"secure":false,"sameSite":"Lax"},{"name":"SESSION","value":"M2VlYWUxNWQtY2Q4MC00NTVjLWI3MzItYjRjMWJmMmI1YTVi","domain":"po-automation.talkdeskid.eu","path":"/","expires":-1,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"X-Platform-Tid","value":"1da22c41-d904-4bad-928c-f09d943234eb","domain":"dialer-team-qa-aux.talkdeskqaid.com","path":"/","expires":-1,"httpOnly":false,"secure":false,"sameSite":"Lax"},{"name":"SESSION","value":"ZTc3YzBjMGUtOWFiOS00NGRkLWI5NzYtYzBhNmYwZDc3OWZl","domain":"dialer-team-qa-aux.talkdeskqaid.com","path":"/","expires":-1,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"X-Platform-Tid","value":"ca1305b2-6d06-4446-bf96-398b2d4c21bf","domain":"deeting.talkdeskid.com","path":"/","expires":-1,"httpOnly":false,"secure":false,"sameSite":"Lax"},{"name":"SESSION","value":"ZjRiNGEwNTgtZTZhNC00NGViLWI4MTYtNTk5YTlmOTc3ZmM1","domain":"deeting.talkdeskid.com","path":"/","expires":-1,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"AWSALBCORS","value":"ILR6rgXARXNVWnTuUQiiLK3QyLPtVqQS2GZIZyyH2fbJ4Iv3AOd2sb5VxqXVdvefh90IdSFp8S+o0O2DJaxhpfTLOJPt45pef0gjlUSX3EU5BXHFI/fQbXmIQUXD","domain":"webrtc.cpaas.talkdeskqa.com","path":"/","expires":1679715457.137436,"httpOnly":false,"secure":true,"sameSite":"None"},{"name":"SESSION","value":"YjFiZWVlYzktZTg5Zi00NWQ0LWFkODEtMzhmNjlmZjBmNzg3","domain":"jingwei.trytalkdesk.com","path":"/atlas","expires":-1,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"SESSION","value":"MTgxMzAzMDYtNjY1Yi00NWU2LWE2ZjQtYWQzZTc1YTI4ZGRl","domain":"cfm-automatic.trytalkdesk.com","path":"/atlas","expires":-1,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"X-Platform-Tid","value":"dcf6f925-b8c3-4700-b8a7-80b7b878adab","domain":"cfm-automatic.talkdeskqaid.com","path":"/","expires":-1,"httpOnly":false,"secure":false,"sameSite":"Lax"},{"name":"SESSION","value":"ZTRiODE1MjEtZWMxZi00YjU5LTg1YzItYzQ5ZTU4NTZmOWVj","domain":"cfm-automatic.talkdeskqaid.com","path":"/","expires":-1,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"SESSION","value":"NWI5YzI0MmItN2Q5NS00MTVkLTlkMDQtMGJhZTQzYzNmZWIz","domain":"po-automation.mytalkdesk.eu","path":"/atlas","expires":-1,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"SESSION","value":"YmY5MWY2ZTctYWYyNS00ZTdlLWI2ZjYtYTU2ZmY0OWFlZDdm","domain":"tiger.mytalkdesk.com","path":"/atlas","expires":-1,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"test_cookie","value":"CheckForPermission","domain":".doubleclick.net","path":"/","expires":1693564161.931661,"httpOnly":false,"secure":true,"sameSite":"None"},{"name":"_gd_visitor","value":"b7e80772-8708-4fe6-8e47-505c5b21660a","domain":"td-frontend-app.meza.talkdeskqa.com","path":"/","expires":1728123261.973548,"httpOnly":false,"secure":true,"sameSite":"None"},{"name":"_gd_session","value":"555026b8-0947-4443-8afa-7c4a72829db4","domain":"td-frontend-app.meza.talkdeskqa.com","path":"/","expires":1693577661,"httpOnly":false,"secure":true,"sameSite":"None"},{"name":"__cf_bm","value":"G_XrFgwas9cK5xEzpm1GDZ_roVG4S1d6U.wQE04zk_Q-1693563262-0-AdXrhQ1WTAYxVTkTIhA9rMEEO11aArFyyiKEx+22hpbqe1ijxFiHTFAn1SrRz2exggWh3T1Ecp4V0EDItEJGDkA=","domain":".learn.talkdesk.com","path":"/","expires":1693565062.277784,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"MUID","value":"09E1A783187861190F14B4FC19A1609F","domain":".bing.com","path":"/","expires":1727259262.716438,"httpOnly":false,"secure":true,"sameSite":"None"},{"name":"MR","value":"0","domain":".bat.bing.com","path":"/","expires":1694168062.716535,"httpOnly":false,"secure":true,"sameSite":"None"},{"name":"AWSALBTGCORS","value":"uNyjpkdRB7hLt6GRdAbskfNDPECW+7hkZpwSFDzbWoYvBrXVdaGZbg6eAxoY5Pff1YFsUqiGywbjaxl5R35Y1d9lvJJARirr7Di7YZki2oJ7XFKE6dG3YiMBqsw786REkNdVu44Ylif6KAbxnUhTQhthLviHO0CwJqffq3jA48Wvmso71hc=","domain":"talkdeskinc.us-5.evergage.com","path":"/","expires":1694168062.89109,"httpOnly":false,"secure":true,"sameSite":"None"},{"name":"__cflb","value":"0H28vkj1CsG1kVja9aXDWFTuLhooECYNUox2Ujg4gAw","domain":"tiger.talkdeskid.com","path":"/","expires":1715676417.636742,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"SESSION","value":"Y2I1ZmE1MDAtMGFkYS00MGE2LWFkNDUtODk1YWMwYzNiNzRk","domain":"dialerintegrations.mytalkdesk.eu","path":"/atlas","expires":-1,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"__cflb","value":"0H28v1ZSB7SR68a6L8SoJge4ZzLtDkizaNNznGtiU3m","domain":"dialerintegrations.talkdeskid.eu","path":"/","expires":1715676885.193663,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"SESSION","value":"YjM0MmVmMDYtMWQ5OS00ZDcxLTg2OWUtOThlZWQ0NTgyZDQ0","domain":"dialerintegrations.talkdeskid.eu","path":"/","expires":-1,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"X-Platform-Tid","value":"fb84b65b-ad49-4be2-afa6-f9916c78376e","domain":"dialerintegrations.talkdeskid.eu","path":"/","expires":-1,"httpOnly":false,"secure":false,"sameSite":"Lax"},{"name":"SESSION","value":"MzE0NDhmMDAtZTA5Yi00MTM1LTg3NzQtOTM5NTdiZWM1ZGIx","domain":"po-automationca.mytalkdeskca.com","path":"/atlas","expires":-1,"httpOnly":true,"secure":true,"sameSite":"None"},{"name":"dtCookie","value":"v_4_srv_2_sn_3FC9FFACCA65EDB56392EB0AC3474196_perc_100000_ol_0_mul_1_app-3Aea7c4b59f27d43eb_1","domain":".mytalkdeskca.com","path":"/","expires":-1,"httpOnly":false,"secure":true,"sameSite":"Lax"},{"name":"dtCookie","value":"v_4_srv_1_sn_3D69C20F98F69A1B0DB59B9A9FB88B26_perc_100000_ol_0_mul_1_app-3Aea7c4b59f27d43eb_1_app-3A7425ed15a5752e43_1","domain":".talkdeskidca.com","path":"/","expires":-1,"httpOnly":false,"secure":true,"sameSite":"Lax"},{"name":"rxVisitor","value":"1748203314359FKUGF2C9OQBO51NPL239EQ61LLF93JO3","domain":".talkdeskidca.com","path":"/","expires":-1,"httpOnly":false,"secure":true,"sameSite":"Lax"},{"name":"rxvt","value":"1748205114626|1748203314360","domain":".talkdeskidca.com","path":"/","expires":-1,"httpOnly":false,"secure":true,"sameSite":"Lax"},{"name":"dtSa","value":"false%7CC%7C3%7CLogin%7Cfetch%7C1748203314625%7C403314357_820%7Chttps%3A%2F%2Fpo-automationca.talkdeskidca.com%2Flogin%7C%7C%7C%7C","domain":".talkdeskidca.com","path":"/","expires":-1,"httpOnly":false,"secure":true,"sameSite":"Lax"},{"name":"dtPC","value":"1$403314357_820h-vKJHBMUSMFGBLRUAHNLACPFSUKQFAJUCB-0e0","domain":".talkdeskidca.com","path":"/","expires":-1,"httpOnly":false,"secure":true,"sameSite":"Lax"}],"origins":[{"origin":"https://tiger.mytalkdesk.com","localStorage":[{"name":"USER_PREFERENCES/62c6b963bbdb3a26c0b26249","value":"{\"drawerFixed\":null,\"secondaryAreaFixed\":null,\"secondaryAreaOpenedApp\":{\"icon\":{\"fallbackUrl\":\"https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/app_helper.svg\"},\"title\":\"Smart Help\",\"selected\":false,\"badge\":{\"hasError\":false,\"hasOngoingAction\":false},\"slug\":\"contextual-help\",\"path\":\"/\",\"key\":\"contextual-help\",\"type\":\"droplet\",\"location\":{\"slug\":\"contextual-help\",\"path\":\"\",\"search\":\"\",\"pathLength\":0,\"url\":\"contextual-help\"}},\"userPrefersDrawerOverSecondary\":false}"},{"name":"_pendo_visitorId.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"{\"ttl\":*************,\"value\":\"62c6b963bbdb3a26c0b26249\"}"},{"name":"API_USER_PREFERENCES/62c6b963bbdb3a26c0b26249","value":"{\"language\":\"en-US\",\"alwaysOnTop\":true}"},{"name":"_pendo_accountId.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"{\"ttl\":*************,\"value\":\"619f4d30f5d6b0e4e7563024\"}"},{"name":"_pendo_meta.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"{\"ttl\":*************,\"value\":*********}"},{"name":"NRBA_SESSION","value":"{\"value\":\"b7da7565fbc81da7\",\"inactiveAt\":*************,\"expiresAt\":*************,\"updatedAt\":*************,\"sessionReplayMode\":0,\"sessionReplaySentFirstChunk\":false,\"sessionTraceMode\":0,\"traceHarvestStarted\":false,\"serverTimeDiff\":405,\"custom\":{}}"}]},{"origin":"https://prd-cdn-talkdesk.talkdesk.com","localStorage":[{"name":"_pendo_visitorId.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"{\"ttl\":*************,\"value\":\"637b6d301f05cb5c9508b7dc\"}"},{"name":"_pendo_guides_blocked.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"{\"ttl\":*************,\"value\":\"0\"}"},{"name":"_pendo_sessionId.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"{\"ttl\":*************,\"value\":\"{\\\"sessionId\\\":\\\"NXMgd7f6pfLfpYUC\\\",\\\"timestamp\\\":*************}\"}"},{"name":"_pendo_accountId.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"{\"ttl\":*************,\"value\":\"637b683d24e55622d4899257\"}"},{"name":"_pendo_meta.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"{\"ttl\":*************,\"value\":*********}"}]},{"origin":"https://jingwei.trytalkdesk.com","localStorage":[{"name":"NRBA_SESSION","value":"{\"value\":\"821b26c950a9312f\",\"inactiveAt\":*************,\"expiresAt\":*************,\"updatedAt\":*************,\"sessionReplay\":0,\"sessionTraceMode\":0,\"custom\":{}}"},{"name":"USER_PREFERENCES/undefined","value":"{\"drawerFixed\":null,\"secondaryAreaFixed\":null,\"secondaryAreaOpenedApp\":null,\"userPrefersDrawerOverSecondary\":false}"},{"name":"pusherTransportTLS","value":"{\"timestamp\":*************,\"transport\":\"ws\",\"latency\":628}"}]},{"origin":"https://qa-cdn-talkdesk.talkdeskdev.com","localStorage":[{"name":"bugsnag-anonymous-id","value":"cle5ndc9y00003b6aoivppive"}]},{"origin":"https://dialer-team-qa-aux.trytalkdesk.com","localStorage":[{"name":"USER_PREFERENCES/undefined","value":"{\"drawerFixed\":null,\"secondaryAreaFixed\":null,\"secondaryAreaOpenedApp\":null,\"userPrefersDrawerOverSecondary\":false}"},{"name":"pusherTransportTLS","value":"{\"timestamp\":1676463983566,\"transport\":\"ws\",\"latency\":980}"}]},{"origin":"https://email-qa.trytalkdesk.com","localStorage":[{"name":"USER_PREFERENCES/undefined","value":"{\"drawerFixed\":null,\"secondaryAreaFixed\":null,\"secondaryAreaOpenedApp\":null,\"userPrefersDrawerOverSecondary\":false}"},{"name":"pusherTransportTLS","value":"{\"timestamp\":1675520085671,\"transport\":\"ws\",\"latency\":1311}"}]},{"origin":"https://qa-yeva.trytalkdesk.com","localStorage":[{"name":"USER_PREFERENCES/undefined","value":"{\"drawerFixed\":null,\"secondaryAreaFixed\":null,\"secondaryAreaOpenedApp\":null,\"userPrefersDrawerOverSecondary\":false}"},{"name":"pusherTransportTLS","value":"{\"timestamp\":1675135570555,\"transport\":\"ws\",\"latency\":964}"}]},{"origin":"https://diting.trytalkdesk.com","localStorage":[{"name":"_pendo_visitorId.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"{\"ttl\":*************,\"value\":\"63a3c66d7d6a980ae307229e\"}"},{"name":"_pendo_guides_blocked.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"{\"ttl\":*************,\"value\":\"0\"}"},{"name":"USER_PREFERENCES/undefined","value":"{\"drawerFixed\":null,\"secondaryAreaFixed\":null,\"secondaryAreaOpenedApp\":null,\"userPrefersDrawerOverSecondary\":false}"},{"name":"_pendo_accountId.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"612e017f14d9b0e42c81f132"},{"name":"pusherTransportTLS","value":"{\"timestamp\":*************,\"transport\":\"ws\",\"latency\":919}"},{"name":"_pendo_meta.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"*********"},{"name":"mongoMachineId","value":"3342665"}]},{"origin":"https://deeting-fe.gettalkdesk.com","localStorage":[{"name":"_pendo_visitorId.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"{\"ttl\":*************,\"value\":\"63b38cc65399951be5a85bfa\"}"},{"name":"_pendo_guides_blocked.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"{\"ttl\":*************,\"value\":\"0\"}"},{"name":"USER_PREFERENCES/undefined","value":"{\"drawerFixed\":null,\"secondaryAreaFixed\":null,\"secondaryAreaOpenedApp\":null,\"userPrefersDrawerOverSecondary\":false}"},{"name":"_pendo_accountId.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"6142f79f738eb0e4a44b18c4"},{"name":"pusherTransportTLS","value":"{\"timestamp\":*************,\"transport\":\"ws\",\"latency\":1529}"},{"name":"_pendo_meta.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"**********"}]},{"origin":"https://stg-cdn-talkdesk.talkdeskdev.com","localStorage":[{"name":"bugsnag-anonymous-id","value":"cldjokn6100003969jvrgra4y"}]},{"origin":"https://deeting.mytalkdesk.com","localStorage":[{"name":"_pendo_visitorId.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"{\"ttl\":*************,\"value\":\"6410355f136e3c7d91f3b4f7\"}"},{"name":"_pendo_guides_blocked.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"{\"ttl\":*************,\"value\":\"0\"}"},{"name":"USER_PREFERENCES/undefined","value":"{\"drawerFixed\":null,\"secondaryAreaFixed\":null,\"secondaryAreaOpenedApp\":{\"icon\":{\"fallbackUrl\":\"https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/agent-assistant.svg\"},\"title\":\"Product Help\",\"selected\":false,\"badge\":{\"hasError\":false,\"hasOngoingAction\":false},\"slug\":\"contextual-help\",\"path\":\"/\",\"key\":\"contextual-help\",\"type\":\"droplet\",\"location\":{\"slug\":\"contextual-help\",\"path\":\"\",\"search\":\"\",\"pathLength\":0,\"url\":\"contextual-help\"}},\"userPrefersDrawerOverSecondary\":false}"},{"name":"_pendo_accountId.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"618a35d25b1fb0e4a4931dad"},{"name":"pusherTransportTLS","value":"{\"timestamp\":*************,\"transport\":\"ws\",\"latency\":847}"},{"name":"_pendo_lastStepAdvanced.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"{\"ttl\":*************,\"value\":\"[{\\\"tabId\\\":\\\"WWBYFTxMhHzWhtVH\\\",\\\"guideId\\\":\\\"7o-hPxO6EjC3yXoacnNDH6nX9r0\\\",\\\"guideStepId\\\":\\\"dbAJ0nsqSJhDcLhZY5KI5akCZCE\\\",\\\"time\\\":*************,\\\"state\\\":\\\"active\\\",\\\"seenReason\\\":\\\"auto\\\",\\\"visitorId\\\":\\\"6410355f136e3c7d91f3b4f7\\\"}]\"}"},{"name":"_pendo_meta.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"**********"}]},{"origin":"https://outbound-tiger.mytalkdesk.eu","localStorage":[{"name":"_pendo_visitorId.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"{\"ttl\":*************,\"value\":\"62e9e0cf4bda17732aea92de\"}"},{"name":"_pendo_guides_blocked.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"{\"ttl\":*************,\"value\":\"0\"}"},{"name":"USER_PREFERENCES/undefined","value":"{\"drawerFixed\":null,\"secondaryAreaFixed\":null,\"secondaryAreaOpenedApp\":{\"icon\":{\"fallbackUrl\":\"https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/agent-assistant.svg\"},\"title\":\"Agent Assist\",\"selected\":false,\"badge\":{\"hasError\":false,\"hasOngoingAction\":false},\"slug\":\"agent-assist-secondary\",\"path\":\"/\",\"key\":\"agent-assist-secondary\",\"type\":\"droplet\",\"location\":{\"slug\":\"agent-assist-secondary\",\"path\":\"\",\"search\":\"\",\"pathLength\":0,\"url\":\"agent-assist-secondary\"}},\"userPrefersDrawerOverSecondary\":false}"},{"name":"_pendo_accountId.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"62e9dec86ec83c24420b280d"},{"name":"pusherTransportTLS","value":"{\"timestamp\":*************,\"transport\":\"ws\",\"latency\":1439}"},{"name":"_pendo_meta.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"**********"}]},{"origin":"https://po-automationca.mytalkdeskca.com","localStorage":[{"name":"_pendo_visitorId.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"{\"ttl\":*************,\"value\":\"637b6d301f05cb5c9508b7dc\"}"},{"name":"_pendo_guides_blocked.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"{\"ttl\":*************,\"value\":\"0\"}"},{"name":"USER_PREFERENCES/637b6d301f05cb5c9508b7dc","value":"{\"drawerFixed\":null,\"secondaryAreaFixed\":true,\"secondaryAreaOpenedApp\":{\"icon\":{\"fallbackUrl\":\"https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/agent-assistant.svg\"},\"title\":\"Copilot\",\"selected\":false,\"badge\":{\"hasError\":false,\"hasOngoingAction\":false},\"slug\":\"agent-assist-secondary\",\"path\":\"/\",\"key\":\"agent-assist-secondary\",\"type\":\"droplet\",\"location\":{\"slug\":\"agent-assist-secondary\",\"path\":\"\",\"search\":\"\",\"pathLength\":0,\"url\":\"agent-assist-secondary\"}},\"userPrefersDrawerOverSecondary\":false}"},{"name":"_pendo_sessionId.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"{\"ttl\":*************,\"value\":\"{\\\"sessionId\\\":\\\"5gEaOkOXhzBFQ3Sr\\\",\\\"timestamp\\\":*************}\"}"},{"name":"_pendo_accountId.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"{\"ttl\":*************,\"value\":\"637b683d24e55622d4899257\"}"},{"name":"_pendo_meta.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"{\"ttl\":*************,\"value\":**********}"}]},{"origin":"https://po-automation.mytalkdesk.eu","localStorage":[{"name":"_pendo_visitorId.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"{\"ttl\":*************,\"value\":\"637b6dce34a968316773099e\"}"},{"name":"_pendo_guides_blocked.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"{\"ttl\":*************,\"value\":\"0\"}"},{"name":"USER_PREFERENCES/undefined","value":"{\"drawerFixed\":null,\"secondaryAreaFixed\":null,\"secondaryAreaOpenedApp\":{\"icon\":{\"fallbackUrl\":\"https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/agent-assistant.svg\"},\"title\":\"Agent Assist\",\"selected\":false,\"badge\":{\"hasError\":false,\"hasOngoingAction\":false},\"slug\":\"agent-assist-secondary\",\"path\":\"/\",\"key\":\"agent-assist-secondary\",\"type\":\"droplet\",\"location\":{\"slug\":\"agent-assist-secondary\",\"path\":\"\",\"search\":\"\",\"pathLength\":0,\"url\":\"agent-assist-secondary\"}},\"userPrefersDrawerOverSecondary\":false}"},{"name":"_pendo_accountId.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"637b656738e6007d9c4e004b"},{"name":"pusherTransportTLS","value":"{\"timestamp\":*************,\"transport\":\"ws\",\"latency\":742}"},{"name":"_pendo_meta.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"*********"},{"name":"NRBA_SESSION","value":"{\"value\":\"11e8bdfabdd3c82b\",\"inactiveAt\":*************,\"expiresAt\":*************,\"updatedAt\":*************,\"sessionReplay\":0,\"sessionTraceMode\":0,\"custom\":{}}"}]},{"origin":"https://td-frontend-app.meza.talkdeskqa.com","localStorage":[{"name":"td_marketing","value":"{\"thankYou\":{\"lastResourceName\":\"fourzerofour | Talkdesk\"}}"},{"name":"uc_ui_version","value":"3.26.0"}]},{"origin":"https://www.talkdesk.com","localStorage":[{"name":"uc_ui_version","value":"3.26.0"}]},{"origin":"https://dialerintegrations.mytalkdesk.eu","localStorage":[{"name":"USER_PREFERENCES/6639b60a55687e5a333dc666","value":"{\"drawerFixed\":null,\"secondaryAreaFixed\":null,\"secondaryAreaOpenedApp\":{\"icon\":{\"fallbackUrl\":\"https://prd-cdn-talkdesk.talkdesk.com/cdn-assets/latest/talkdesk/product/app-icons/agent-assistant.svg\"},\"title\":\"Copilot\",\"selected\":false,\"badge\":{\"hasError\":false,\"hasOngoingAction\":false},\"slug\":\"agent-assist-secondary\",\"path\":\"/\",\"key\":\"agent-assist-secondary\",\"type\":\"droplet\",\"location\":{\"slug\":\"agent-assist-secondary\",\"path\":\"\",\"search\":\"\",\"pathLength\":0,\"url\":\"agent-assist-secondary\"}},\"userPrefersDrawerOverSecondary\":false}"},{"name":"_pendo_visitorId.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"{\"ttl\":*************,\"value\":\"6639b60a55687e5a333dc666\"}"},{"name":"_pendo_accountId.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"{\"ttl\":*************,\"value\":\"65b789aa3595f951cfb5cc8c\"}"},{"name":"_pendo_meta.f3ec577a-e10f-4b30-6dcb-6d9a8c8c09a5","value":"{\"ttl\":*************,\"value\":**********}"},{"name":"API_USER_PREFERENCES/6639b60a55687e5a333dc666","value":"{\"language\":\"en-US\",\"alwaysOnTop\":true}"},{"name":"pusherTransportTLS","value":"{\"timestamp\":*************,\"transport\":\"ws\",\"latency\":7048,\"cacheSkipCount\":0}"},{"name":"NRBA_SESSION","value":"{\"value\":\"ac39654405eebe04\",\"inactiveAt\":*************,\"expiresAt\":*************,\"updatedAt\":*************,\"sessionReplayMode\":0,\"sessionReplaySentFirstChunk\":false,\"sessionTraceMode\":0,\"traceHarvestStarted\":false,\"serverTimeDiff\":-323,\"custom\":{}}"}]}]}},"platform":"linux","wallTime":*************,"monotonicTime":89706.77,"sdkLanguage":"javascript","testIdAttributeName":"data-testid","channel":"chromium","title":"smoke/test_campaign_record_flow_clone.spec.js:1375 › Smoke-Preview Campaign Create"}
{"type":"before","callId":"call@1233","startTime":89709.025,"apiName":"browserContext.newPage","class":"BrowserContext","method":"newPage","params":{},"stepId":"pw:api@6","beforeSnapshot":"before@call@1233"}
{"type":"event","time":89742.65,"class":"BrowserContext","method":"page","params":{"pageId":"page@8583ad047eb24e68c5c391e2c36584ed"}}
{"type":"after","callId":"call@1233","endTime":89742.728,"result":{"page":"<Page>"},"afterSnapshot":"after@call@1233"}
{"type":"before","callId":"call@1235","startTime":89749.772,"apiName":"page.goto","class":"Frame","method":"goto","params":{"url":"/atlas/apps/outbound-dialer","waitUntil":"load"},"stepId":"pw:api@9","pageId":"page@8583ad047eb24e68c5c391e2c36584ed","beforeSnapshot":"before@call@1235"}
{"type":"before","callId":"call@1237","startTime":89750.234,"apiName":"page.waitForNavigation","class":"Page","method":"waitForEventInfo","params":{"info":{"waitId":"c0cf012f70de833b55db1b8ba7e5e1c2","phase":"before","event":""}},"stepId":"pw:api@10","pageId":"page@8583ad047eb24e68c5c391e2c36584ed","beforeSnapshot":"before@call@1237"}
{"type":"log","callId":"call@1237","time":89750.64,"message":"waiting for navigation until \"load\""}
{"type":"log","callId":"call@1235","time":89751.905,"message":"navigating to \"https://po-automationca.mytalkdeskca.com/atlas/apps/outbound-dialer\", waiting until \"load\""}
{"type":"log","callId":"call@1237","time":89983.117,"message":"  navigated to \"https://po-automationca.mytalkdeskca.com/atlas/apps/outbound-dialer\""}
{"type":"console","messageType":"error","text":"Refused to load the script 'https://po-automationca.mytalkdeskca.com/atlas/ruxitagentjs_ICA7NVfqrux_10313250422105919.js' because it violates the following Content Security Policy directive: \"script-src 'nonce-1FabzlTCFPav3iB8TA2gs4IT/BHg7PDXnPhBPDaKXSPkjI5oLRK1r6BIWEYaot0aZd6VmWMM01AVRQhbM3J6POqimytcDiYCgUzQh2N3+YANsF8R05D5ZqabNkfX06Kty5vEMX7Q4bhUzHU2J95At9wXSnGJNEOeMT0QZLicqyU=' 'strict-dynamic' 'unsafe-inline' http: https:\". Note that 'strict-dynamic' is present, so host-based allowlisting is disabled. Note that 'script-src-elem' was not explicitly set, so 'script-src' is used as a fallback.\n","args":[],"location":{"url":"https://po-automationca.mytalkdeskca.com/atlas/apps/outbound-dialer","lineNumber":0,"columnNumber":0},"time":89987.046,"pageId":"page@8583ad047eb24e68c5c391e2c36584ed"}
{"type":"log","callId":"call@1237","time":90505.227,"message":"  \"domcontentloaded\" event fired"}
{"type":"log","callId":"call@1237","time":90615.997,"message":"  \"load\" event fired"}
{"type":"after","callId":"call@1237","endTime":90617.382,"afterSnapshot":"after@call@1237"}
{"type":"before","callId":"call@1251","startTime":90617.854,"apiName":"page.waitForTimeout","class":"Frame","method":"waitForTimeout","params":{"timeout":3000},"stepId":"pw:api@11","pageId":"page@8583ad047eb24e68c5c391e2c36584ed","beforeSnapshot":"before@call@1251"}
{"type":"console","messageType":"log","text":"Fetching new desktop versions is not available on Web engine.","args":[{"preview":"Fetching new desktop versions is not available on Web engine.","value":"Fetching new desktop versions is not available on Web engine."}],"location":{"url":"https://prd-cdn-talkdesk.talkdesk.com/atlas/0.176.12_atlas-runtime/runtime/main.js","lineNumber":1,"columnNumber":2547123},"time":90633.048,"pageId":"page@8583ad047eb24e68c5c391e2c36584ed"}
{"type":"after","callId":"call@1235","endTime":90614.371,"result":{"response":"<Response>"},"afterSnapshot":"after@call@1235"}
{"type":"console","messageType":"error","text":"Error: The 'voicemails:show-details' protocol is already registered. Skipping registration.\n    at new t (https://prd-cdn-talkdesk.talkdesk.com/atlas/0.176.12_atlas-runtime/runtime/main.js:2:2378270)\n    at Object.on (https://prd-cdn-talkdesk.talkdesk.com/atlas/0.176.12_atlas-runtime/runtime/main.js:2:2378614)\n    at Proxy.<anonymous> (https://prd-cdn-talkdesk.talkdesk.com/atlas/0.176.12_atlas-runtime/runtime/main.js:2:3103309)\n    at d (https://prd-cdn-talkdesk.talkdesk.com/atlas/0.176.12_atlas-runtime/runtime/main.js:2:2525762)\n    at service (https://prd-cdn-talkdesk.talkdesk.com/atlas/0.176.12_atlas-runtime/runtime/main.js:2:2526339)\n    at https://prd-cdn-talkdesk.talkdesk.com/atlas/0.176.12_atlas-runtime/runtime/main.js:2:2519006\n    at Array.reduce (<anonymous>)\n    at e.value (https://prd-cdn-talkdesk.talkdesk.com/atlas/0.176.12_atlas-runtime/runtime/main.js:2:2518896)\n    at j.r (https://prd-cdn-talkdesk.talkdesk.com/atlas/0.176.12_atlas-runtime/runtime/main.js:2:2518060)\n    at j.emit (https://prd-cdn-talkdesk.talkdesk.com/atlas/0.176.12_atlas-runtime/runtime/main.js:2:1026259)","args":[{"preview":"Error: The 'voicemails:show-details' protocol is already registered. Skipping registration.\n    at new t (https://prd-cdn-talkdesk.talkdesk.com/atlas/0.176.12_atlas-runtime/runtime/main.js:2:2378270)\n    at Object.on (https://prd-cdn-talkdesk.talkdesk.com/atlas/0.176.12_atlas-runtime/runtime/main.js:2:2378614)\n    at Proxy.<anonymous> (https://prd-cdn-talkdesk.talkdesk.com/atlas/0.176.12_atlas-runtime/runtime/main.js:2:3103309)\n    at d (https://prd-cdn-talkdesk.talkdesk.com/atlas/0.176.12_atlas-runtime/runtime/main.js:2:2525762)\n    at service (https://prd-cdn-talkdesk.talkdesk.com/atlas/0.176.12_atlas-runtime/runtime/main.js:2:2526339)\n    at https://prd-cdn-talkdesk.talkdesk.com/atlas/0.176.12_atlas-runtime/runtime/main.js:2:2519006\n    at Array.reduce (<anonymous>)\n    at e.value (https://prd-cdn-talkdesk.talkdesk.com/atlas/0.176.12_atlas-runtime/runtime/main.js:2:2518896)\n    at j.r (https://prd-cdn-talkdesk.talkdesk.com/atlas/0.176.12_atlas-runtime/runtime/main.js:2:2518060)\n    at j.emit (https://prd-cdn-talkdesk.talkdesk.com/atlas/0.176.12_atlas-runtime/runtime/main.js:2:1026259)"}],"location":{"url":"https://prd-cdn-talkdesk.talkdesk.com/atlas/0.176.12_atlas-runtime/runtime/main.js","lineNumber":1,"columnNumber":2526300},"time":91304.472,"pageId":"page@8583ad047eb24e68c5c391e2c36584ed"}
{"type":"console","messageType":"warning","text":"Unrecognized feature: 'speaker'.","args":[],"location":{"url":"https://prd-cdn-talkdesk.talkdesk.com/atlas/0.176.12_atlas-runtime/runtime/main.js","lineNumber":1,"columnNumber":0},"time":91399.123,"pageId":"page@8583ad047eb24e68c5c391e2c36584ed"}
{"type":"console","messageType":"warning","text":"An iframe which has both allow-scripts and allow-same-origin for its sandbox attribute can escape its sandboxing.","args":[],"location":{"url":"https://prd-cdn-talkdesk.talkdesk.com/campaign-manager-ui/latest/index.html?monitoringConfig=eyJwcm92aWRlciI6ImR5bmF0cmFjZS1vdGxwIiwidXJsIjoiaHR0cHM6Ly90YWxrZGVzay1wcm9kLWNhLTEubGl2ZS5keW5hdHJhY2UuY29tL2FwaS92Mi9vdGxwIiwidG9rZW4iOiJkdDBjMDEuQjQ2STJJM1ZFSUhPSU5BVUFWQ1VHVUJHLkpDRVZJNVBBSzdYSjdJN0laQ0pMUVNPV1pTUzZEUUxVV0JVQVVNVDRZMjVIVUZJUk5WMkVOVzNBVjdETjRBTDMiLCJwcm9wZXJ0aWVzIjp7Im5hbWUiOiJvdXRib3VuZC1kaWFsZXItcHJkLXRkLWNhLTEtY2EtY2VudHJhbC0xIiwiYmF0Y2hEZWxheSI6NTAwMCwibWV0cmljcyI6eyJpbnRlcnZhbCI6MTAwMDB9LCJpZ25vcmVVcmxzIjpbImV1XFwuanNcXC5sb2dzXFwuaW5zaWdodFxcLnJhcGlkN1xcLmNvbSIsInJcXC5sci1pblxcLmNvbSIsInJcXC5pbnRha2UtbHJcXC5jb20iLCJldmVudGd3XFwudHdpbGlvXFwuY29tIl0sInByb3BhZ2F0ZUhlYWRlclVybHMiOlsiYXBpXFwudGFsa2Rlc2thcHBjYVxcLmNvbSJdfSwiYXR0cmlidXRlcyI6eyJhdGxhcy51c2VyX2lkIjoiNjM3YjZkMzAxZjA1Y2I1Yzk1MDhiN2RjIiwiYXRsYXMudGVuYW50X2lkIjoiNjM3YjY4M2QyNGU1NTYyMmQ0ODk5MjU3IiwiYXRsYXMudGVuYW50X25hbWUiOiJwby1hdXRvbWF0aW9uY2EiLCJhdGxhcy52ZXJzaW9uIjoiMC4xNzYuMTIiLCJhdGxhcy5zZXNzaW9uX3RpZCI6IjA1NzljOTllLTI0NDEtNGU5Mi1hYTFiLTkxZmY4NDIxOTcwZiIsImF0bGFzLmVuZ2luZV90eXBlIjoid2ViIiwiYXRsYXMuZW5naW5lX3ZlcnNpb24iOiIxMzEuMC42Nzc4LjMzIiwiYXRsYXMuZW5naW5lX2V4dGVuc2lvbiI6InVua25vd24iLCJhdGxhcy5yZWdpb24iOiJ0ZC1jYS0xIiwiYXRsYXMucHJvdmlkZXJfcmVnaW9uIjoiY2EtY2VudHJhbC0xIiwiYXRsYXMuYWNjb3VudF9hZmZpbml0eSI6ImNhLWNlbnRyYWwtMSIsImF0bGFzLnVzZXJfYWdlbnQiOiJNb3ppbGxhLzUuMCAoV2luZG93cyBOVCAxMC4wOyBXaW42NDsgeDY0KSBBcHBsZVdlYktpdC81MzcuMzYgKEtIVE1MLCBsaWtlIEdlY2tvKSBDaHJvbWUvMTMxLjAuNjc3OC4zMyBTYWZhcmkvNTM3LjM2IiwiYXRsYXMuZW52IjoicHJkIiwiYXRsYXMuYXBwX3NsdWciOiJvdXRib3VuZC1kaWFsZXIiLCJhdGxhcy5vd25lciI6ImNvbnRpbnVvdXNlbmctZGVldGluZyJ9fQ%3D%3D&nonce=ab43101f-24f0-4e62-94eb-439922cfe81e&targetOrigin=https%3A%2F%2Fpo-automationca.mytalkdeskca.com&depth=1","lineNumber":0,"columnNumber":0},"time":91457.82,"pageId":"page@8583ad047eb24e68c5c391e2c36584ed"}
{"type":"console","messageType":"warning","text":"An iframe which has both allow-scripts and allow-same-origin for its sandbox attribute can escape its sandboxing.","args":[],"location":{"url":"https://conversationapp.svc.talkdeskapp.com/conversation-app/runtime/3.37.0/runtime/index.html?monitoringConfig=eyJwcm92aWRlciI6ImR5bmF0cmFjZS1vdGxwIiwidXJsIjoiaHR0cHM6Ly90YWxrZGVzay1wcm9kLWNhLTEubGl2ZS5keW5hdHJhY2UuY29tL2FwaS92Mi9vdGxwIiwidG9rZW4iOiJkdDBjMDEuQjQ2STJJM1ZFSUhPSU5BVUFWQ1VHVUJHLkpDRVZJNVBBSzdYSjdJN0laQ0pMUVNPV1pTUzZEUUxVV0JVQVVNVDRZMjVIVUZJUk5WMkVOVzNBVjdETjRBTDMiLCJwcm9wZXJ0aWVzIjp7Im5hbWUiOiJjb252ZXJzYXRpb24tcHJkLXRkLWNhLTEtY2EtY2VudHJhbC0xIiwiYmF0Y2hEZWxheSI6NTAwMCwibWV0cmljcyI6eyJpbnRlcnZhbCI6MTAwMDB9LCJpZ25vcmVVcmxzIjpbImV1XFwuanNcXC5sb2dzXFwuaW5zaWdodFxcLnJhcGlkN1xcLmNvbSIsInJcXC5sci1pblxcLmNvbSIsInJcXC5pbnRha2UtbHJcXC5jb20iLCJldmVudGd3XFwudHdpbGlvXFwuY29tIl0sInByb3BhZ2F0ZUhlYWRlclVybHMiOlsiYXBpXFwudGFsa2Rlc2thcHBjYVxcLmNvbSJdfSwiYXR0cmlidXRlcyI6eyJhdGxhcy51c2VyX2lkIjoiNjM3YjZkMzAxZjA1Y2I1Yzk1MDhiN2RjIiwiYXRsYXMudGVuYW50X2lkIjoiNjM3YjY4M2QyNGU1NTYyMmQ0ODk5MjU3IiwiYXRsYXMudGVuYW50X25hbWUiOiJwby1hdXRvbWF0aW9uY2EiLCJhdGxhcy52ZXJzaW9uIjoiMC4xNzYuMTIiLCJhdGxhcy5zZXNzaW9uX3RpZCI6IjA1NzljOTllLTI0NDEtNGU5Mi1hYTFiLTkxZmY4NDIxOTcwZiIsImF0bGFzLmVuZ2luZV90eXBlIjoid2ViIiwiYXRsYXMuZW5naW5lX3ZlcnNpb24iOiIxMzEuMC42Nzc4LjMzIiwiYXRsYXMuZW5naW5lX2V4dGVuc2lvbiI6InVua25vd24iLCJhdGxhcy5yZWdpb24iOiJ0ZC1jYS0xIiwiYXRsYXMucHJvdmlkZXJfcmVnaW9uIjoiY2EtY2VudHJhbC0xIiwiYXRsYXMuYWNjb3VudF9hZmZpbml0eSI6ImNhLWNlbnRyYWwtMSIsImF0bGFzLnVzZXJfYWdlbnQiOiJNb3ppbGxhLzUuMCAoV2luZG93cyBOVCAxMC4wOyBXaW42NDsgeDY0KSBBcHBsZVdlYktpdC81MzcuMzYgKEtIVE1MLCBsaWtlIEdlY2tvKSBDaHJvbWUvMTMxLjAuNjc3OC4zMyBTYWZhcmkvNTM3LjM2IiwiYXRsYXMuZW52IjoicHJkIiwiYXRsYXMuYXBwX3NsdWciOiJjb252ZXJzYXRpb24iLCJhdGxhcy5vd25lciI6ImNvbnZlcnNhdGlvbi1jb3JlIn19&nonce=a7b2f96b-b620-4d32-be75-292b10d848bd&targetOrigin=https%3A%2F%2Fpo-automationca.mytalkdeskca.com&depth=1","lineNumber":0,"columnNumber":0},"time":91462.77,"pageId":"page@8583ad047eb24e68c5c391e2c36584ed"}
{"type":"console","messageType":"warning","text":"An iframe which has both allow-scripts and allow-same-origin for its sandbox attribute can escape its sandboxing.","args":[],"location":{"url":"https://prd-cdn-talkdesk.talkdesk.com/ur-app/1.19.0/index.html?monitoringConfig=eyJwcm92aWRlciI6ImR5bmF0cmFjZS1vdGxwIiwidXJsIjoiaHR0cHM6Ly90YWxrZGVzay1wcm9kLWNhLTEubGl2ZS5keW5hdHJhY2UuY29tL2FwaS92Mi9vdGxwIiwidG9rZW4iOiJkdDBjMDEuQjQ2STJJM1ZFSUhPSU5BVUFWQ1VHVUJHLkpDRVZJNVBBSzdYSjdJN0laQ0pMUVNPV1pTUzZEUUxVV0JVQVVNVDRZMjVIVUZJUk5WMkVOVzNBVjdETjRBTDMiLCJwcm9wZXJ0aWVzIjp7Im5hbWUiOiJ1ci1hcHAtcHJkLXRkLWNhLTEtY2EtY2VudHJhbC0xIiwiYmF0Y2hEZWxheSI6NTAwMCwibWV0cmljcyI6eyJpbnRlcnZhbCI6MTAwMDB9LCJpZ25vcmVVcmxzIjpbImV1XFwuanNcXC5sb2dzXFwuaW5zaWdodFxcLnJhcGlkN1xcLmNvbSIsInJcXC5sci1pblxcLmNvbSIsInJcXC5pbnRha2UtbHJcXC5jb20iLCJldmVudGd3XFwudHdpbGlvXFwuY29tIl0sInByb3BhZ2F0ZUhlYWRlclVybHMiOlsiYXBpXFwudGFsa2Rlc2thcHBjYVxcLmNvbSJdfSwiYXR0cmlidXRlcyI6eyJhdGxhcy51c2VyX2lkIjoiNjM3YjZkMzAxZjA1Y2I1Yzk1MDhiN2RjIiwiYXRsYXMudGVuYW50X2lkIjoiNjM3YjY4M2QyNGU1NTYyMmQ0ODk5MjU3IiwiYXRsYXMudGVuYW50X25hbWUiOiJwby1hdXRvbWF0aW9uY2EiLCJhdGxhcy52ZXJzaW9uIjoiMC4xNzYuMTIiLCJhdGxhcy5zZXNzaW9uX3RpZCI6IjA1NzljOTllLTI0NDEtNGU5Mi1hYTFiLTkxZmY4NDIxOTcwZiIsImF0bGFzLmVuZ2luZV90eXBlIjoid2ViIiwiYXRsYXMuZW5naW5lX3ZlcnNpb24iOiIxMzEuMC42Nzc4LjMzIiwiYXRsYXMuZW5naW5lX2V4dGVuc2lvbiI6InVua25vd24iLCJhdGxhcy5yZWdpb24iOiJ0ZC1jYS0xIiwiYXRsYXMucHJvdmlkZXJfcmVnaW9uIjoiY2EtY2VudHJhbC0xIiwiYXRsYXMuYWNjb3VudF9hZmZpbml0eSI6ImNhLWNlbnRyYWwtMSIsImF0bGFzLnVzZXJfYWdlbnQiOiJNb3ppbGxhLzUuMCAoV2luZG93cyBOVCAxMC4wOyBXaW42NDsgeDY0KSBBcHBsZVdlYktpdC81MzcuMzYgKEtIVE1MLCBsaWtlIEdlY2tvKSBDaHJvbWUvMTMxLjAuNjc3OC4zMyBTYWZhcmkvNTM3LjM2IiwiYXRsYXMuZW52IjoicHJkIiwiYXRsYXMuYXBwX3NsdWciOiJ1ci1hcHAiLCJhdGxhcy5vd25lciI6Indmby1lY2hvIn19&nonce=ad2c66d1-764f-476b-b0b4-36867062d420&targetOrigin=https%3A%2F%2Fpo-automationca.mytalkdeskca.com&depth=1","lineNumber":0,"columnNumber":0},"time":91467.103,"pageId":"page@8583ad047eb24e68c5c391e2c36584ed"}
{"type":"console","messageType":"warning","text":"An iframe which has both allow-scripts and allow-same-origin for its sandbox attribute can escape its sandboxing.","args":[],"location":{"url":"https://prd-cdn-talkdesk.talkdesk.com/ai-agent-assist-frontoffice/3.27.6/secondary/index.html?monitoringConfig=eyJwcm92aWRlciI6ImR5bmF0cmFjZS1vdGxwIiwidXJsIjoiaHR0cHM6Ly90YWxrZGVzay1wcm9kLWNhLTEubGl2ZS5keW5hdHJhY2UuY29tL2FwaS92Mi9vdGxwIiwidG9rZW4iOiJkdDBjMDEuQjQ2STJJM1ZFSUhPSU5BVUFWQ1VHVUJHLkpDRVZJNVBBSzdYSjdJN0laQ0pMUVNPV1pTUzZEUUxVV0JVQVVNVDRZMjVIVUZJUk5WMkVOVzNBVjdETjRBTDMiLCJwcm9wZXJ0aWVzIjp7Im5hbWUiOiJhZ2VudC1hc3Npc3Qtc2Vjb25kYXJ5LXByZC10ZC1jYS0xLWNhLWNlbnRyYWwtMSIsImJhdGNoRGVsYXkiOjUwMDAsIm1ldHJpY3MiOnsiaW50ZXJ2YWwiOjEwMDAwfSwiaWdub3JlVXJscyI6WyJldVxcLmpzXFwubG9nc1xcLmluc2lnaHRcXC5yYXBpZDdcXC5jb20iLCJyXFwubHItaW5cXC5jb20iLCJyXFwuaW50YWtlLWxyXFwuY29tIiwiZXZlbnRnd1xcLnR3aWxpb1xcLmNvbSJdLCJwcm9wYWdhdGVIZWFkZXJVcmxzIjpbImFwaVxcLnRhbGtkZXNrYXBwY2FcXC5jb20iXX0sImF0dHJpYnV0ZXMiOnsiYXRsYXMudXNlcl9pZCI6IjYzN2I2ZDMwMWYwNWNiNWM5NTA4YjdkYyIsImF0bGFzLnRlbmFudF9pZCI6IjYzN2I2ODNkMjRlNTU2MjJkNDg5OTI1NyIsImF0bGFzLnRlbmFudF9uYW1lIjoicG8tYXV0b21hdGlvbmNhIiwiYXRsYXMudmVyc2lvbiI6IjAuMTc2LjEyIiwiYXRsYXMuc2Vzc2lvbl90aWQiOiIwNTc5Yzk5ZS0yNDQxLTRlOTItYWExYi05MWZmODQyMTk3MGYiLCJhdGxhcy5lbmdpbmVfdHlwZSI6IndlYiIsImF0bGFzLmVuZ2luZV92ZXJzaW9uIjoiMTMxLjAuNjc3OC4zMyIsImF0bGFzLmVuZ2luZV9leHRlbnNpb24iOiJ1bmtub3duIiwiYXRsYXMucmVnaW9uIjoidGQtY2EtMSIsImF0bGFzLnByb3ZpZGVyX3JlZ2lvbiI6ImNhLWNlbnRyYWwtMSIsImF0bGFzLmFjY291bnRfYWZmaW5pdHkiOiJjYS1jZW50cmFsLTEiLCJhdGxhcy51c2VyX2FnZW50IjoiTW96aWxsYS81LjAgKFdpbmRvd3MgTlQgMTAuMDsgV2luNjQ7IHg2NCkgQXBwbGVXZWJLaXQvNTM3LjM2IChLSFRNTCwgbGlrZSBHZWNrbykgQ2hyb21lLzEzMS4wLjY3NzguMzMgU2FmYXJpLzUzNy4zNiIsImF0bGFzLmVudiI6InByZCIsImF0bGFzLmFwcF9zbHVnIjoiYWdlbnQtYXNzaXN0LXNlY29uZGFyeSIsImF0bGFzLm93bmVyIjoiYWktZW5naW5lZXJpbmcifX0%3D&nonce=79d560a2-ceb7-44d0-8a77-3252f9719a9d&targetOrigin=https%3A%2F%2Fpo-automationca.mytalkdeskca.com&depth=1","lineNumber":0,"columnNumber":0},"time":91562.815,"pageId":"page@8583ad047eb24e68c5c391e2c36584ed"}
{"type":"console","messageType":"warning","text":"[zustand devtools middleware]: `serialize.options` is deprecated, just use `serialize`","args":[{"preview":"[zustand devtools middleware]: `serialize.options` is deprecated, just use `serialize`","value":"[zustand devtools middleware]: `serialize.options` is deprecated, just use `serialize`"}],"location":{"url":"https://prd-cdn-talkdesk.talkdesk.com/ai-agent-assist-frontoffice/3.27.6/secondary/main.c97841f3dbea25029da4.js","lineNumber":2175,"columnNumber":110505},"time":92587.832,"pageId":"page@8583ad047eb24e68c5c391e2c36584ed"}
{"type":"console","messageType":"warning","text":"[zustand devtools middleware] Please install/enable Redux devtools extension","args":[{"preview":"[zustand devtools middleware] Please install/enable Redux devtools extension","value":"[zustand devtools middleware] Please install/enable Redux devtools extension"}],"location":{"url":"https://prd-cdn-talkdesk.talkdesk.com/ai-agent-assist-frontoffice/3.27.6/secondary/main.c97841f3dbea25029da4.js","lineNumber":2175,"columnNumber":110739},"time":92587.949,"pageId":"page@8583ad047eb24e68c5c391e2c36584ed"}
{"type":"console","messageType":"warning","text":"Warning: You are attempting to use a basename on a page whose URL path does not begin with the basename. Expected path \"/ai-agent-assist-frontoffice/3.27.6/secondary/index.html?monitoringConfig=eyJwcm92aWRlciI6ImR5bmF0cmFjZS1vdGxwIiwidXJsIjoiaHR0cHM6Ly90YWxrZGVzay1wcm9kLWNhLTEubGl2ZS5keW5hdHJhY2UuY29tL2FwaS92Mi9vdGxwIiwidG9rZW4iOiJkdDBjMDEuQjQ2STJJM1ZFSUhPSU5BVUFWQ1VHVUJHLkpDRVZJNVBBSzdYSjdJN0laQ0pMUVNPV1pTUzZEUUxVV0JVQVVNVDRZMjVIVUZJUk5WMkVOVzNBVjdETjRBTDMiLCJwcm9wZXJ0aWVzIjp7Im5hbWUiOiJhZ2VudC1hc3Npc3Qtc2Vjb25kYXJ5LXByZC10ZC1jYS0xLWNhLWNlbnRyYWwtMSIsImJhdGNoRGVsYXkiOjUwMDAsIm1ldHJpY3MiOnsiaW50ZXJ2YWwiOjEwMDAwfSwiaWdub3JlVXJscyI6WyJldVxcLmpzXFwubG9nc1xcLmluc2lnaHRcXC5yYXBpZDdcXC5jb20iLCJyXFwubHItaW5cXC5jb20iLCJyXFwuaW50YWtlLWxyXFwuY29tIiwiZXZlbnRnd1xcLnR3aWxpb1xcLmNvbSJdLCJwcm9wYWdhdGVIZWFkZXJVcmxzIjpbImFwaVxcLnRhbGtkZXNrYXBwY2FcXC5jb20iXX0sImF0dHJpYnV0ZXMiOnsiYXRsYXMudXNlcl9pZCI6IjYzN2I2ZDMwMWYwNWNiNWM5NTA4YjdkYyIsImF0bGFzLnRlbmFudF9pZCI6IjYzN2I2ODNkMjRlNTU2MjJkNDg5OTI1NyIsImF0bGFzLnRlbmFudF9uYW1lIjoicG8tYXV0b21hdGlvbmNhIiwiYXRsYXMudmVyc2lvbiI6IjAuMTc2LjEyIiwiYXRsYXMuc2Vzc2lvbl90aWQiOiIwNTc5Yzk5ZS0yNDQxLTRlOTItYWExYi05MWZmODQyMTk3MGYiLCJhdGxhcy5lbmdpbmVfdHlwZSI6IndlYiIsImF0bGFzLmVuZ2luZV92ZXJzaW9uIjoiMTMxLjAuNjc3OC4zMyIsImF0bGFzLmVuZ2luZV9leHRlbnNpb24iOiJ1bmtub3duIiwiYXRsYXMucmVnaW9uIjoidGQtY2EtMSIsImF0bGFzLnByb3ZpZGVyX3JlZ2lvbiI6ImNhLWNlbnRyYWwtMSIsImF0bGFzLmFjY291bnRfYWZmaW5pdHkiOiJjYS1jZW50cmFsLTEiLCJhdGxhcy51c2VyX2FnZW50IjoiTW96aWxsYS81LjAgKFdpbmRvd3MgTlQgMTAuMDsgV2luNjQ7IHg2NCkgQXBwbGVXZWJLaXQvNTM3LjM2IChLSFRNTCwgbGlrZSBHZWNrbykgQ2hyb21lLzEzMS4wLjY3NzguMzMgU2FmYXJpLzUzNy4zNiIsImF0bGFzLmVudiI6InByZCIsImF0bGFzLmFwcF9zbHVnIjoiYWdlbnQtYXNzaXN0LXNlY29uZGFyeSIsImF0bGFzLm93bmVyIjoiYWktZW5naW5lZXJpbmcifX0%3D&nonce=79d560a2-ceb7-44d0-8a77-3252f9719a9d&targetOrigin=https%3A%2F%2Fpo-automationca.mytalkdeskca.com&depth=1\" to begin with \"/ai-agent-assist-frontoffice/latest/secondary\".","args":[{"preview":"Warning: You are attempting to use a basename on a page whose URL path does not begin with the basename. Expected path \"/ai-agent-assist-frontoffice/3.27.6/secondary/index.html?monitoringConfig=eyJwcm92aWRlciI6ImR5bmF0cmFjZS1vdGxwIiwidXJsIjoiaHR0cHM6Ly90YWxrZGVzay1wcm9kLWNhLTEubGl2ZS5keW5hdHJhY2UuY29tL2FwaS92Mi9vdGxwIiwidG9rZW4iOiJkdDBjMDEuQjQ2STJJM1ZFSUhPSU5BVUFWQ1VHVUJHLkpDRVZJNVBBSzdYSjdJN0laQ0pMUVNPV1pTUzZEUUxVV0JVQVVNVDRZMjVIVUZJUk5WMkVOVzNBVjdETjRBTDMiLCJwcm9wZXJ0aWVzIjp7Im5hbWUiOiJhZ2VudC1hc3Npc3Qtc2Vjb25kYXJ5LXByZC10ZC1jYS0xLWNhLWNlbnRyYWwtMSIsImJhdGNoRGVsYXkiOjUwMDAsIm1ldHJpY3MiOnsiaW50ZXJ2YWwiOjEwMDAwfSwiaWdub3JlVXJscyI6WyJldVxcLmpzXFwubG9nc1xcLmluc2lnaHRcXC5yYXBpZDdcXC5jb20iLCJyXFwubHItaW5cXC5jb20iLCJyXFwuaW50YWtlLWxyXFwuY29tIiwiZXZlbnRnd1xcLnR3aWxpb1xcLmNvbSJdLCJwcm9wYWdhdGVIZWFkZXJVcmxzIjpbImFwaVxcLnRhbGtkZXNrYXBwY2FcXC5jb20iXX0sImF0dHJpYnV0ZXMiOnsiYXRsYXMudXNlcl9pZCI6IjYzN2I2ZDMwMWYwNWNiNWM5NTA4YjdkYyIsImF0bGFzLnRlbmFudF9pZCI6IjYzN2I2ODNkMjRlNTU2MjJkNDg5OTI1NyIsImF0bGFzLnRlbmFudF9uYW1lIjoicG8tYXV0b21hdGlvbmNhIiwiYXRsYXMudmVyc2lvbiI6IjAuMTc2LjEyIiwiYXRsYXMuc2Vzc2lvbl90aWQiOiIwNTc5Yzk5ZS0yNDQxLTRlOTItYWExYi05MWZmODQyMTk3MGYiLCJhdGxhcy5lbmdpbmVfdHlwZSI6IndlYiIsImF0bGFzLmVuZ2luZV92ZXJzaW9uIjoiMTMxLjAuNjc3OC4zMyIsImF0bGFzLmVuZ2luZV9leHRlbnNpb24iOiJ1bmtub3duIiwiYXRsYXMucmVnaW9uIjoidGQtY2EtMSIsImF0bGFzLnByb3ZpZGVyX3JlZ2lvbiI6ImNhLWNlbnRyYWwtMSIsImF0bGFzLmFjY291bnRfYWZmaW5pdHkiOiJjYS1jZW50cmFsLTEiLCJhdGxhcy51c2VyX2FnZW50IjoiTW96aWxsYS81LjAgKFdpbmRvd3MgTlQgMTAuMDsgV2luNjQ7IHg2NCkgQXBwbGVXZWJLaXQvNTM3LjM2IChLSFRNTCwgbGlrZSBHZWNrbykgQ2hyb21lLzEzMS4wLjY3NzguMzMgU2FmYXJpLzUzNy4zNiIsImF0bGFzLmVudiI6InByZCIsImF0bGFzLmFwcF9zbHVnIjoiYWdlbnQtYXNzaXN0LXNlY29uZGFyeSIsImF0bGFzLm93bmVyIjoiYWktZW5naW5lZXJpbmcifX0%3D&nonce=79d560a2-ceb7-44d0-8a77-3252f9719a9d&targetOrigin=https%3A%2F%2Fpo-automationca.mytalkdeskca.com&depth=1\" to begin with \"/ai-agent-assist-frontoffice/latest/secondary\".","value":"Warning: You are attempting to use a basename on a page whose URL path does not begin with the basename. Expected path \"/ai-agent-assist-frontoffice/3.27.6/secondary/index.html?monitoringConfig=eyJwcm92aWRlciI6ImR5bmF0cmFjZS1vdGxwIiwidXJsIjoiaHR0cHM6Ly90YWxrZGVzay1wcm9kLWNhLTEubGl2ZS5keW5hdHJhY2UuY29tL2FwaS92Mi9vdGxwIiwidG9rZW4iOiJkdDBjMDEuQjQ2STJJM1ZFSUhPSU5BVUFWQ1VHVUJHLkpDRVZJNVBBSzdYSjdJN0laQ0pMUVNPV1pTUzZEUUxVV0JVQVVNVDRZMjVIVUZJUk5WMkVOVzNBVjdETjRBTDMiLCJwcm9wZXJ0aWVzIjp7Im5hbWUiOiJhZ2VudC1hc3Npc3Qtc2Vjb25kYXJ5LXByZC10ZC1jYS0xLWNhLWNlbnRyYWwtMSIsImJhdGNoRGVsYXkiOjUwMDAsIm1ldHJpY3MiOnsiaW50ZXJ2YWwiOjEwMDAwfSwiaWdub3JlVXJscyI6WyJldVxcLmpzXFwubG9nc1xcLmluc2lnaHRcXC5yYXBpZDdcXC5jb20iLCJyXFwubHItaW5cXC5jb20iLCJyXFwuaW50YWtlLWxyXFwuY29tIiwiZXZlbnRnd1xcLnR3aWxpb1xcLmNvbSJdLCJwcm9wYWdhdGVIZWFkZXJVcmxzIjpbImFwaVxcLnRhbGtkZXNrYXBwY2FcXC5jb20iXX0sImF0dHJpYnV0ZXMiOnsiYXRsYXMudXNlcl9pZCI6IjYzN2I2ZDMwMWYwNWNiNWM5NTA4YjdkYyIsImF0bGFzLnRlbmFudF9pZCI6IjYzN2I2ODNkMjRlNTU2MjJkNDg5OTI1NyIsImF0bGFzLnRlbmFudF9uYW1lIjoicG8tYXV0b21hdGlvbmNhIiwiYXRsYXMudmVyc2lvbiI6IjAuMTc2LjEyIiwiYXRsYXMuc2Vzc2lvbl90aWQiOiIwNTc5Yzk5ZS0yNDQxLTRlOTItYWExYi05MWZmODQyMTk3MGYiLCJhdGxhcy5lbmdpbmVfdHlwZSI6IndlYiIsImF0bGFzLmVuZ2luZV92ZXJzaW9uIjoiMTMxLjAuNjc3OC4zMyIsImF0bGFzLmVuZ2luZV9leHRlbnNpb24iOiJ1bmtub3duIiwiYXRsYXMucmVnaW9uIjoidGQtY2EtMSIsImF0bGFzLnByb3ZpZGVyX3JlZ2lvbiI6ImNhLWNlbnRyYWwtMSIsImF0bGFzLmFjY291bnRfYWZmaW5pdHkiOiJjYS1jZW50cmFsLTEiLCJhdGxhcy51c2VyX2FnZW50IjoiTW96aWxsYS81LjAgKFdpbmRvd3MgTlQgMTAuMDsgV2luNjQ7IHg2NCkgQXBwbGVXZWJLaXQvNTM3LjM2IChLSFRNTCwgbGlrZSBHZWNrbykgQ2hyb21lLzEzMS4wLjY3NzguMzMgU2FmYXJpLzUzNy4zNiIsImF0bGFzLmVudiI6InByZCIsImF0bGFzLmFwcF9zbHVnIjoiYWdlbnQtYXNzaXN0LXNlY29uZGFyeSIsImF0bGFzLm93bmVyIjoiYWktZW5naW5lZXJpbmcifX0%3D&nonce=79d560a2-ceb7-44d0-8a77-3252f9719a9d&targetOrigin=https%3A%2F%2Fpo-automationca.mytalkdeskca.com&depth=1\" to begin with \"/ai-agent-assist-frontoffice/latest/secondary\"."}],"location":{"url":"https://prd-cdn-talkdesk.talkdesk.com/ai-agent-assist-frontoffice/3.27.6/secondary/main.c97841f3dbea25029da4.js","lineNumber":2399,"columnNumber":2098},"time":92650.52,"pageId":"page@8583ad047eb24e68c5c391e2c36584ed"}
{"type":"console","messageType":"error","text":"Failed to load resource: the server responded with a status of 404 ()","args":[],"location":{"url":"https://prd-cdn-talkdesk.talkdesk.com/i18n/live/agent-assist-secondary/en-US/latest/translation.json","lineNumber":0,"columnNumber":0},"time":93412.502,"pageId":"page@8583ad047eb24e68c5c391e2c36584ed"}
{"type":"after","callId":"call@1251","endTime":93657.857,"afterSnapshot":"after@call@1251"}
{"type":"before","callId":"call@1253","startTime":93812.045,"apiName":"locator.isVisible","class":"Frame","method":"isVisible","params":{"selector":"div:not([style*=\"display: none\"])[data-testid=\"app\"] svg.loader-module__loader >> nth=0","strict":true},"stepId":"pw:api@12","pageId":"page@8583ad047eb24e68c5c391e2c36584ed","beforeSnapshot":"before@call@1253"}
{"type":"log","callId":"call@1253","time":93908.304,"message":"  checking visibility of locator('div:not([style*=\"display: none\"])[data-testid=\"app\"] svg.loader-module__loader').first()"}
{"type":"after","callId":"call@1253","endTime":93937.053,"result":{"value":false},"afterSnapshot":"after@call@1253"}
{"type":"before","callId":"call@1255","startTime":93945.908,"apiName":"page.evaluate","class":"Frame","method":"evaluateExpression","params":{"expression":"() => {\n      console.log(\"========START EXECUTE JQUERY=============\");\n      // let toastRoot = document.querySelector('div#toast');\n      const toastRoot = document.querySelector('div[data-testid=\"toaster\"]');\n      let MutationObserver = window.MutationObserver || window.WebKitMutationObserver || window.MozMutationObserver;\n      let mutationObserver = new MutationObserver(mutations => {\n        const toast = document.querySelector('div[data-testid=\"toaster\"] div[data-co-name=\"Message\"] div[data-testid]');\n        const toast_content = document.querySelector('div[data-testid=\"toaster\"] div[data-co-name=\"Message\"] div[data-testid] p').textContent;\n        console.log(toast);\n        if (toast_content.includes('Conversations is open in another tab or device')) {\n          console.log(\"=====find the conversation conflict toast======\");\n          toast.remove();\n        } else {\n          console.log(\"=======No need to fix this toast=======\");\n        }\n      });\n      mutationObserver.observe(toastRoot, {\n        childList: true,\n        //\n        // attributes: true, //\n        // characterData: true, //\n        subtree: true //\n        // attributesFilter: ['class', 'style'], //\n        // attributesOldValue: true, //\n        // characterDataOldValue: true //\n      });\n      console.log(\"========START EXECUTE JQUERY=============\");\n    }","isFunction":true,"arg":{"value":{"v":"undefined"},"handles":[]}},"stepId":"pw:api@13","pageId":"page@8583ad047eb24e68c5c391e2c36584ed","beforeSnapshot":"before@call@1255"}
{"type":"after","callId":"call@1255","endTime":93955.511,"result":{"value":{"v":"undefined"}},"afterSnapshot":"after@call@1255"}
{"type":"before","callId":"call@1257","startTime":93960.873,"apiName":"expect.toBeVisible","class":"Frame","method":"expect","params":{"selector":"header.dock-drawer-component-module__header h4","expression":"to.be.visible","expectedValue":{"value":{"v":"undefined"},"handles":[]},"isNot":false,"timeout":6000},"stepId":"expect@14","pageId":"page@8583ad047eb24e68c5c391e2c36584ed","beforeSnapshot":"before@call@1257"}
{"type":"log","callId":"call@1257","time":93964.748,"message":"expect.toBeVisible with timeout 6000ms"}
{"type":"log","callId":"call@1257","time":93964.757,"message":"waiting for locator('header.dock-drawer-component-module__header h4')"}
{"type":"log","callId":"call@1257","time":93966.983,"message":"  locator resolved to <h4 data-co-name=\"Heading\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-heading react-typography_2-0-1_co-heading--4\">Dialer</h4>"}
{"type":"after","callId":"call@1257","endTime":93967.043,"result":{"matches":true,"received":{"b":true}},"afterSnapshot":"after@call@1257"}
{"type":"before","callId":"call@1259","startTime":93972.635,"apiName":"locator.textContent","class":"Frame","method":"textContent","params":{"selector":"header.dock-drawer-component-module__header h4","strict":true},"stepId":"pw:api@15","pageId":"page@8583ad047eb24e68c5c391e2c36584ed","beforeSnapshot":"before@call@1259"}
{"type":"log","callId":"call@1259","time":93976.635,"message":"waiting for locator('header.dock-drawer-component-module__header h4')"}
{"type":"log","callId":"call@1259","time":93996.451,"message":"  locator resolved to <h4 data-co-name=\"Heading\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-heading react-typography_2-0-1_co-heading--4\">Dialer</h4>"}
{"type":"after","callId":"call@1259","endTime":93996.491,"result":{"value":"Dialer"},"afterSnapshot":"after@call@1259"}
{"type":"before","callId":"call@1261","startTime":94008.26,"apiName":"locator.isVisible","class":"Frame","method":"isVisible","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> header h1","strict":true},"stepId":"pw:api@17","pageId":"page@8583ad047eb24e68c5c391e2c36584ed","beforeSnapshot":"before@call@1261"}
{"type":"log","callId":"call@1261","time":94012.324,"message":"  checking visibility of locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('header h1')"}
{"type":"after","callId":"call@1261","endTime":94089.14,"result":{"value":true},"afterSnapshot":"after@call@1261"}
{"type":"before","callId":"call@1263","startTime":94114.92,"apiName":"locator.isVisible","class":"Frame","method":"isVisible","params":{"selector":"button.secondary-area-module__active","strict":true},"stepId":"pw:api@18","pageId":"page@8583ad047eb24e68c5c391e2c36584ed","beforeSnapshot":"before@call@1263"}
{"type":"log","callId":"call@1263","time":94138.731,"message":"  checking visibility of locator('button.secondary-area-module__active')"}
{"type":"after","callId":"call@1263","endTime":94210.254,"result":{"value":true},"afterSnapshot":"after@call@1263"}
{"type":"before","callId":"call@1265","startTime":94220.268,"apiName":"locator.isVisible","class":"Frame","method":"isVisible","params":{"selector":"div._pendo-step-container-size","strict":true},"stepId":"pw:api@19","pageId":"page@8583ad047eb24e68c5c391e2c36584ed","beforeSnapshot":"before@call@1265"}
{"type":"log","callId":"call@1265","time":94262.859,"message":"  checking visibility of locator('div._pendo-step-container-size')"}
{"type":"after","callId":"call@1265","endTime":94307.741,"result":{"value":false},"afterSnapshot":"after@call@1265"}
{"type":"before","callId":"call@1267","startTime":94331.21,"apiName":"locator.click","class":"Frame","method":"click","params":{"selector":"button.secondary-area-module__active","strict":true},"stepId":"pw:api@20","pageId":"page@8583ad047eb24e68c5c391e2c36584ed","beforeSnapshot":"before@call@1267"}
{"type":"log","callId":"call@1267","time":94351.984,"message":"waiting for locator('button.secondary-area-module__active')"}
{"type":"log","callId":"call@1267","time":94372.04,"message":"  locator resolved to <button data-co-name=\"Button\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" aria-label=\"Toggle secondary area\" data-testid=\"secondary-area-btn-toggle\" class=\"react-button_2-0-1_co-button react-button_2-0-1_co-button--secondary react-button_2-0-1_co-button--medium react-button_2-0-1_co-button--transparent secondary-area-module__active secondary-area-module__button\">…</button>"}
{"type":"log","callId":"call@1267","time":94388.853,"message":"attempting click action"}
{"type":"log","callId":"call@1267","time":94388.882,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1267","time":94410.048,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1267","time":94410.059,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1267","time":94410.687,"message":"  done scrolling"}
{"type":"console","messageType":"debug","text":"Returning value for CONVERSATION_APP_enable_digital_connect: false","args":[{"preview":"Returning value for CONVERSATION_APP_enable_digital_connect: false","value":"Returning value for CONVERSATION_APP_enable_digital_connect: false"}],"location":{"url":"https://conversationapp.svc.talkdeskapp.com/conversation-app/runtime/3.37.0/runtime/main.17805132c5224d076ed8.js","lineNumber":7,"columnNumber":251090},"time":94418.44,"pageId":"page@8583ad047eb24e68c5c391e2c36584ed"}
{"type":"console","messageType":"debug","text":"Returning value for CONVERSATION_APP_enable_fax: false","args":[{"preview":"Returning value for CONVERSATION_APP_enable_fax: false","value":"Returning value for CONVERSATION_APP_enable_fax: false"}],"location":{"url":"https://conversationapp.svc.talkdeskapp.com/conversation-app/runtime/3.37.0/runtime/main.17805132c5224d076ed8.js","lineNumber":7,"columnNumber":251090},"time":94418.53,"pageId":"page@8583ad047eb24e68c5c391e2c36584ed"}
{"type":"console","messageType":"debug","text":"Returning value for CONVERSATION_APP_enable_demo: false","args":[{"preview":"Returning value for CONVERSATION_APP_enable_demo: false","value":"Returning value for CONVERSATION_APP_enable_demo: false"}],"location":{"url":"https://conversationapp.svc.talkdeskapp.com/conversation-app/runtime/3.37.0/runtime/main.17805132c5224d076ed8.js","lineNumber":7,"columnNumber":251090},"time":94418.584,"pageId":"page@8583ad047eb24e68c5c391e2c36584ed"}
{"type":"console","messageType":"debug","text":"Returning value for CONVERSATION_APP_enable_whatsapp: false","args":[{"preview":"Returning value for CONVERSATION_APP_enable_whatsapp: false","value":"Returning value for CONVERSATION_APP_enable_whatsapp: false"}],"location":{"url":"https://conversationapp.svc.talkdeskapp.com/conversation-app/runtime/3.37.0/runtime/main.17805132c5224d076ed8.js","lineNumber":7,"columnNumber":251090},"time":94418.611,"pageId":"page@8583ad047eb24e68c5c391e2c36584ed"}
{"type":"console","messageType":"debug","text":"Returning value for DCE_Infobip_WhatsApp: false","args":[{"preview":"Returning value for DCE_Infobip_WhatsApp: false","value":"Returning value for DCE_Infobip_WhatsApp: false"}],"location":{"url":"https://conversationapp.svc.talkdeskapp.com/conversation-app/runtime/3.37.0/runtime/main.17805132c5224d076ed8.js","lineNumber":7,"columnNumber":251090},"time":94418.695,"pageId":"page@8583ad047eb24e68c5c391e2c36584ed"}
{"type":"console","messageType":"debug","text":"Returning value for DCE_Infobip_AMB: false","args":[{"preview":"Returning value for DCE_Infobip_AMB: false","value":"Returning value for DCE_Infobip_AMB: false"}],"location":{"url":"https://conversationapp.svc.talkdeskapp.com/conversation-app/runtime/3.37.0/runtime/main.17805132c5224d076ed8.js","lineNumber":7,"columnNumber":251090},"time":94418.812,"pageId":"page@8583ad047eb24e68c5c391e2c36584ed"}
{"type":"log","callId":"call@1267","time":94422.657,"message":"  performing click action"}
{"type":"log","callId":"call@1267","time":94488.378,"message":"  click action done"}
{"type":"log","callId":"call@1267","time":94488.386,"message":"  waiting for scheduled navigations to finish"}
{"type":"console","messageType":"warning","text":"Unsatisfied version 0.46.1 from @conversation-app/runtime of shared singleton module @atlas/sdk (required =0.46.2)","args":[{"preview":"Unsatisfied version 0.46.1 from @conversation-app/runtime of shared singleton module @atlas/sdk (required =0.46.2)","value":"Unsatisfied version 0.46.1 from @conversation-app/runtime of shared singleton module @atlas/sdk (required =0.46.2)"}],"location":{"url":"https://conversationapp.svc.talkdeskapp.com/conversation-app/voice-channel/1.48.0/voice-channel/remoteEntry.js","lineNumber":0,"columnNumber":6115},"time":94491.708,"pageId":"page@8583ad047eb24e68c5c391e2c36584ed"}
{"type":"log","callId":"call@1267","time":94501.151,"message":"  navigations have finished"}
{"type":"after","callId":"call@1267","endTime":94501.271,"point":{"x":1260,"y":20},"afterSnapshot":"after@call@1267"}
{"type":"before","callId":"call@1269","startTime":94788.213,"apiName":"locator.isVisible","class":"Frame","method":"isVisible","params":{"selector":"div[data-testid=\"toaster\"] div[data-co-name=\"Message\"]:nth-child(1) div[data-testid]","strict":true},"stepId":"pw:api@21","pageId":"page@8583ad047eb24e68c5c391e2c36584ed","beforeSnapshot":"before@call@1269"}
{"type":"log","callId":"call@1269","time":94797.195,"message":"  checking visibility of locator('div[data-testid=\"toaster\"] div[data-co-name=\"Message\"]:nth-child(1) div[data-testid]')"}
{"type":"after","callId":"call@1269","endTime":94802.107,"result":{"value":false},"afterSnapshot":"after@call@1269"}
{"type":"before","callId":"call@1271","startTime":94809.196,"apiName":"locator.click","class":"Frame","method":"click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div button[data-pendo-campaignlist-header-createbutton]","strict":true},"stepId":"pw:api@22","pageId":"page@8583ad047eb24e68c5c391e2c36584ed","beforeSnapshot":"before@call@1271"}
{"type":"log","callId":"call@1271","time":94813.965,"message":"waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div button[data-pendo-campaignlist-header-createbutton]')"}
{"type":"log","callId":"call@1271","time":94817.253,"message":"  locator resolved to <button data-co-name=\"Button\" data-co-version=\"1.4.0\" data-co-project=\"cobalt\" data-pendo-campaignlist-header-createbutton=\"true\" class=\"react-button_1-4-0_co-button react-button_1-4-0_co-button--primary react-button_1-4-0_co-button--medium\">Create campaign</button>"}
{"type":"log","callId":"call@1271","time":94817.763,"message":"attempting click action"}
{"type":"log","callId":"call@1271","time":94817.978,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":94843.321,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":94843.329,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":94851.963,"message":"  done scrolling"}
{"type":"event","time":94860.746,"class":"BrowserContext","method":"pageError","params":{"error":{"error":{"message":"Cannot read properties of null (reading 'textContent')","stack":"TypeError: Cannot read properties of null (reading 'textContent')\n    at MutationObserver.eval (eval at evaluate (:234:30), <anonymous>:8:130)","name":"TypeError"}}},"pageId":"page@8583ad047eb24e68c5c391e2c36584ed"}
{"type":"log","callId":"call@1271","time":94871.279,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":94871.289,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":94871.806,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":94892.886,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":94892.895,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":94893.658,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":94904.16,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":94904.17,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":94904.173,"message":"  waiting 20ms"}
{"type":"log","callId":"call@1271","time":94925.243,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":94942.812,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":94942.823,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":94943.309,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":94953.495,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":94953.504,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":94953.508,"message":"  waiting 100ms"}
{"type":"log","callId":"call@1271","time":95054.725,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":95076.525,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":95076.534,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":95077.055,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":95084.979,"message":"  <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":95084.989,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":95084.992,"message":"  waiting 100ms"}
{"type":"log","callId":"call@1271","time":95185.742,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":95209.509,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":95209.521,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":95209.851,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":95218.402,"message":"  <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":95218.415,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":95218.42,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":95719.603,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":95742.69,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":95742.703,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":95744.333,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":95752.39,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":95752.4,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":95752.403,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":96253.707,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":96259.155,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":96259.164,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":96259.707,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":96267.852,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":96267.866,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":96267.871,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":96769.109,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":96792.612,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":96792.623,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":96793.142,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":96801.2,"message":"  <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":96801.21,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":96801.213,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":97302.108,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":97309.252,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":97309.263,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":97309.52,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":97317.297,"message":"  <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":97317.307,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":97317.309,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":97818.396,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":97842.614,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":97842.63,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":97844.798,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":97852.785,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":97852.795,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":97852.798,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":98354,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":98359.072,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":98359.082,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":98359.557,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":98367.631,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":98367.643,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":98367.646,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":98869.035,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":98892.486,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":98892.496,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":98893.07,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":98901.46,"message":"  <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":98901.471,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":98901.473,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":99402.507,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":99409.119,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":99409.129,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":99409.384,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":99417.82,"message":"  <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":99417.83,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":99417.833,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":99919.093,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":99942.421,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":99942.432,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":99943.683,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":99952.466,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":99952.477,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":99952.48,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":100453.798,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":100459.023,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":100459.031,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":100459.635,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":100468.575,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":100468.585,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":100468.588,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":100969.859,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":100992.448,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":100992.46,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":100993.072,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":101001.459,"message":"  <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":101001.469,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":101001.472,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":101502.413,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":101509.011,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":101509.019,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":101509.324,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":101516.94,"message":"  <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":101516.949,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":101516.952,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":102018.447,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":102042.464,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":102042.476,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":102043.091,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":102051.631,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":102051.641,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":102051.644,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":102552.914,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":102558.885,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":102558.893,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":102559.359,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":102566.794,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":102566.805,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":102566.807,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":103067.87,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":103092.344,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":103092.359,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":103093.087,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":103156.445,"message":"  <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":103156.46,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":103156.463,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":103657.483,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":103658.876,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":103658.884,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":103659.092,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":103668.295,"message":"  <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":103668.305,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":103668.308,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":104169.616,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":104192.53,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":104192.545,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":104193.134,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":104202.436,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":104202.447,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":104202.45,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":104703.692,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":104725.525,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":104725.534,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":104725.979,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":104734.061,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":104734.071,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":104734.074,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":105235.284,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":105258.858,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":105258.869,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":105259.441,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":105267.787,"message":"  <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":105267.8,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":105267.804,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":105768.8,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":105775.52,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":105775.528,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":105775.767,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":105785.458,"message":"  <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":105785.468,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":105785.471,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":106286.971,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":106308.853,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":106308.864,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":106309.42,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":106320.007,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":106320.017,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":106320.027,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":106821.324,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":106825.372,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":106825.381,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":106825.844,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":106836.811,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":106836.821,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":106836.824,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":107338.115,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":107358.88,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":107358.892,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":107359.517,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":107369.649,"message":"  <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":107369.659,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":107369.662,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":107870.641,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":107875.428,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":107875.439,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":107875.669,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":107883.726,"message":"  <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":107883.736,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":107883.738,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":108384.963,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":108408.823,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":108408.834,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":108409.577,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":108422.101,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":108422.111,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":108422.115,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":108923.429,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":108925.347,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":108925.355,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":108925.834,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":108934.922,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":108934.935,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":108934.94,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":109436.279,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":109458.794,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":109458.806,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":109459.435,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":109467.37,"message":"  <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":109467.38,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":109467.383,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":109968.383,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":109975.395,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":109975.403,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":109975.639,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":109985.084,"message":"  <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":109985.094,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":109985.097,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":110486.402,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":110508.702,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":110508.714,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":110509.311,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":110519.273,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":110519.285,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":110519.289,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":111020.546,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":111025.301,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":111025.31,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":111025.813,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":111033.933,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":111033.942,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":111033.945,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":111535.256,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":111558.646,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":111558.66,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":111559.352,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":111568.163,"message":"  <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":111568.21,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":111568.213,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":112069.255,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":112075.31,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":112075.318,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":112075.555,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":112083.625,"message":"  <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":112083.634,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":112083.637,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":112584.95,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":112608.717,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":112608.734,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":112609.473,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":112618.889,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":112618.899,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":112618.903,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":113120.869,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":113125.138,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":113125.146,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":113125.606,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":113133.449,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":113133.458,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":113133.46,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":113634.802,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":113658.588,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":113658.598,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":113659.218,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":113668.06,"message":"  <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":113668.075,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":113668.079,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":114169.162,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":114175.262,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":114175.27,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":114175.559,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":114185.809,"message":"  <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":114185.821,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":114185.825,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":114687.195,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":114708.495,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":114708.506,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":114709.249,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":114719.573,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":114719.586,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":114719.591,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":115221.379,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":115225.059,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":115225.067,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":115225.537,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":115233.654,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":115233.663,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":115233.666,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":115734.974,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":115758.46,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":115758.468,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":115759.033,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":115767.114,"message":"  <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":115767.123,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":115767.126,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":116268.463,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":116275.101,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":116275.108,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":116275.363,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":116283.224,"message":"  <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":116283.233,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":116283.235,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":116784.512,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":116808.464,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":116808.473,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":116808.994,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":116816.945,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":116816.954,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":116816.957,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":117318.014,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":117325.037,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":117325.044,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":117325.507,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":117337.281,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":117337.292,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":117337.294,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":117838.936,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":117858.4,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":117858.408,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":117858.977,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":117868.84,"message":"  <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":117868.852,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":117868.857,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":118369.798,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":118374.956,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":118374.964,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":118375.234,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":118384.013,"message":"  <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":118384.021,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":118384.024,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":118885.24,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":118908.346,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":118908.355,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":118908.861,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":118917.416,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":118917.425,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":118917.428,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":119418.68,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":119425.007,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":119425.014,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":119425.52,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":119435.222,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":119435.231,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":119435.234,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":119936.503,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":119958.341,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":119958.351,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":119958.888,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":119966.715,"message":"  <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":119966.723,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":119966.726,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":120467.834,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":120475.08,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":120475.088,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":120475.371,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":120483.807,"message":"  <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":120483.819,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":120483.823,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":120985.13,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":121008.224,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":121008.232,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":121008.785,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":121017.356,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":121017.366,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":121017.368,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":121518.613,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":121524.812,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":121524.82,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":121525.317,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":121533.674,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":121533.684,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":121533.686,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":122034.844,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":122058.158,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":122058.166,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":122058.685,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":122067.613,"message":"  <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":122067.624,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":122067.627,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":122568.593,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":122574.839,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":122574.848,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":122575.102,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":122583.642,"message":"  <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":122583.654,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":122583.658,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":123084.853,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":123108.18,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":123108.192,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":123108.777,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":123117.135,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":123117.147,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":123117.151,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":123618.229,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":123624.697,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":123624.704,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":123625.141,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":123633.544,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":123633.556,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":123633.559,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":124134.844,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":124158.148,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":124158.16,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":124158.752,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":124167.594,"message":"  <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":124167.603,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":124167.605,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":124668.541,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":124674.952,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":124674.959,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":124675.391,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":124683.826,"message":"  <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":124683.835,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":124683.837,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":125185.044,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":125208.087,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":125208.098,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":125208.644,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":125216.515,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":125216.526,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":125216.53,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":125717.687,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":125724.849,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":125724.858,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":125725.398,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":125736.044,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":125736.053,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":125736.055,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":126237.241,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":126258.115,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":126258.126,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":126258.681,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":126267.75,"message":"  <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":126267.759,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":126267.761,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":126768.678,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":126774.651,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":126774.659,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":126774.854,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":126784.863,"message":"  <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":126784.872,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":126784.874,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":127286.023,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":127308.044,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":127308.054,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":127308.683,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":127318.736,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":127318.748,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":127318.751,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":127819.908,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":127824.581,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":127824.588,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":127825.063,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":127834.903,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":127834.912,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":127834.914,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":128336.353,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":128358.036,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":128358.047,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":128358.603,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":128366.493,"message":"  <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":128366.503,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":128366.505,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":128867.459,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":128874.529,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":128874.536,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":128874.768,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":128885.096,"message":"  <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":128885.106,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":128885.108,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":129386.296,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":129407.874,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":129407.883,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":129408.399,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":129416.753,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":129416.762,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":129416.764,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":129917.975,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":129924.45,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":129924.457,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":129924.905,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":129933.75,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":129933.769,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":129933.771,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":130435.222,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":130457.931,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":130457.943,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":130458.555,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":130468.301,"message":"  <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":130468.313,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":130468.317,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":130969.436,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":130974.489,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":130974.497,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":130974.752,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":130983.447,"message":"  <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":130983.459,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":130983.463,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":131484.944,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":131507.871,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":131507.885,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":131508.678,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":131520.362,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":131520.378,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":131520.384,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":132021.622,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":132024.442,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":132024.451,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":132024.903,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":132032.664,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":132032.673,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":132032.675,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":132534.049,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":132557.863,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":132557.876,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":132558.571,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":132567.46,"message":"  <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":132567.473,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":132567.478,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":133068.43,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":133074.353,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":133074.36,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":133074.563,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":133082.263,"message":"  <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":133082.272,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":133082.274,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":133583.534,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":133607.867,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":133607.882,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":133608.646,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":133617.757,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":133617.768,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":133617.772,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":134119.088,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":134124.366,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":134124.373,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":134124.824,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":134133.09,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":134133.099,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":134133.102,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":134634.336,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":134657.783,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":134657.794,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":134658.554,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":134669.481,"message":"  <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":134669.492,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":134669.494,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":135170.665,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":135174.3,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":135174.307,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":135174.591,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":135183.58,"message":"  <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":135183.591,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":135183.595,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":135684.85,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":135707.794,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":135707.805,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":135708.391,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":135716.863,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":135716.871,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":135716.874,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":136218.158,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":136224.21,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":136224.218,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":136224.714,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":136233.338,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":136233.347,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":136233.35,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":136734.951,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":136757.532,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":136757.54,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":136758.085,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":136766.248,"message":"  <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":136766.258,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":136766.26,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":137267.327,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":137274.22,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":137274.23,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":137274.5,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":137287.268,"message":"  <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":137287.279,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":137287.283,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":137788.799,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":137807.691,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":137807.701,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":137808.334,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":137817.633,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":137817.641,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":137817.644,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":138318.818,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":138324.086,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":138324.093,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":138324.505,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":138335.333,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":138335.342,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":138335.344,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":138836.498,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":138857.65,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":138857.661,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":138858.377,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":138866.506,"message":"  <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":138866.514,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":138866.516,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":139367.473,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":139374.071,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":139374.078,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":139374.314,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":139383.083,"message":"  <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":139383.092,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":139383.096,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":139884.264,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":139907.488,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":139907.501,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":139908.647,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":139916.752,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":139916.761,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":139916.763,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":140417.93,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":140423.991,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":140423.998,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":140424.428,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":140432.295,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":140432.303,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":140432.306,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":140933.388,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":140957.461,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":140957.472,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":140957.957,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":140966.318,"message":"  <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":140966.326,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":140966.328,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":141467.122,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":141473.984,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":141473.99,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":141474.207,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":141481.956,"message":"  <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":141481.964,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":141481.966,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":141983.09,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":142007.423,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":142007.433,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":142008.397,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":142017.48,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":142017.489,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":142017.491,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":142518.471,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":142523.96,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":142523.968,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":142524.412,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":142532.313,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":142532.322,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":142532.324,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":143033.516,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":143057.396,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":143057.409,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":143058.977,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":143068.348,"message":"  <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":143068.361,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":143068.365,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":143569.377,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":143574.011,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":143574.018,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":143574.241,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":143582.05,"message":"  <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":143582.058,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":143582.06,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":144083.575,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":144107.627,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":144107.642,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":144108.432,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":144119.736,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":144119.748,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":144119.752,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":144621.118,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":144623.896,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":144623.904,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":144624.379,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":144633.443,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":144633.452,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":144633.454,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":145134.812,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":145157.228,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":145157.238,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":145157.731,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":145166.063,"message":"  <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":145166.072,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":145166.074,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":145667.351,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":145674.336,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":145674.348,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":145674.916,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":145687.457,"message":"  <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":145687.469,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":145687.472,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":146189.072,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":146210.428,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":146210.445,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":146211.348,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":146223.696,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":146223.706,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":146223.708,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":146724.822,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":146740.426,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":146740.433,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":146740.89,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":146748.78,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":146748.791,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":146748.795,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":147250.206,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":147274.267,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":147274.283,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":147275.033,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":147283.991,"message":"  <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":147284.006,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":147284.01,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":147784.957,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":147790.413,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":147790.419,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":147790.624,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":147803.004,"message":"  <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":147803.013,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":147803.015,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":148304.754,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":148324.321,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":148324.328,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":148324.986,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":148341.927,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":148341.937,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":148341.94,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":148843.505,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":148857.078,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":148857.087,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":148857.602,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":148866.748,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":148866.76,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":148866.764,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":149368.058,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":149390.545,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":149390.559,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":149391.287,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":149403.108,"message":"  <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":149403.119,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":149403.122,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":149904.319,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":149908.659,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":149908.667,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":149909.001,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":149918.06,"message":"  <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":149918.069,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":149918.071,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":150419.528,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":150442.793,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":150442.804,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":150443.681,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":150457.212,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":150457.222,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":150457.224,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":150958.623,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":150973.728,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":150973.738,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":150974.343,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":150983.59,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":150983.601,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":150983.607,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":151484.855,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":151507.038,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":151507.047,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":151507.613,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":151515.925,"message":"  <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":151515.935,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":151515.938,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":152016.922,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":152023.682,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":152023.69,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":152023.924,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":152032.942,"message":"  <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":152032.951,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":152032.953,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":152534.282,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":152557.048,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":152557.061,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":152557.697,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":152567.414,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":152567.425,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":152567.429,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":153068.681,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":153073.587,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":153073.595,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":153074.114,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":153083.142,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":153083.152,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":153083.154,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":153584.126,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":153607.002,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":153607.012,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":153607.751,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":153618.584,"message":"  <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":153618.597,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":153618.601,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":154119.497,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":154123.587,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":154123.593,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":154123.806,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":154131.925,"message":"  <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":154131.933,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":154131.936,"message":"  waiting 500ms"}
{"type":"log","callId":"call@1271","time":154633.183,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":154656.941,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@1271","time":154656.955,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@1271","time":154657.632,"message":"  done scrolling"}
{"type":"log","callId":"call@1271","time":154666.902,"message":"  <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events"}
{"type":"log","callId":"call@1271","time":154666.914,"message":"retrying click action"}
{"type":"log","callId":"call@1271","time":154666.918,"message":"  waiting 500ms"}
{"type":"after","callId":"call@1271","endTime":154813.016,"error":{"message":"Timeout 60000ms exceeded.","stack":"TimeoutError: Timeout 60000ms exceeded.\n    at ProgressController.run (/node_modules/playwright-core/lib/server/progress.js:75:26)\n    at Frame.click (/node_modules/playwright-core/lib/server/frames.js:1022:23)\n    at FrameDispatcher.click (/node_modules/playwright-core/lib/server/dispatchers/frameDispatcher.js:158:30)\n    at FrameDispatcher._handleCommand (/node_modules/playwright-core/lib/server/dispatchers/dispatcher.js:94:40)\n    at DispatcherConnection.dispatch (/node_modules/playwright-core/lib/server/dispatchers/dispatcher.js:361:39)","name":"TimeoutError"},"point":{"x":1184.74,"y":84},"afterSnapshot":"after@call@1271"}
{"type":"before","callId":"call@1273","startTime":154947.531,"apiName":"page.screenshot","class":"Page","method":"screenshot","params":{"timeout":5000,"type":"png","fullPage":true,"caret":"initial"},"stepId":"pw:api@24","pageId":"page@8583ad047eb24e68c5c391e2c36584ed","beforeSnapshot":"before@call@1273"}
{"type":"log","callId":"call@1273","time":154953.389,"message":"taking page screenshot"}
{"type":"log","callId":"call@1273","time":154954.782,"message":"waiting for fonts to load..."}
{"type":"log","callId":"call@1273","time":154955.427,"message":"fonts loaded"}
{"type":"after","callId":"call@1273","endTime":155036.107,"result":{"binary":"<Buffer>"},"afterSnapshot":"after@call@1273"}
