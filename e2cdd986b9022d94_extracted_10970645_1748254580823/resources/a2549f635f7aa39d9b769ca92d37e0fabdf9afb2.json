{"timestampLabel": "Last updated: Today, {{updateTime}}", "filters.status": "Status", "tableHeaders.priority.title": "Priority", "filters.priority": "Priority", "modals.duplicateCampaign.infoArea": "{{originalCampaignName}}'s lists will not be added to the new campaign. To run the campaign you must add lists in the Lists tab.", "modals.duplicateCampaign.placeholder": "{{originalCampaignName}} Copy", "modals.startConfirmation.title": "Start campaign?", "tableHeaders.priority.hint": "Priority is set between {{min}} (lowest) and {{max}} (highest)", "tableHeaders.createdAt": "Created at", "tableHeaders.team": "Team", "sidePanels.filters.title": "Filters", "modals.createCampaign.teamTooltip": "Users can only see campaigns from their own team", "filters.dialingMode": "Dialing Mode", "filters.extra": "Extra", "filters.created": "Created", "count_plural": "{{count}} campaigns", "modals.createCampaign.title": "Create campaign", "modals.startConfirmation.messageLine": "The campaign will start calling records and the reporting information will be available in the campaign details", "toolbar.title": "Filters", "modals.createCampaign.namePlaceholder": "e.g. Black Friday", "modals.duplicateCampaign.title": "Duplicate campaign", "filters.emptyMessage": "We couldn’t find any campaigns with the applied filters", "delete": "Delete", "noTeams": "No teams", "sidePanels.filters.created.label": "Created", "newButtonLabel": "Create campaign", "emptyMessage": "Create a campaign to view it in the campaigns list", "emptyTitle": "No campaigns yet", "tableHeaders.createdAt.label": "Created", "filters.emptyTitle": "No campaigns found", "tableHeaders.createdAt.hint": "All times are displayed in the {{timezone}} timezone", "modals.startConfirmation.info": "The dialing mode cannot be changed after the campaign starts.", "sidePanels.filters.status.placeholder": "Select status", "sidePanels.filters.status.label": "Status", "sidePanels.filters.dialingMode.placeholder": "Select dialing mode", "sidePanels.filters.dialingMode.option.preview": "Preview dialing", "sidePanels.filters.dialingMode.option.predictive": "Predictive dialing", "sidePanels.filters.dialingMode.label": "Dialing Mode", "sidePanels.filters.actionRequired.label": "Show only campaigns with warning flags", "tableHeaders.name": "Name", "duplicate": "Duplicate", "toolbar.filters.availableFilters.dialingMode": "Dialing Mode", "toolbar.filters.availableFilters.status": "Status", "toolbar.filters.availableFilters.extra": "Extra", "toolbar.filters.clearButton": "Clear All", "tableHeaders.dialingMode": "Dialing mode", "tableHeaders.status": "Status", "modals.createCampaign.teamPermissionTooltip": "Users can only see the campaigns within their permissions", "search": "Search by campaign name", "searchTooltip": "Type at least {{minLength}} characters", "toolbar.filters.title": "Filters", "headerLabel": "Campaigns", "count": "{{count}} campaign", "modals.duplicateCampaign.warningArea": "Setting up could take some seconds. You can close this modal and access the campaign in the campaigns list, once its setup is complete.", "sidePanels.filters.priority.label": "Priority"}