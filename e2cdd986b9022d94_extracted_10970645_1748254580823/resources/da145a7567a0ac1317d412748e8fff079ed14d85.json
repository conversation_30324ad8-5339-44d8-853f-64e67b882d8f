{"selectedCount": "{{count}} selected", "pause": "Pause", "disabled": "Disabled", "timePeriod.custom": "Custom...", "emptyWidget.requestTimeoutError.title": "Oops", "resourceChanged": "Resource has changed", "remove": "Remove", "filters.filterText": "Filters", "name": "Name", "emptyWidget.noPermission.title": "You don't have access to this", "add": "Add", "teamDropdownLabel": "Team", "dropdownLoadingText": "Loading...", "enabled": "Enabled", "createdAt": "Created at", "days.general": "day(s)", "maxLengthWarning": "We can only support a maximum number of {{maxLength}} characters", "refresh": "Refresh", "emptyWidget.searchWithNoResults.message": "Please use different criteria to search or filter for campaigns", "list.emptyTitle": "No entries", "list.emptyMessage": "There are currently no available entries", "save": "Save", "emptyWidget.noTeam.title": "You don't belong to any team", "timePeriod.lastDay": "Last 24 hours", "searchByName": "Search by name", "enterIsOutOfBoundsAndIntOnlyWarning": "Entering a number between {{min}} and {{max}}, without decimal places", "emptyWidget.searchWithNoResults.title": "No campaigns found", "hours.count": "{{count}} hour", "hours.count_plural": "{{count}} hours", "exceedingGlobalMaxAttemptsError": "The attempts in record chaining is exceeding the global max. attempts per record", "seconds.count": "{{count}} second", "seconds.count_plural": "{{count}} seconds", "minutes.count_plural": "{{count}} minutes", "days.count_plural": "{{count}} days", "running": "Running", "emptyWidget.requestTimeoutError.message": "there was a problem loading data.", "goBack": "Go back", "incomplete": "Incomplete", "loadMore": "Load more", "timePeriod.anyTime": "All time", "select": "Select", "warningNameCouldNotBeVerified": "Couldn't verify name. Please try again.", "ringGroups": "Ring groups", "warningNameCouldNotHaveRestrictedCharacters": "Characters \"&\" and \"#\" are not allowed when creating campaign's name. Please choose a different name without those characters.", "leave": "Leave", "newName": "New name", "paused": "Paused", "pausing": "Pausing", "previous": "Previous", "ready": "Ready", "noMatchesFoundMessage": "Please search for another keyword", "minutes.count": "{{count}} minute", "requiredFieldWarning": "This field is required", "seconds.general": "second(s)", "start": "Start", "starting": "Starting", "status": "Status", "emptyWidget.noPermission.message": "It looks like you don't have permission to see this content.", "create": "Create", "emptyRowValue": "N/A", "outMaxAttempt": "The number cannot exceed the max attempts per record", "emptyWidget.noConnection.message": "We weren't able to process your request. Please try again", "emptyWidget.noConnection.title": "No internet connection", "from": "From", "outOfBoundsAndIntOnlyWarningMessage": "Enter a number between {{min}} and {{max}}", "filters.clearAllButton": "Clear all", "days.count": "{{count}} day", "nameAllOptionText": "All", "timePeriod.lastMonth": "Last month", "teamAllOptionText": "All teams", "loading": "Loading", "to": "To", "apply": "Apply", "close": "Close", "dropdownLoadMoreButton": "Load more...", "exceedingGlobalMaxAttemptsErrorMessage": "Some attempts exceed this limit", "delete": "Delete", "discard": "Discard", "hours.general": "hour(s)", "emptyWidget.minSearchInputLength.message": "Type at least {{minInputLength}} characters for us to fetch your search results", "emptyWidget.noTeam.message": "Check with your manager about your permissions.", "noMatchesFoundTitle": "No results found", "numberOfRecords": "Number of records", "search": "Search", "teamDropdownPlaceholder": "Select an option", "download": "download", "createCampaign.outScope": "You can't create a campaign for this team. Please select a team within your management scope.", "timePeriod.lastSixHours": "Last 6 hours", "timePeriod.lastWeek": "Last week", "option.name.default": "All", "dismissibleTags.activity": "activity", "dismissibleTags.activities": "activities", "outOfBoundsAndIntOnlyWarning": "Please insert a value between {{min}} and {{max}}, without decimal places", "searchExactNumberTooltipText": "Type at least {{minLength}} characters", "emptyWidget.minSearchInputLength.title": "Type {{minInputLength}} characters to start searching", "somethingWentWrong": "Something went wrong", "errorNameNotUnique": "This campaign name is already taken", "next": "Next", "noItemsFoundTitle": "No items found", "file": "file", "cancel": "Cancel\t", "searchExactNumberPlaceHolderText": "Search for an exact number", "listPagination.jumpTo": "Jump to", "maxCharacterLimitExceded": "The maximum number of characters has been exceeded\t", "clear": "Clear", "minutes.general": "minute(s)"}