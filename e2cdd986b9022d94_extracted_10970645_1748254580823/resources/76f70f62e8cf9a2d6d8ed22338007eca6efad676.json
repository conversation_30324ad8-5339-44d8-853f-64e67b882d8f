{"runtime.session_engine.start_new_session.paragraph_1": "Talkdesk is already open in another tab or device. Click\n<strong>Start new session</strong> to open Conversations here instead.", "extension_points.ui_blocks.email_content.auto_reply_sent_to": "Auto-reply sent to {{name}}", "chat_content.search_placeholder": "Search this conversation", "extension_points.ui_blocks.filter_mode.filter_by": "FILTER BY", "countries_dropdown.countries.mu": "Mauritius", "extension_points.ui_blocks.components.countries_dropdown.countries.cf": "Central African Republic", "tab_host.failure.retry": "Retry", "extension_points.ui_blocks.panel.targets_list.transfer_target.search_placeholder.ring_groups": "Search for ring groups...", "extension_points.ui_blocks.interaction_actions.buttons.mute.false.text": "Mute", "countries_dropdown.countries.gr": "Greece", "extension_points.ui_blocks.components.countries_dropdown.countries.mq": "Martinique", "extension_points.ui_blocks.components.caller_number_dropdown.error_message": "Couldn't load the list.", "panel.targets_list.error_states.ring_groups.message": "There was a problem loading the ring groups list, please try again.", "extension_points.ui_blocks.interaction_actions.buttons.end_text_conversation.dialog.text": "If you end the chat and this contact sends a new message, it will count as a new conversation.", "extension_points.notifications.outbound_email_action": "Couldn't start new Em<PERSON>", "message_input.hint": "Enter to add a new line | Shift + Enter to send", "countries_dropdown.countries.ee": "Estonia", "countries_dropdown.countries.nz": "New Zealand", "extension_points.ui_blocks.email_content.to": "to", "extension_points.ui_blocks.components.countries_dropdown.countries.nr": "Nauru", "extension_points.ui_blocks.components.email_editor.from": "From", "notifications.actions.consultation.transfer_failure.title": "Couldn't transfer the call", "extension_points.notifications.actions.agent_call.cancel": "Couldn't close the panel", "extension_points.commands.conference.removing": "Removing...", "countries_dropdown.countries.bm": "Bermuda", "caller_number_dropdown.placeholder": "Select a number", "countries_dropdown.countries.lk": "Sri Lanka", "agent_status.after_call_work": "After Call Work", "extension_points.notifications.actions.agent_call.unavailable": "Couldn't start the call. Agent is unavailable", "countries_dropdown.countries.io": "British Indian Ocean Territory", "extension_points.ui_blocks.components.countries_dropdown.countries.aw": "Aruba", "message_input.placeholder": "Message {{name}}", "extension_points.agent_status.busy": "On a Call", "extension_points.ui_blocks.components.countries_dropdown.countries.no": "Norway", "extension_points.notifications.outbound_digital_whatsapp_unselected_sender.message": "Please select an outbound sender number", "extension_points.commands.subtitles.consultation_offer": "Regarding {{name}}", "extension_points.commands.unknown_participant": "Unknown Participant", "countries_dropdown.countries.bj": "Benin", "extension_points.ui_blocks.components.countries_dropdown.countries.im": "Isle of Man", "conversations_panel.group.all_conversations": "All conversations", "countries_dropdown.countries.va": "Vatican City", "extension_points.commands.topics.default": "via {{topic}}", "extension_points.ui_blocks.components.email_editor.bcc": "Bcc", "extension_points.notifications.actions.transfer.start": "Couldn't start the transfer", "conversations_panel.tabs.active_tab.failed_loading_channel_message_ay11_one": "Couldn't load one channel", "extension_points.ui_blocks.interaction_actions.buttons.elevate_to_call.dialog.cancelText": "Cancel", "countries_dropdown.countries.sz": "Swaziland", "extension_points.ui_blocks.components.countries_dropdown.countries.ge": "Georgia", "panel.targets_list.tabs.labels.ring_groups": "Ring groups", "extension_points.commands.subtitles.conference_received_conversation": "Multiple participants", "extension_points.ui_blocks.panel.targets_list.error_states.agents.message": "There was a problem loading the agents list, please try again.", "conversations_panel.tabs.active_tab.sla_policy_popup.seconds": "{{seconds}}s", "runtime.session_engine.session_override.title": "This session has ended", "extension_points.ui_blocks.components.countries_dropdown.countries.om": "Oman", "extension_points.notifications.actions.accept_call": "Couldn't accept the call", "countries_dropdown.countries.gy": "Guyana", "conversations_panel.tabs.active_tab.description_other": "{{count}} conversations", "conversations_panel.tabs.active_tab.description_plural": "{{count}} conversations", "extension_points.ui_blocks.components.countries_dropdown.countries.mu": "Mauritius", "commands.live_chat.visitor": "Visitor", "panel.targets_list.transfer_target.search_hint": "Type at least 3 characters to start searching", "extension_points.commands.subtitles.email_requested_conversation_with_name": "To {{name}} <{{email}}>", "extension_points.templates.empty.title": "No activity yet", "extension_points.ui_blocks.interaction_info_consultation.active_states.hold_participant_conversation": "Talking", "extension_points.ui_blocks.components.countries_dropdown.countries.ss": "South Sudan", "notifications.actions.agent_call.unavailable": "Couldn't start the call. Agent is unavailable", "extension_points.ui_blocks.interaction_info_consultation.targetStates.hold_contact_dial": "Dialing", "core.notifications.permission_autoplay.title": "Ringtone is muted", "extension_points.ui_blocks.email_content.wrote_message_by": "On {{date}} at {{time}} {{name}} wrote:", "conversations_panel.tabs.active_tab.sla_policy_popup.title": "SLA Policy", "extension_points.ui_blocks.digital_interaction_with_tabs.tab.chat_tab": "Cha<PERSON>", "chat_content.automatic_message": "Automatic message", "conversations_panel.tabs.queue_tab.failure_widget.message": "There was a problem while trying to load this tab's contents.", "extension_points.ui_blocks.components.countries_dropdown.countries.qa": "Qatar", "panel.titles.agent_call": "Call an agent", "extension_points.notifications.unrecoverable_errors.wrap_up.message": "The wrap-up was automatically dismissed. If this problem persists, please contact your administrator", "extension_points.ui_blocks.components.countries_dropdown.countries.cd": "Democratic Republic of the Congo", "extension_points.ui_blocks.components.chat_content.message_input.template_shortcut_tips": "Enter to send", "conversations_panel.tabs.active_tab.sla_policy_popup.seconds_overdue": "Overdue {{seconds}}s", "extension_points.notifications.outbound_sms_unselected_sender.message": "Please select an outbound sender number", "countries_dropdown.countries.bf": "Burkina Faso", "extension_points.ui_blocks.trigger_outbound_actions.outbound_action": "Start Outbound", "countries_dropdown.countries.tv": "Tuvalu", "extension_points.ui_blocks.components.countries_dropdown.countries.fi": "Finland", "extension_points.ui_blocks.components.countries_dropdown.countries.id": "Indonesia", "conversations_panel.tabs.active_tab.sla_policy_popup.time_remaining_label": "Time to first response", "extension_points.ui_blocks.interaction_info.states.incoming_voice_consultation": "Incoming consultation", "extension_points.notifications.actions.reject_call": "Couldn't reject the call", "conversations_panel.tabs.active_tab.failed_loading_channel_message_subtitle_with_action": "If the problem persists, contact your administrator.", "extension_points.ui_blocks.interaction_actions.buttons.end_consultation.text": "End consultation", "panel.targets_list.empty_states.no_search_results.title": "No results found", "extension_points.ui_blocks.panel.targets_list.tabs.labels.external_number": "External number", "extension_points.ui_blocks.components.countries_dropdown.countries.mh": "Marshall Islands", "commands.subtitles.consultation_offer": "Regarding {{name}}", "extension_points.ui_blocks.filter_mode.internal_messages": "Internal messages", "runtime.renderer.mode.error.reconnection.notification": "Please reconnect the host widget and try again.", "dropdown.error_message": "Could not load.", "extension_points.ui_blocks.components.countries_dropdown.countries.ua": "Ukraine", "conversations_panel.tabs.active_tab.sort_by_channel_option": "Channel", "extension_points.ui_blocks.components.countries_dropdown.countries.bq": "Caribbean Netherlands", "extension_points.ui_blocks.components.countries_dropdown.countries.so": "Somalia", "extension_points.commands.unknown_contact": "Unknown Contact", "notifications.unrecoverable_errors.offer_consultation.title": "Couldn't answer the consultation", "countries_dropdown.countries.mn": "Mongolia", "countries_dropdown.countries.pm": "Saint Pierre and Miquelon", "countries_dropdown.countries.fi": "Finland", "countries_dropdown.countries.tw": "Taiwan", "countries_dropdown.countries.th": "Thailand", "core.notifications.create_channel_not_allowed.title": "Failed to start a new conversation", "countries_dropdown.countries.al": "Albania", "notifications.unrecoverable_errors.offer_outbound.title": "Couldn't load the dialing screen", "extension_points.ui_blocks.components.chat_content.message_input.shortcut_key_tips": "Enter to add a new line | Cmd/Shift + Enter to send", "core.notifications.create_channel_not_allowed.message": "Maximum on-going interactions reached", "core.notifications.session_override.message": "Please try again. If the problem persists, contact your administrator\t", "notifications.actions.agent_call.cancel": "Couldn't close the panel", "extension_points.notifications.agent_status_synced.message": "Agent status synced with omnichannel", "extension_points.ui_blocks.components.countries_dropdown.countries.pa": "Panama", "countries_dropdown.countries.hn": "Honduras", "extension_points.ui_blocks.components.countries_dropdown.countries.cy": "Cyprus", "extension_points.ui_blocks.components.countries_dropdown.countries.gu": "Guam", "extension_points.ui_blocks.interaction_actions.buttons.leave.text": "End call", "notifications.actions.consultation.transfer_success_received": "The call was transferred to you", "extension_points.ui_blocks.components.caller_number_dropdown.store.default_friendly_name": "<PERSON><PERSON><PERSON>", "extension_points.ui_blocks.components.email_editor.reply": "Reply", "extension_points.ui_blocks.interaction_info.states.facebook_conversation_info": "Chatting on Facebook Messenger", "extension_points.ui_blocks.components.countries_dropdown.countries.li": "Liechtenstein", "extension_points.ui_blocks.components.countries_dropdown.countries.nl": "Netherlands", "extension_points.ui_blocks.interaction_actions.buttons.dtmf.text": "Keypad", "extension_points.notifications.actions.unmute": "Couldn't unmute", "extension_points.ui_blocks.components.countries_dropdown.countries.us": "United States", "countries_dropdown.countries.re": "Réunion", "countries_dropdown.countries.km": "Comoros", "agent_status.offline": "Offline", "extension_points.ui_blocks.components.countries_dropdown.countries.ni": "Nicaragua", "extension_points.ui_blocks.components.countries_dropdown.countries.it": "Italy", "extension_points.notifications.start_outbound_digital_whatsapp_error.title": "An error occurred", "extension_points.ui_blocks.components.countries_dropdown.countries.my": "Malaysia", "countries_dropdown.countries.wf": "Wallis and Futuna", "extension_points.ui_blocks.components.countries_dropdown.countries.sa": "Saudi Arabia", "commands.subtitles.consultation_offer_outbound": "Consulting {{name}}", "extension_points.notifications.actions.wrap_up.submit.title": "Couldn't submit the wrap-up", "conversations_panel.tabs.empty_state.title": "No conversations", "interaction_header.actions.recording": "REC", "runtime.ui_engine.error.message": "Sorry, there was a problem loading this page. If the problem persists, contact your administrator", "extension_points.ui_blocks.components.chat_content.message_input.internal_chat": "Internal message", "extension_points.ui_blocks.components.chat_content.message_input.external_chat": "Message", "extension_points.ui_blocks.interaction_actions.buttons.send_to_queue.text": "Send to Inbox", "notifications.actions.establish.title": "Couldn't establish the call", "extension_points.ui_blocks.interaction_actions.buttons.record.false.text": "Start recording", "extension_points.notifications.dialed_number_has_already_an_ongoing_conversation_error": "The dialed number already has an ongoing conversation", "notifications.actions.consultation.transfer_success_requested": "Call transferred successfully", "notifications.actions.transfer.cancel": "Couldn't cancel the transfer", "extension_points.ui_blocks.panel.subtitles.consultation": "Consult an agent, ring group or number", "extension_points.ui_blocks.components.email_editor.touchpoints_placeholder": "Search...", "countries_dropdown.countries.cn": "China", "countries_dropdown.countries.mq": "Martinique", "countries_dropdown.countries.mx": "Mexico", "extension_points.ui_blocks.components.countries_dropdown.countries.af": "Afghanistan", "countries_dropdown.countries.uz": "Uzbekistan", "extension_points.ui_blocks.components.chat_content.virtual_agent": "Virtual agent", "extension_points.ui_blocks.email_content.forwarded": "Forwarded", "extension_points.ui_blocks.components.countries_dropdown.countries.vc": "Saint Vincent and the Grenadines", "extension_points.notifications.actions.consultation.transfer_success_requested": "Call transferred successfully", "extension_points.ui_blocks.components.countries_dropdown.countries.sy": "Syria", "countries_dropdown.countries.tk": "Tokelau", "extension_points.ui_blocks.components.countries_dropdown.countries.cg": "Republic of the Congo", "extension_points.ui_blocks.components.countries_dropdown.countries.rs": "Serbia", "extension_points.ui_blocks.components.countries_dropdown.countries.ag": "Antigua and Barbuda", "countries_dropdown.countries.ck": "Cook Islands", "countries_dropdown.countries.uy": "Uruguay", "extension_points.notifications.file_upload.maximum_file": "The file you are trying to upload exceeds the maximum size allowed.", "extension_points.notifications.actions.transfer.failure.title": "Unable to transfer the call", "extension_points.commands.subtitles.conference": "Multiple participants", "extension_points.ui_blocks.components.caller_number_dropdown.load_more": "Load more elements...", "extension_points.ui_blocks.panel.targets_list.error_states.ring_groups.message": "There was a problem loading the ring groups list, please try again.", "error.retry": "Retry", "extension_points.ui_blocks.components.countries_dropdown.countries.bs": "Bahamas", "countries_dropdown.countries.ca": "Canada", "notifications.unrecoverable_errors.offer_outbound.message": "Please wait for the call to be picked up. If this problem persists, please contact your administrator", "extension_points.ui_blocks.components.countries_dropdown.countries.pl": "Poland", "countries_dropdown.countries.pa": "Panama", "extension_points.ui_blocks.components.countries_dropdown.countries.mg": "Madagascar", "panel.titles.blind_transfer": "Blind transfer", "countries_dropdown.countries.et": "Ethiopia", "conversations_panel.tabs.active_tab.sla_policy_popup.hours_and_minutes": "{{hours}}h {{minutes}}m", "extension_points.notifications.actions.elevate_to_call": "Starting call", "extension_points.ui_blocks.interaction_actions.buttons.elevate_to_call.dialog.title": "Call {{contact_person}}", "extension_points.notifications.actions.session_override.message": "Please try again. If the problem persists, contact your administrator", "extension_points.ui_blocks.components.countries_dropdown.countries.lt": "Lithuania", "runtime.session_engine.start_new_session.title": "Start new session", "agent_status.available": "Available", "extension_points.ui_blocks.interaction_actions.buttons.send_to_queue.dialog.confirmText": "Send to Inbox", "extension_points.ui_blocks.panel.targets_list.empty_states.no_search_results.title": "No results found", "conversations_panel.channels.email": "Email", "session_override.title": "The session has ended", "extension_points.notifications.actions.send_email_failure.title": "Couldn't send the email", "extension_points.ui_blocks.components.chat_content.chat_message.location_message_content": "Latitude: {{latitude}}, Longitude: {{longitude}}", "countries_dropdown.countries.zw": "Zimbabwe", "extension_points.ui_blocks.components.countries_dropdown.countries.de": "Germany", "extension_points.ui_blocks.components.countries_dropdown.countries.pn": "Pitcairn", "countries_dropdown.countries.sg": "Singapore", "extension_points.ui_blocks.components.countries_dropdown.countries.io": "British Indian Ocean Territory", "extension_points.commands.live_chat.visitor": "Visitor", "extension_points.commands.unknown_resource": "Unknown Resource", "extension_points.ui_blocks.components.countries_dropdown.countries.ad": "Andorra", "extension_points.ui_blocks.components.countries_dropdown.countries.ke": "Kenya", "extension_points.ui_blocks.components.countries_dropdown.countries.lr": "Liberia", "countries_dropdown.countries.mt": "Malta", "countries_dropdown.countries.il": "Israel", "extension_points.ui_blocks.interaction_info.states.live_chat_conversation_info": "Chatting on Chat", "extension_points.ui_blocks.components.countries_dropdown.countries.vu": "Vanuatu", "extension_points.ui_blocks.components.countries_dropdown.countries.by": "Belarus", "conversations_panel.new_conversation.title": "New conversation", "countries_dropdown.countries.me": "Montenegro", "countries_dropdown.countries.gl": "Greenland", "countries_dropdown.countries.pg": "Papua New Guinea", "extension_points.ui_blocks.interaction_info.states.dial_voice_call": "Dialing", "extension_points.ui_blocks.components.countries_dropdown.countries.xk": "Kosovo", "notifications.outbound_sms_unselected_sender.message": "Please select an outbound sender number", "extension_points.ui_blocks.components.countries_dropdown.countries.pg": "Papua New Guinea", "interaction_info.states.incoming_live_chat_conversation": "Incoming Live chat", "extension_points.ui_blocks.components.chat_content.message_input.button": "Send", "countries_dropdown.countries.ky": "Cayman Islands", "extension_points.ui_blocks.components.countries_dropdown.countries.mw": "Malawi", "extension_points.notifications.actions.send_to_queue.success": "<PERSON><PERSON> sent to Inbox successfully", "countries_dropdown.countries.tc": "Turks and Caicos Islands", "countries_dropdown.countries.vn": "Vietnam", "extension_points.ui_blocks.interaction_actions.buttons.end_text_conversation.dialog.confirmText": "End chat", "extension_points.notifications.actions.contact_left": "Contact ended the call", "extension_points.ui_blocks.components.countries_dropdown.countries.co": "Colombia", "extension_points.ui_blocks.components.countries_dropdown.countries.kh": "Cambodia", "extension_points.notifications.start_outbound_sms_error.title": "An error occurred", "extension_points.ui_blocks.components.countries_dropdown.countries.tl": "East Timor", "extension_points.ui_blocks.components.countries_dropdown.countries.ar": "Argentina", "runtime.dialog_engine.dialog.closed.button": "Close", "conversations_panel.nav_bar.unread.messages": "{{count}} unread", "extension_points.ui_blocks.components.countries_dropdown.countries.il": "Israel", "extension_points.ui_blocks.components.chat_content.message_input.discard": "Discard", "countries_dropdown.countries.to": "Tonga", "conversations_panel.tabs.active_tab.sort_by_newest_assignment_option": "Newest assignments", "chat_message.format_not_supported": "Message format is not supported", "extension_points.ui_blocks.interaction_actions.buttons.end_text_conversation.dialog.title": "End chat?", "extension_points.notifications.start_new_email_conversation_not_allowed.title": "Couldn’t start new Email", "agent_status.custom_busy": "Busy", "extension_points.agent_status.custom_busy": "Busy", "extension_points.ui_blocks.components.countries_dropdown.countries.mm": "Myanmar", "extension_points.ui_blocks.components.countries_dropdown.countries.fj": "Fiji", "extension_points.commands.topics.emergency_call": "Emergency call", "notifications.permission.microphone.title": "The application needs to have microphone access", "runtime.session_engine.browser.mode.only.title": "Desktop access is disabled", "countries_dropdown.countries.gw": "Guinea-Bissau", "extension_points.notifications.create_channel_not_allowed.message": "Maximum on-going interactions reached", "dropdown.options.loading": "Loading", "extension_points.ui_blocks.components.email_editor.cc": "Cc", "countries_dropdown.countries.ga": "Gabon", "extension_points.ui_blocks.components.email_editor.Paragraph": "Paragraph", "extension_points.ui_blocks.interaction_actions.buttons.send_to_queue.dialog.cancelText": "Cancel", "conversations_panel.tabs.active_tab.sort_by_contact_option": "Contact (A-Z)", "extension_points.ui_blocks.panel.targets_list.tabs.labels.agents": "Agents", "extension_points.ui_blocks.components.countries_dropdown.countries.mp": "Northern Mariana Islands", "countries_dropdown.countries.mk": "Macedonia", "extension_points.ui_blocks.email_content.hide_details": "Hide details", "extension_points.ui_blocks.components.countries_dropdown.countries.lk": "Sri Lanka", "extension_points.ui_blocks.components.countries_dropdown.countries.mo": "Macau", "extension_points.notifications.actions.consultation.transfer_failure.message": "Please try again", "extension_points.ui_blocks.components.countries_dropdown.countries.sn": "Senegal", "countries_dropdown.countries.bh": "Bahrain", "countries_dropdown.countries.nf": "Norfolk Island", "notifications.actions.end_call.title": "Couldn't end the call", "interaction_info.states.conversation_voice_call": "Talking", "extension_points.ui_blocks.interaction_info.states.hold_conversation_call": "On hold", "extension_points.ui_blocks.components.countries_dropdown.countries.yt": "Mayotte", "extension_points.ui_blocks.interaction_actions.buttons.more.text": "More", "extension_points.ui_blocks.components.countries_dropdown.countries.dm": "Dominica", "notifications.actions.agent_call.failure": "Couldn't establish the call", "extension_points.ui_blocks.components.countries_dropdown.countries.sj": "Svalbard and <PERSON>", "countries_dropdown.countries.dz": "Algeria", "countries_dropdown.countries.ph": "Philippines", "extension_points.ui_blocks.components.countries_dropdown.countries.nz": "New Zealand", "extension_points.notifications.conference.guest_connected.title": "Added to conference", "extension_points.ui_blocks.components.countries_dropdown.countries.ws": "Samoa", "extension_points.ui_blocks.components.countries_dropdown.countries.tc": "Turks and Caicos Islands", "core.notifications.session_override.title": "Couldn't start a new session", "extension_points.ui_blocks.components.countries_dropdown.countries.gy": "Guyana", "countries_dropdown.countries.pr": "Puerto Rico", "notifications.actions.transfer.failure.message": "Please try again or select another destination", "extension_points.commands.tags.dedicated": "Dedicated", "extension_points.ui_blocks.components.countries_dropdown.countries.do": "Dominican Republic", "countries_dropdown.countries.sm": "San Marino", "extension_points.notifications.actions.transfer.failure.message": "Please try again or select another destination", "countries_dropdown.countries.sr": "Suriname", "extension_points.ui_blocks.interaction_info.states.emergency_voice_call": "Emergency call", "countries_dropdown.countries.ls": "Lesotho", "countries_dropdown.countries.cu": "Cuba", "caller_number_dropdown.store.refine_search": "{{count}} more numbers. Use the search to refine further.", "commands.topics.consultation": "Consultation", "extension_points.channels.fbm.display_name": "Facebook Messenger", "countries_dropdown.countries.gu": "Guam", "notifications.actions.target.empty_ring_group.message": "Please contact your administrator", "countries_dropdown.countries.tm": "Turkmenistan", "countries_dropdown.countries.ar": "Argentina", "extension_points.ui_blocks.components.email_editor.Heading1": "Heading 1", "notifications.actions.hold": "Couldn't hold", "extension_points.ui_blocks.components.countries_dropdown.countries.br": "Brazil", "countries_dropdown.countries.is": "Iceland", "extension_points.notifications.actions.reject_live_chat.failure.title": "Couldn't reject the Chat", "extension_points.ui_blocks.components.countries_dropdown.countries.ck": "Cook Islands", "extension_points.ui_blocks.components.countries_dropdown.countries.dj": "Djibouti", "extension_points.ui_blocks.components.phone_input.error_message": "Invalid number", "countries_dropdown.countries.mm": "Myanmar", "extension_points.ui_blocks.components.countries_dropdown.countries.bi": "Burundi", "countries_dropdown.countries.ag": "Antigua and Barbuda", "extension_points.ui_blocks.components.countries_dropdown.countries.lv": "Latvia", "extension_points.notifications.actions.conference.add_guest_failure.title": "Couldn't add guest", "extension_points.ui_blocks.interaction_actions.buttons.start_consultation.text": "Consult", "notifications.outbound_sms_action": "Couldn't send the SMS", "extension_points.ui_blocks.interaction_info_consultation.active_states.hold_participant_dial": "Dialing", "countries_dropdown.countries.lu": "Luxembourg", "conversations_panel.tabs.active_tab.sort_by_button_tooltip": "Sort by", "conversations_panel.tabs.queue_tab.failure_widget.title": "Could not load contents", "countries_dropdown.countries.gm": "Gambia", "extension_points.ui_blocks.components.countries_dropdown.countries.kr": "South Korea", "conversations_panel.tabs.active_tab.sla_policy_popup.more_than_99_days": "99+ d", "extension_points.ui_blocks.components.countries_dropdown.countries.ph": "Philippines", "extension_points.ui_blocks.components.countries_dropdown.countries.ga": "Gabon", "runtime.session_engine.notification.override_session_request_error.title": "Couldn’t start a new session", "extension_points.ui_blocks.components.email_editor.placeholder": "Type something...", "extension_points.ui_blocks.components.countries_dropdown.countries.cu": "Cuba", "extension_points.ui_blocks.components.countries_dropdown.countries.tr": "Turkey", "countries_dropdown.countries.mc": "Monaco", "extension_points.ui_blocks.interaction_actions.buttons.dismiss_wrap_up.dialog.cancelText": "Cancel", "extension_points.ui_blocks.components.chat_content.message_input.placeholder_internal": "This message will be visible to teammates only... \n Use @ to mention people", "extension_points.ui_blocks.components.countries_dropdown.countries.np": "Nepal", "extension_points.ui_blocks.components.countries_dropdown.countries.lu": "Luxembourg", "extension_points.ui_blocks.filter_mode.external_messages": "External messages", "notifications.actions.unmute": "Couldn't unmute", "extension_points.ui_blocks.components.countries_dropdown.countries.ai": "<PERSON><PERSON><PERSON>", "countries_dropdown.countries.er": "Eritrea", "extension_points.ui_blocks.components.countries_dropdown.countries.sk": "Slovakia", "countries_dropdown.countries.gq": "Equatorial Guinea", "extension_points.ui_blocks.components.countries_dropdown.countries.sh": "Saint Helena", "countries_dropdown.countries.mr": "Mauritania", "extension_points.notifications.unrecoverable_errors.prepare_agent_call.title": "Couldn't initiate the agent call", "extension_points.ui_blocks.interaction_info.accessibility.remove_guest": "<PERSON><PERSON><PERSON> guest", "extension_points.ui_blocks.components.email_editor.forward": "Forward", "extension_points.ui_blocks.components.countries_dropdown.countries.bj": "Benin", "notifications.actions.auto_answer": "Couldn't acknowledge auto-answer", "extension_points.ui_blocks.components.countries_dropdown.countries.ie": "Ireland", "countries_dropdown.countries.mf": "Saint <PERSON>", "conversations_panel.tabs.queue_tab.loading": "Loading...", "extension_points.ui_blocks.components.countries_dropdown.countries.pk": "Pakistan", "countries_dropdown.countries.ch": "Switzerland", "panel.targets_list.empty_states.no_ring_groups.title": "No ring groups found", "extension_points.ui_blocks.components.countries_dropdown.countries.gp": "Guadeloupe", "panel.subtitles.consultation": "Consult an agent, ring group or number", "panel.subtitles.blind_transfer": "Transfer the call to an agent, ring group or number", "extension_points.ui_blocks.components.countries_dropdown.countries.tj": "Tajikistan", "extension_points.ui_blocks.components.chat_content.message_input.hint": "Enter to add a new line | Shift + Enter to send", "countries_dropdown.countries.ss": "South Sudan", "notifications.unknown.title": "An unexpected error occurred", "countries_dropdown.countries.cy": "Cyprus", "extension_points.ui_blocks.interaction_actions.buttons.complete.text": "Auto-answer is enabled", "extension_points.notifications.permission.microphone.title": "The application needs to have microphone access", "extension_points.ui_blocks.interaction_info_consultation.buttons.switch_call.text": "Switch to this call", "extension_points.commands.subtitles.consultation_offer_outbound": "Consulting {{name}}", "conversations_panel.tabs.active_tab.failed_loading_channel_message_other": "Couldn't load {{channels}} channels", "extension_points.commands.unknown_email_subject": "(no subject)", "notifications.actions.dial_number.title": "Couldn't initiate the call", "extension_points.ui_blocks.components.countries_dropdown.countries.bz": "Belize", "extension_points.ui_blocks.components.chat_content.chat_message.format_not_supported": "Message format is not supported", "interaction_info_consultation.userStates.hold_contact_conversation": "Talking", "extension_points.ui_blocks.components.countries_dropdown.countries.bw": "Botswana", "countries_dropdown.countries.sa": "Saudi Arabia", "dropdown.retry": "Retry", "extension_points.ui_blocks.components.countries_dropdown.countries.me": "Montenegro", "runtime.renderer.mode.error.lost.connection.notification": "Lost connection with Talkdesk Workspace host.", "extension_points.ui_blocks.interaction_actions.buttons.submit_wrap_up.text": "Submit", "countries_dropdown.countries.bz": "Belize", "extension_points.ui_blocks.panel.titles.consultation": "Consultation", "extension_points.ui_blocks.components.countries_dropdown.countries.ls": "Lesotho", "extension_points.ui_blocks.components.countries_dropdown.countries.sr": "Suriname", "countries_dropdown.countries.sk": "Slovakia", "notifications.actions.send_to_queue.success": "<PERSON><PERSON> sent to Queue successfully", "countries_dropdown.countries.cl": "Chile", "conversations_panel.tabs.active_tab.sort_by_most_recent_option": "Most recent", "extension_points.ui_blocks.panel.targets_list.error_states.agents.title": "Could not load the agents list", "interaction_info.states.after_voice_call": "Wrap-up", "extension_points.ui_blocks.components.countries_dropdown.countries.is": "Iceland", "countries_dropdown.countries.sb": "Solomon Islands", "extension_points.ui_blocks.components.countries_dropdown.countries.bt": "Bhutan", "countries_dropdown.countries.la": "Laos", "countries_dropdown.countries.af": "Afghanistan", "extension_points.notifications.unrecoverable_errors.prepare_add_guest.title": "Couldn’t add guest", "extension_points.channels.email.display_name": "Email", "extension_points.ui_blocks.panel.targets_list.transfer_target.buttons.retry": "Retry", "notifications.actions.consultation.end.title": "Couldn't end the consultation", "extension_points.notifications.invalid_number": "Invalid number", "extension_points.notifications.file_upload.file_upload_error": "Couldn’t upload file. Please try again.", "countries_dropdown.countries.au": "Australia", "notifications.actions.transfer.success": "Call transferred successfully", "extension_points.ui_blocks.outbound_actions.caller_heading.title": "Outbound on:", "extension_points.ui_blocks.components.countries_dropdown.countries.ca": "Canada", "extension_points.ui_blocks.components.countries_dropdown.countries.al": "Albania", "countries_dropdown.countries.td": "Chad", "conversations_panel.tabs.active_tab.sla_policy_popup.hours_and_minutes_overdue": "Overdue {{hours}}h {{minutes}}m", "extension_points.ui_blocks.components.countries_dropdown.countries.am": "Armenia", "countries_dropdown.countries.sn": "Senegal", "conversations_panel.nav_bar.open": "Open the conversations panel", "extension_points.ui_blocks.components.caller_number_dropdown.search_options.character_limit_message": "Type at least 3 characters", "extension_points.ui_blocks.components.countries_dropdown.countries.ru": "Russia", "extension_points.notifications.file_upload.only_file_format_are_allowed": "The file type you are trying to upload is not supported.", "extension_points.notifications.cti_not_connected.message": "Some capabilities won't work", "extension_points.ui_blocks.components.countries_dropdown.countries.la": "Laos", "countries_dropdown.countries.mw": "Malawi", "extension_points.ui_blocks.start_interaction_menu.view_more": "More...", "countries_dropdown.countries.om": "Oman", "countries_dropdown.countries.si": "Slovenia", "notifications.actions.accept_live_chat.failure.title": "Couldn't accept the live chat", "translator.hide_sent": "<PERSON><PERSON> sent", "countries_dropdown.countries.ki": "Kiribati", "countries_dropdown.countries.bn": "Brunei", "countries_dropdown.countries.mz": "Mozambique", "extension_points.ui_blocks.email_content.cancel": "Cancel", "conversations_panel.channels.sms": "SMS", "extension_points.ui_blocks.components.countries_dropdown.countries.as": "American Samoa", "runtime.session_engine.take_back_session.label": "Take back session", "translator.translation_failed": "Failed to translate", "extension_points.notifications.permission.autoplay.title": "Ringtone is muted", "extension_points.ui_blocks.components.countries_dropdown.countries.cv": "Cape Verde", "commands.unknown_agent": "Unknown Agent", "extension_points.ui_blocks.components.countries_dropdown.countries.th": "Thailand", "extension_points.commands.subtitles.consultation_requested_conversation": "Consulting {{name}}", "extension_points.ui_blocks.outbound_actions.button_labels.start_digital_whatsapp_conversation": "Start WhatsApp", "countries_dropdown.countries.pl": "Poland", "extension_points.ui_blocks.components.countries_dropdown.countries.bf": "Burkina Faso", "extension_points.notifications.actions.target.empty_ring_group.title": "No colleagues assigned to this queue", "countries_dropdown.countries.sy": "Syria", "extension_points.channels.dg_whatsapp.display_name": "WhatsApp", "countries_dropdown.countries.cd": "Congo", "extension_points.ui_blocks.interaction_actions.buttons.park.text": "Park", "extension_points.ui_blocks.components.countries_dropdown.countries.km": "Comoros", "extension_points.ui_blocks.components.countries_dropdown.extension_label": "Dial an extension", "extension_points.ui_blocks.interaction_actions.buttons.accept.text": "Accept call", "countries_dropdown.countries.do": "Dominican Republic", "countries_dropdown.countries.sv": "El Salvador", "extension_points.ui_blocks.components.countries_dropdown.countries.fo": "Faroe Islands", "extension_points.ui_blocks.components.countries_dropdown.countries.td": "Chad", "countries_dropdown.countries.ws": "Samoa", "countries_dropdown.countries.kh": "Cambodia", "countries_dropdown.countries.ao": "Angola", "extension_points.ui_blocks.components.countries_dropdown.countries.eg": "Egypt", "notifications.actions.reject_live_chat.failure.title": "Couldn't reject the live chat", "notifications.dialed_number_has_already_an_ongoing_conversation_error.message": "The dialed number already has an ongoing conversation", "extension_points.ui_blocks.components.countries_dropdown.countries.dz": "Algeria", "panel.targets_list.transfer_target.store.refine_search": "{{count}} more agents. Use the search to refine further.", "countries_dropdown.countries.tt": "Trinidad and Tobago", "extension_points.ui_blocks.tabs.empty_state_message": "There are no tabs configured for this conversation", "error.message": "Sorry, there was a problem loading this page. If the problem persists, contact your administrator", "extension_points.ui_blocks.components.chat_content.reachability_status.contact_connected": "Contact has entered the chat at {{time}}", "notifications.actions.accept_call": "Couldn't accept the call", "runtime.session_engine.desktop.mode.only.paragraph_1": "To use Conversations App, please use Talkdesk desktop app. Conversations app access via web browser is disabled by your account administrator. Please reach out the administrator for more information.", "extension_points.notifications.actions.dial_number.title": "Couldn't initiate the call", "core.notifications.permission_microphone.action.button": "How to enable", "notifications.error_persist_contact_administrator_try_again": "Please try again. If this problem persists, please contact your administrator", "notifications.actions.send_to_queue.failure.title": "Couldn't send the chat to <PERSON>ue", "extension_points.ui_blocks.interaction_actions.buttons.dismiss_wrap_up.dialog.text": "Are you sure you want to dismiss the wrap-up stage? You will lose any changes you made.", "extension_points.agent_status.available": "Available", "extension_points.ui_blocks.components.countries_dropdown.countries.et": "Ethiopia", "countries_dropdown.countries.sx": "Sint Maarten", "core.notifications.session_already_exist.message": "Please ensure you have only one active Conversations app while using other Talkdesk apps", "extension_points.ui_blocks.tabs.right_control": "<PERSON>roll tabs forward", "notifications.create_channel_not_allowed.message": "Maximum on-going interactions reached", "extension_points.channels.live_chat.display_name": "Cha<PERSON>", "countries_dropdown.countries.lb": "Lebanon", "countries_dropdown.countries.mv": "Maldives", "extension_points.ui_blocks.components.email_editor.select_outbound_id": "Select outbound ID", "countries_dropdown.countries.mo": "Macau", "interaction_info.states.incoming_voice_consultation": "Incoming consultation", "extension_points.ui_blocks.components.email_editor.no_contacts_found": "No contacts found", "extension_points.ui_blocks.interaction_actions.buttons.dismiss_wrap_up.dialog.title": "Dismiss wrap-up?", "countries_dropdown.countries.fm": "Micronesia", "countries_dropdown.countries.md": "Moldova", "extension_points.ui_blocks.components.countries_dropdown.countries.pr": "Puerto Rico", "notifications.actions.reject_call": "Couldn't reject the call", "start_new_session.paragraph_2": "Unsaved changes will be lost and any call in progress will be automatically disconnected.", "countries_dropdown.countries.py": "Paraguay", "extension_points.notifications.actions.conference.remove_guest_failure.title": "Couldn’t remove guest", "countries_dropdown.countries.dj": "Djibouti", "extension_points.ui_blocks.components.countries_dropdown.countries.ye": "Yemen", "notifications.start_outbound_sms_error.title": "An error occurred", "extension_points.ui_blocks.components.email_editor.subject": "Subject", "extension_points.ui_blocks.components.email_editor.recipients": "Recipients", "notifications.unrecoverable_errors.wrap_up.message": "The wrap-up was automatically dismissed. If this problem persists, please contact your administrator", "extension_points.ui_blocks.components.countries_dropdown.countries.mf": "Saint <PERSON>", "extension_points.ui_blocks.components.countries_dropdown.countries.ae": "United Arab Emirates", "extension_points.ui_blocks.components.countries_dropdown.countries.at": "Austria", "phone_input.error_message": "Invalid number", "countries_dropdown.extension_label": "Dial an extension", "conversations_panel.tabs.active_tab.sla_policy_popup.hours_overdue": "Overdue {{hours}}h", "extension_points.ui_blocks.components.countries_dropdown.countries.sb": "Solomon Islands", "extension_points.ui_blocks.components.countries_dropdown.countries.wf": "Wallis and Futuna", "notifications.actions.transfer.failure.title": "Unable to transfer the call", "extension_points.ui_blocks.panel.targets_list.transfer_target.search_placeholder.agents": "Search for agents...", "panel.targets_list.transfer_target.search_placeholder": "Search for agents...", "extension_points.notifications.actions.wrap_up.dismiss": "Couldn't dismiss the wrap-up", "extension_points.notifications.agent_status_change_error.message": "Please make sure to manually update your status to the desired one", "extension_points.ui_blocks.components.caller_number_dropdown.placeholder": "Select a number", "extension_points.ui_blocks.components.countries_dropdown.countries.gd": "Grenada", "countries_dropdown.countries.kn": "Saint Kitts and Nevis", "extension_points.ui_blocks.interaction_info.states.incoming_voice_conference": "Incoming conference", "countries_dropdown.countries.bg": "Bulgaria", "extension_points.notifications.actions.park": "Couldn't park this conversation", "extension_points.notifications.file_upload.file_deleting": "File is being deleting, do not operate", "extension_points.ui_blocks.components.countries_dropdown.countries.nc": "New Caledonia", "templates.empty.title": "No activity yet", "extension_points.ui_blocks.components.countries_dropdown.countries.st": "São Tomé and Príncipe", "countries_dropdown.countries.dk": "Denmark", "extension_points.notifications.unrecoverable_errors.offer_outbound.title": "Couldn't load the dialing screen", "runtime.ui_engine.error.retry": "Retry", "countries_dropdown.countries.mg": "Madagascar", "notifications.actions.session_override.title": "Couldn't start a new session", "countries_dropdown.countries.br": "Brazil", "countries_dropdown.countries.se": "Sweden", "extension_points.ui_blocks.outbound_actions.button_labels.start_agent_voice_conversation": "Call an agent", "runtime.session_engine.notification.override_session_request_error.message": "Please try again. If the problem persists, please contact your administrator", "extension_points.ui_blocks.panel.targets_list.transfer_target.buttons.call": "Call", "core.notifications.permission_autoplay.message": "To unmute your ringtone temporarily, click anywhere on the page. You can also enable the ringtone permanently on the link below.", "countries_dropdown.countries.de": "Germany", "extension_points.ui_blocks.panel.subtitles.conference": "Add an agent or a number to a conference call", "extension_points.notifications.actions.send_to_queue.failure.title": "Couldn't send the chat to Inbox", "conversations_panel.tabs.active_tab.description_one": "{{count}} conversation", "extension_points.ui_blocks.components.countries_dropdown.countries.jp": "Japan", "extension_points.ui_blocks.components.countries_dropdown.countries.cx": "Christmas Islands", "extension_points.ui_blocks.components.countries_dropdown.countries.cc": "Cocos Islands", "extension_points.ui_blocks.components.countries_dropdown.countries.tv": "Tuvalu", "extension_points.ui_blocks.components.countries_dropdown.countries.an": "Netherlands Antilles", "notifications.actions.elevate_to_call": "Starting call", "countries_dropdown.countries.ua": "Ukraine", "runtime.session_engine.session_override.paragraph": "A new session was started in another tab or device. You can now close this one.", "extension_points.ui_blocks.components.countries_dropdown.countries.ch": "Switzerland", "extension_points.ui_blocks.panel.targets_list.error_states.ring_groups.title": "Could not load the ring groups list", "extension_points.notifications.actions.consultation.start": "Couldn't start the consultation", "dropdown.options.load_more": "Load more elements...", "countries_dropdown.countries.cf": "Central African Republic", "countries_dropdown.countries.nu": "Niue", "tab_host.failure.message": "There was a problem while trying to load this tab's contents.", "countries_dropdown.countries.bs": "Bahamas", "countries_dropdown.countries.vu": "Vanuatu", "extension_points.ui_blocks.interaction_info.states.incoming_voice_call": "Inbound call", "conversations_panel.tabs.active_tab.sla_policy_popup.days_and_hours": "{{days}}d {{hours}}h", "extension_points.ui_blocks.interaction_actions.buttons.transfer_consultation.text": "Transfer", "countries_dropdown.countries.ht": "Haiti", "notifications.unrecoverable_errors.prepare_blind_transfer.title": "Couldn't initiate the transfer", "countries_dropdown.countries.gb": "United Kingdom", "extension_points.commands.subtitles.conference_requested_conversation": "Conference: {{names}}", "extension_points.ui_blocks.components.countries_dropdown.countries.za": "South Africa", "extension_points.notifications.unrecoverable_errors.offer_outbound.message": "Please wait for the call to be picked up. If this problem persists, please contact your administrator", "extension_points.ui_blocks.components.caller_number_dropdown.error_load_more_message": "Error loading more items. Try again.", "extension_points.ui_blocks.components.countries_dropdown.countries.ve": "Venezuela", "conversations_panel.tabs.active_tab.failed_loading_channel_message_subtitle_without_action": "Please reload the app or refresh the page. If the problem persists, contact your administrator.", "conversations_panel.tabs.active_tab.sla_policy_popup.days_and_hours_overdue": "Overdue {{days}}d {{hours}}h", "extension_points.channels.google_business_messages.display_name": "Google Business Messages", "extension_points.notifications.actions.consultation.failure.message": "Please try again or select another destination", "countries_dropdown.countries.sc": "Seychelles", "extension_points.notifications.actions.end_text_conversation.failure.title": "Couldn't end the chat", "outbound_actions.caller_heading.title": "Outbound on:", "countries_dropdown.countries.kp": "North Korea", "extension_points.ui_blocks.components.caller_number_dropdown.store.refine_search": "{{count}} more numbers. Use the search to refine further.", "conversations_panel.nav_bar.close": "Close the conversations panel", "notifications.dialed_number_has_already_a_conversation_in_queue_error.message": "The dialed number already number has a conversation on <PERSON><PERSON>", "extension_points.ui_blocks.components.countries_dropdown.countries.bd": "Bangladesh", "extension_points.notifications.unknown.title": "An unexpected error occurred", "extension_points.ui_blocks.components.countries_dropdown.countries.ug": "Uganda", "extension_points.notifications.actions.conference.remove_guest_success.title": "Removed from conference", "notifications.actions.contact_switch": "Couldn't change to the selected contact", "countries_dropdown.countries.kr": "South Korea", "countries_dropdown.countries.ni": "Nicaragua", "agent_status.busy": "On a Call", "extension_points.notifications.actions.start_recording": "Couldn't start the recording", "caller_number_dropdown.store.default_friendly_name": "<PERSON><PERSON><PERSON>", "runtime.session_engine.browser.mode.only.paragraph_1": "To use Conversations App, please use Talkdesk on your web browser. Conversations app access via desktop app is disabled by your account administrator. Please reach out the administrator for more information.", "countries_dropdown.countries.jm": "Jamaica", "extension_points.notifications.actions.stop_recording": "Couldn't stop the recording", "extension_points.notifications.unrecoverable_errors.prepare_consultation.title": "Couldn't initiate the consultation", "extension_points.notifications.actions.send_email.failure.title": "Couldn't send the email", "extension_points.ui_blocks.components.countries_dropdown.countries.cr": "Costa Rica", "conversations_panel.tabs.active_tab.sla_policy_popup.days": "{{days}}d", "extension_points.commands.subtitles.consultation_received_conversation": "Regarding {{name}}", "tab_host.failure.title": "Could not load contents", "countries_dropdown.countries.ba": "Bosnia and Herzegovina", "extension_points.ui_blocks.components.countries_dropdown.countries.fm": "Micronesia", "extension_points.ui_blocks.components.email_editor.email_address_error": "The email address in the \"{{field}}\" field was not recognized", "extension_points.ui_blocks.components.countries_dropdown.countries.tn": "Tunisia", "extension_points.ui_blocks.components.chat_content.chat_message.unknown_message": "You’ve received an unsupported message", "extension_points.ui_blocks.components.phone_input.keypad_tooltip": "Keypad", "conversations_panel.tabs.active_tab.failed_loading_channel_message_subtitle": "Please reload the app or refresh the page. If the problem persists, contact your administrator.", "commands.unknown_resource": "Unknown Resource", "conversations_panel.tabs.active_tab.sla_policy_popup.minutes_and_seconds": "{{minutes}}m {{seconds}}s", "conversations_panel.tabs.active_tab.sla_policy_popup.more_than_99_days_overdue": "Overdue 99+ d", "countries_dropdown.countries.tj": "Tajikistan", "runtime.dialog_engine.dialog.conversation.unavailable.title": "Conversations unavailable", "runtime.session_engine.session_override.paragraph_1": "A new session was started in another tab or device.", "extension_points.notifications.emergency.no_address.message": "If you call 911, additional costs will incur. Please edit your profile and add an emergency address", "extension_points.ui_blocks.components.countries_dropdown.countries.sg": "Singapore", "notifications.actions.consultation.cancel": "Couldn't cancel the consultation", "extension_points.ui_blocks.components.email_editor.send": "Send", "countries_dropdown.countries.bl": "<PERSON>", "extension_points.notifications.actions.conference.add_guest_failure.message": "Please try again", "extension_points.notifications.file_upload.file_uploading": "File is being uploaded, do not operate", "extension_points.ui_blocks.email_content.reply": "Reply", "extension_points.ui_blocks.components.countries_dropdown.countries.sl": "Sierra Leone", "countries_dropdown.countries.sl": "Sierra Leone", "notifications.actions.consultation.contact_left_consultation": "Contact has left the call", "notifications.chat_error.title": "An unexpected error occurred", "countries_dropdown.countries.ye": "Yemen", "extension_points.ui_blocks.components.countries_dropdown.countries.mn": "Mongolia", "extension_points.ui_blocks.components.countries_dropdown.countries.dk": "Denmark", "extension_points.notifications.actions.auto_answer": "Couldn't acknowledge auto-answer", "extension_points.ui_blocks.components.countries_dropdown.countries.hu": "Hungary", "countries_dropdown.countries.qa": "Qatar", "extension_points.notifications.dialed_number_has_already_a_conversation_in_queue_error": "The dialed number already has a conversation on Inbox", "extension_points.notifications.actions.send_to_flow.success": "Conversation transferred successfully", "countries_dropdown.countries.ae": "United Arab Emirates", "countries_dropdown.countries.ms": "Montserrat", "extension_points.notifications.start_new_email_conversation_not_allowed.message": "There are no outbound touchpoints available. Please contact your administrator.", "extension_points.notifications.connection.internet_unstable": "Your internet connection is unstable", "notifications.unrecoverable_errors.prepare_agent_call.title": "Couldn't initiate the agent call", "extension_points.commands.another_colleague": "another colleague", "runtime.session_engine.desktop.mode.only.title": "Browser access is disabled", "trigger_outbound_actions.outbound_action": "Start Outbound", "extension_points.ui_blocks.email_content.starting_different_conversation_tips": "You are trying to send a reply to a different contact person. This will start a different conversation with that contact person. Do you want to proceed?", "translator.translated": "Translated", "extension_points.ui_blocks.components.countries_dropdown.countries.hn": "Honduras", "extension_points.ui_blocks.interaction_info_consultation.targetStates.hold_contact_conversation": "Talking", "countries_dropdown.countries.kg": "Kyrgyzstan", "extension_points.ui_blocks.components.countries_dropdown.countries.mt": "Malta", "extension_points.notifications.actions.establish.title": "Couldn't establish the call", "notifications.permission.autoplay.title": "Ringtone is muted", "panel.targets_list.empty_states.no_agents.message": "There are no agents to transfer this call to.", "conversations_panel.tabs.active_tab.failed_loading_channel_message_one": "Couldn't load the {{channels}} channel", "countries_dropdown.countries.bt": "Bhutan", "extension_points.commands.subtitles.conference_offer": "Multiple participants", "runtime.renderer.mode.error.content": "Please check if conversation-app widget is available!", "extension_points.ui_blocks.email_content.reply_all": "Reply All", "outbound_actions.button_labels.start_voice_conversation": "Call", "extension_points.ui_blocks.panel.titles.blind_transfer": "Blind transfer", "notifications.actions.session_override.message": "Please try again. If the problem persists, contact your administrator", "extension_points.ui_blocks.email_content.forward": "Forward", "extension_points.notifications.active_text_conversation_error.message": "Please refresh this page", "extension_points.ui_blocks.panel.targets_list.empty_states.no_favorites.title": "No favorites found", "notifications.unrecoverable_errors.wrap_up.title": "Couldn't load the wrap-up screen", "extension_points.notifications.permission.autoplay.message": "Allow the sound permission by clicking the lock icon next to the URL bar, click on Site Settings, and scroll down to find the Sound permissions. To unmute your ringtone temporarily, click anywhere on the page.", "notifications.invalid_number": "Invalid number", "dropdown.options.no_results": "Type at least 3 characters", "extension_points.ui_blocks.components.countries_dropdown.countries.gi": "Gibraltar", "panel.targets_list.empty_states.no_ring_groups.message": "There are no ring groups to transfer this call to.", "extension_points.ui_blocks.panel.subtitles.blind_transfer": "Transfer the call to an agent, ring group or number", "extension_points.notifications.unrecoverable_errors.prepare_conference.title": "Couldn’t add guest", "conversations_panel.group.incoming": "Incoming", "extension_points.notifications.actions.cancel_call": "Couldn't cancel the call", "extension_points.ui_blocks.email_content.placeholder": "Message {{name}}", "extension_points.ui_blocks.components.countries_dropdown.countries.ec": "Ecuador", "countries_dropdown.countries.pf": "French Polynesia", "extension_points.notifications.file_upload.supported_files": "Supported files: {{formats}}", "extension_points.ui_blocks.components.countries_dropdown.countries.jm": "Jamaica", "extension_points.commands.subtitles.email_received_conversation_with_name": "From {{name}} <{{email}}>", "extension_points.ui_blocks.filter_mode.clear_filter": "Clear filter", "extension_points.ui_blocks.components.countries_dropdown.countries.bb": "Barbados", "extension_points.ui_blocks.components.countries_dropdown.countries.ne": "Niger", "extension_points.notifications.actions.consultation.transfer_failure.title": "Couldn't transfer the call", "extension_points.ui_blocks.components.countries_dropdown.countries.nf": "Norfolk Island", "countries_dropdown.countries.bo": "Bolivia", "notifications.actions.target.empty_ring_group.title": "No agents assigned to this ring group", "extension_points.ui_blocks.components.countries_dropdown.countries.hr": "Croatia", "extension_points.ui_blocks.components.countries_dropdown.countries.je": "Jersey", "countries_dropdown.countries.cm": "Cameroon", "extension_points.notifications.chat_error.title": "An unexpected error occurred", "extension_points.notifications.file_upload.file_total_size_exceeds_maximum": "The total size of the attached files exceeds the maximum size allowed.", "extension_points.ui_blocks.interaction_actions.buttons.dismiss_wrap_up.text": "<PERSON><PERSON><PERSON>", "countries_dropdown.countries.no": "Norway", "notifications.actions.wrap_up.dismiss": "Couldn't dismiss the wrap-up", "countries_dropdown.countries.tn": "Tunisia", "extension_points.notifications.actions.target.empty_ring_group.message": "Please contact your administrator", "countries_dropdown.countries.gd": "Grenada", "extension_points.ui_blocks.interaction_actions.buttons.end_text_conversation.dialog.cancelText": "Back to chat", "countries_dropdown.countries.ve": "Venezuela", "runtime.session_engine.session_override.paragraph_2": "You can now close this one.", "extension_points.commands.topics.conference": "Conference", "extension_points.ui_blocks.components.countries_dropdown.countries.iq": "Iraq", "runtime.dialog_engine.dialog.conversation.closed.title": "Conversation closed", "extension_points.notifications.outbound_sms_action": "Couldn't send the SMS", "extension_points.templates.empty.message": "Start a new conversation or assign one yourself", "extension_points.ui_blocks.outbound_actions.button_labels.start_sms_conversation": "Start SMS", "extension_points.notifications.actions.accept_live_chat.failure.title": "Couldn't accept the Chat", "conversations_panel.channels.voice": "Voice", "countries_dropdown.countries.bi": "Burundi", "countries_dropdown.countries.jp": "Japan", "start_new_session.paragraph_1": "Conversations is already open in another tab or device. Click <strong>Start new session</strong> to open Conversations here instead.", "extension_points.ui_blocks.components.countries_dropdown.countries.bl": "<PERSON>", "extension_points.ui_blocks.interaction_actions.buttons.elevate_to_call.dialog.text": "This chat will be available during the call.", "notifications.actions.contact_left": "Contact ended the call", "extension_points.ui_blocks.components.caller_number_dropdown.error_fetch_settings_message": "Couldn't load the settings.", "countries_dropdown.countries.ci": "Côte d’Ivoire", "extension_points.notifications.actions.agent_call.failure": "Couldn't establish the call", "extension_points.notifications.unrecoverable_errors.offer.title": "Couldn't answer the call", "extension_points.notifications.create_channel_not_allowed.title": "Failed to start a new conversation", "extension_points.notifications.actions.establish.message": "An unexpected problem occurred while trying to establish this call", "core.notifications.permission_microphone.title": "The application needs access to the microphone", "countries_dropdown.countries.my": "Malaysia", "countries_dropdown.countries.ke": "Kenya", "countries_dropdown.countries.nc": "New Caledonia", "panel.targets_list.transfer_target.buttons.retry": "Retry", "extension_points.ui_blocks.components.countries_dropdown.countries.pw": "<PERSON><PERSON>", "conversations_panel.title": "Conversations", "extension_points.ui_blocks.components.countries_dropdown.countries.ba": "Bosnia and Herzegovina", "extension_points.ui_blocks.components.countries_dropdown.countries.gh": "Ghana", "countries_dropdown.countries.zm": "Zambia", "extension_points.ui_blocks.interaction_info.states.incoming_voice_agent_call": "Agent to agent call", "extension_points.ui_blocks.interaction_actions.buttons.end_conference.text": "End conference", "notifications.actions.stop_recording": "Couldn't stop the recording", "notifications.actions.consultation.failure.title": "Couldn't start the consultation call", "countries_dropdown.countries.at": "Austria", "countries_dropdown.countries.ng": "Nigeria", "extension_points.ui_blocks.components.caller_number_dropdown.loading": "Loading", "extension_points.channels.sms.display_name": "SMS", "extension_points.agent_status.unknown": "Unknown status", "extension_points.ui_blocks.components.countries_dropdown.countries.gn": "Guinea", "extension_points.ui_blocks.components.email_editor.no_outbound_id": "No Outbound ID", "extension_points.ui_blocks.components.countries_dropdown.countries.be": "Belgium", "countries_dropdown.countries.pk": "Pakistan", "countries_dropdown.countries.be": "Belgium", "dates.yesterday": "Yesterday", "countries_dropdown.countries.mp": "Northern Mariana Islands", "translator.hide_original": "Hide original", "notifications.actions.unhold": "Couldn't unhold", "extension_points.ui_blocks.panel.targets_list.empty_states.no_favorites.message": "There are no favorite numbers configured on your account", "extension_points.ui_blocks.tabs.left_control": "<PERSON><PERSON> tabs back", "extension_points.ui_blocks.components.countries_dropdown.countries.pe": "Peru", "notifications.actions.end_text_conversation.success": "Chat ended successfully", "countries_dropdown.countries.na": "Namibia", "extension_points.ui_blocks.components.countries_dropdown.countries.es": "Spain", "countries_dropdown.countries.cz": "Czech Republic", "countries_dropdown.countries.bq": "Caribbean Netherlands", "extension_points.commands.subtitles.email_requested_conversation": "To {{email}}", "notifications.active_text_conversation_error.title": "Couldn't load SMS", "countries_dropdown.countries.hr": "Croatia", "countries_dropdown.countries.cv": "Cape Verde", "runtime.session_engine.start_new_session.paragraph_2": "Unsaved changes will be lost and any call in progress will be automatically disconnected.", "extension_points.notifications.unrecoverable_errors.prepare_blind_transfer.title": "Couldn't initiate the transfer", "extension_points.ui_blocks.components.chat_content.campaign_message.name": "Campaign", "extension_points.notifications.actions.end_call.title": "Couldn't end the call", "extension_points.ui_blocks.components.countries_dropdown.countries.zw": "Zimbabwe", "phone_input.keypad_tooltip": "Keypad", "extension_points.ui_blocks.components.countries_dropdown.countries.tm": "Turkmenistan", "extension_points.channels.voice.display_name": "Voice", "panel.targets_list.tabs.labels.agents": "Agents", "extension_points.ui_blocks.components.countries_dropdown.countries.eh": "Western Sahara", "extension_points.notifications.error_persist_contact_administrator_try_again": "Please try again. If this problem persists, please contact your administrator", "extension_points.ui_blocks.components.countries_dropdown.countries.jo": "Jordan", "countries_dropdown.countries.in": "India", "extension_points.ui_blocks.components.countries_dropdown.countries.tk": "Tokelau", "countries_dropdown.countries.kz": "Kazakhstan", "extension_points.notifications.actions.conference.remove_guest_failure.message": "Please try again", "conversations_panel.tabs.active_tab.failed_loading_channel_message_ay11_other": "Couldn't load more than one channel", "extension_points.ui_blocks.components.countries_dropdown.countries.ee": "Estonia", "countries_dropdown.countries.pt": "Portugal", "extension_points.ui_blocks.components.countries_dropdown.countries.cz": "Czech Republic", "extension_points.ui_blocks.email_content.cc": "Cc", "test.polyglot": "Testing Polyglot Sync - Runtime", "caller_number_dropdown.error_message": "Couldn't load the list.", "countries_dropdown.countries.it": "Italy", "countries_dropdown.countries.ro": "Romania", "extension_points.notifications.conference.guest_connected.message": "Guest was successfully added to conference", "extension_points.ui_blocks.components.countries_dropdown.countries.kn": "Saint Kitts and Nevis", "extension_points.notifications.actions.consultation.failure.title": "Couldn't start the consultation call", "notifications.permission.microphone.message": "To make and receive phone calls, please grant permission to use the microphone", "extension_points.notifications.unrecoverable_errors.offer_consultation.title": "Couldn't answer the consultation", "extension_points.ui_blocks.components.chat_content.message_input.placeholder": "Message {{name}}", "extension_points.agent_status.after_call_work": "After Call Work", "countries_dropdown.countries.fo": "Faroe Islands", "message_input.button": "Send", "extension_points.ui_blocks.panel.targets_list.transfer_target.search_placeholder.favorites": "Search favorites...", "countries_dropdown.countries.fr": "France", "extension_points.ui_blocks.components.countries_dropdown.countries.az": "Azerbaijan", "extension_points.ui_blocks.email_content.start_different_conversation": "Start new conversation", "extension_points.ui_blocks.interaction_actions.buttons.end_text_conversation.text": "End chat", "extension_points.ui_blocks.components.countries_dropdown.countries.ly": "Libya", "translator.translating": "Translating...", "extension_points.notifications.permission.microphone.message": "To make and receive phone calls, please grant permission to use the microphone", "commands.unknown_contact": "Unknown Contact", "countries_dropdown.countries.tz": "Tanzania", "interaction_info_consultation.disconnected_tag": "Disconnected", "extension_points.ui_blocks.components.countries_dropdown.countries.ro": "Romania", "extension_points.ui_blocks.components.countries_dropdown.countries.ao": "Angola", "extension_points.ui_blocks.components.countries_dropdown.countries.gt": "Guatemala", "conversations_panel.tabs.active_tab.title": "Active", "extension_points.notifications.actions.consultation.transfer_success_received": "The call was transferred to you", "countries_dropdown.countries.id": "Indonesia", "interaction_info.states.incoming_voice_agent_call": "Agent to agent call", "commands.topics.default": "via {{topic}}", "extension_points.ui_blocks.components.countries_dropdown.countries.ps": "Palestine", "extension_points.ui_blocks.components.countries_dropdown.countries.ng": "Nigeria", "panel.targets_list.transfer_target.buttons.call": "Call", "conversations_panel.tabs.active_tab.sla_policy_popup.minutes_overdue": "Overdue {{minutes}}m", "extension_points.notifications.unrecoverable_errors.wrap_up.title": "Couldn't load the wrap-up screen", "countries_dropdown.countries.ml": "Mali", "countries_dropdown.countries.kw": "Kuwait", "extension_points.ui_blocks.interaction_actions.buttons.cancel.text": "End call", "translator.show_original": "Show original", "extension_points.ui_blocks.panel.targets_list.empty_states.no_ring_groups.title": "No ring groups found", "dropdown.placeholder": "Please select...", "extension_points.ui_blocks.components.caller_number_dropdown.retry": "Retry", "extension_points.ui_blocks.interaction_info.states.conversation_voice_call": "Talking", "extension_points.ui_blocks.email_content.cannot_forward_to_the_same_person": "Can't forward to the recipient that started the thread", "extension_points.ui_blocks.components.countries_dropdown.countries.ml": "Mali", "extension_points.ui_blocks.interaction_actions.buttons.start_transfer.text": "Blind transfer", "agent_status.away": "Away", "extension_points.ui_blocks.components.email_editor.Heading2": "Heading 2", "extension_points.ui_blocks.interaction_actions.buttons.dismiss_wrap_up.dialog.confirmText": "Yes, dismiss", "runtime.renderer.mode.error.title": "Widget connection lost", "countries_dropdown.countries.ru": "Russia", "countries_dropdown.countries.ge": "Georgia", "extension_points.ui_blocks.components.countries_dropdown.countries.tz": "Tanzania", "extension_points.notifications.actions.consultation.contact_left_consultation": "Contact has left the call", "extension_points.agent_status.away": "Away", "countries_dropdown.countries.tl": "Timor-Leste", "countries_dropdown.countries.gp": "Guadeloupe", "extension_points.ui_blocks.components.countries_dropdown.countries.cm": "Cameroon", "extension_points.ui_blocks.panel.targets_list.error_states.favorites.title": "Could not load the favorites list", "notifications.unrecoverable_errors.offer.title": "Couldn't answer the call", "extension_points.notifications.actions.consultation.switch_call.title": "Couldn't switch calls", "extension_points.ui_blocks.components.countries_dropdown.countries.mz": "Mozambique", "extension_points.ui_blocks.components.countries_dropdown.countries.gm": "Gambia", "countries_dropdown.countries.gn": "Guinea", "countries_dropdown.countries.cg": "Congo", "extension_points.ui_blocks.panel.targets_list.tabs.labels.ring_groups": "Ring groups", "countries_dropdown.countries.rw": "Rwanda", "panel.targets_list.error_states.ring_groups.title": "Could not load the ring groups list", "extension_points.ui_blocks.components.countries_dropdown.countries.pt": "Portugal", "countries_dropdown.countries.ps": "Palestine", "extension_points.commands.unknown_guest": "Unknown Guest", "extension_points.ui_blocks.components.countries_dropdown.countries.sv": "El Salvador", "countries_dropdown.countries.vi": "U.S. Virgin Islands", "commands.subtitles.consultation_requested_conversation": "Consulting {{name}}", "notifications.actions.cancel_call": "Couldn't cancel the call", "extension_points.notifications.actions.transfer.cancel": "Couldn't cancel the transfer", "notifications.error_persist_contact_administrator": "If this problem persists, please contact your administrator", "countries_dropdown.countries.us": "United States", "countries_dropdown.countries.gf": "French Guiana", "extension_points.ui_blocks.tabs.empty_state_title": "No content", "countries_dropdown.countries.ma": "Morocco", "extension_points.ui_blocks.components.countries_dropdown.countries.vg": "British Virgin Islands", "extension_points.ui_blocks.email_content.via": "via", "commands.subtitles.consultation_received_conversation": "Regarding {{name}}", "extension_points.ui_blocks.panel.targets_list.empty_states.no_ring_groups.message": "There are no ring groups to transfer this call to.", "conversations_panel.tabs.queue_tab.title": "Inbox", "extension_points.ui_blocks.components.email_editor.to": "To", "conversations_panel.tabs.active_tab.description": "{{count}} conversation", "extension_points.ui_blocks.components.countries_dropdown.countries.vi": "U.S. Virgin Islands", "countries_dropdown.countries.lv": "Latvia", "interaction_info.states.dial_voice_call": "Dialing", "conversations_panel.tabs.active_tab.sla_policy_popup.minutes": "{{minutes}}m", "extension_points.ui_blocks.components.countries_dropdown.countries.gf": "French Guiana", "extension_points.notifications.actions.contact_switch": "Couldn't change to the selected contact", "extension_points.commands.unknown_agent": "Unknown Agent", "countries_dropdown.countries.lc": "Saint Lucia", "countries_dropdown.countries.tr": "Turkey", "extension_points.ui_blocks.components.countries_dropdown.countries.cw": "Curaçao", "extension_points.ui_blocks.interaction_actions.buttons.reject_live_chat.text": "Reject", "extension_points.ui_blocks.components.chat_content.search_placeholder": "Search this conversation", "conversations_panel.tabs.active_tab.sla_policy_popup.days_overdue": "Overdue {{days}}d", "countries_dropdown.countries.vc": "Saint Vincent and the Grenadines", "countries_dropdown.countries.tg": "Togo", "extension_points.ui_blocks.interaction_info.states.after_voice_call": "Wrap-up", "countries_dropdown.countries.am": "Armenia", "extension_points.ui_blocks.components.countries_dropdown.countries.nu": "Niue", "countries_dropdown.countries.cr": "Costa Rica", "extension_points.notifications.actions.conference.start": "Couldn't start adding guest", "interaction_info.states.hold_conversation_call": "On hold", "runtime.ui_engine.error.title": "Couldn't load the page", "countries_dropdown.countries.hk": "Hong Kong", "conversations_panel.tabs.active_tab.title_assigned": "Assigned to you", "notifications.actions.consultation.start": "Couldn't start the consultation", "extension_points.ui_blocks.interaction_actions.buttons.reject.text": "Reject", "extension_points.ui_blocks.components.countries_dropdown.countries.fr": "France", "countries_dropdown.countries.lt": "Lithuania", "countries_dropdown.countries.nl": "Netherlands", "panel.targets_list.error_states.agents.message": "There was a problem loading the agents list, please try again.", "extension_points.ui_blocks.components.countries_dropdown.countries.md": "Moldova", "extension_points.ui_blocks.components.countries_dropdown.countries.ki": "Kiribati", "extension_points.ui_blocks.components.countries_dropdown.countries.uy": "Uruguay", "extension_points.ui_blocks.panel.targets_list.tabs.labels.favorites": "Favorites", "notifications.actions.transfer.start": "Couldn't start the transfer", "extension_points.ui_blocks.components.countries_dropdown.countries.gw": "Guinea-Bissau", "extension_points.commands.subtitles.email_received_conversation": "From {{email}}", "extension_points.notifications.file_upload.drag_tips": "Drag a file here", "countries_dropdown.countries.es": "Spain", "countries_dropdown.countries.pe": "Peru", "extension_points.notifications.actions.session_override.title": "Couldn't start a new session", "countries_dropdown.countries.ec": "Ecuador", "extension_points.notifications.actions.hold": "Couldn't hold", "countries_dropdown.countries.cw": "Curaçao", "tab_host.loading": "Loading...", "extension_points.ui_blocks.outbound_email.title": "New email", "extension_points.ui_blocks.components.email_editor.please_add_recipient": "Please add, at least, one recipient", "phone_input.placeholder": "Type or paste a number", "extension_points.ui_blocks.components.countries_dropdown.countries.pf": "French Polynesia", "extension_points.notifications.file_upload.maximum_tips": "Maximum file size: {{size}}", "conversations_panel.tabs.empty_state.message": "Your active conversations will be displayed here", "extension_points.ui_blocks.components.email_editor.reply_all": "Reply All", "interaction_info.states.live_chat_conversation_info": "Chatting on Live chat", "countries_dropdown.countries.li": "Liechtenstein", "countries_dropdown.countries.hu": "Hungary", "extension_points.ui_blocks.components.caller_number_dropdown.search_options.placeholder": "Search...", "notifications.actions.park": "Couldn't park this conversation", "extension_points.ui_blocks.components.countries_dropdown.countries.cn": "China", "core.notifications.permission_autoplay.action.button": "Learn more", "countries_dropdown.countries.np": "Nepal", "countries_dropdown.countries.vg": "British Virgin Islands", "countries_dropdown.countries.sh": "Saint Helena", "extension_points.ui_blocks.components.countries_dropdown.countries.si": "Slovenia", "extension_points.ui_blocks.interaction_actions.buttons.elevate_to_call.dialog.confirmText": "Call", "notifications.actions.establish.message": "An unexpected problem occurred while trying to establish this call", "extension_points.ui_blocks.components.interaction_header.actions.recording": "REC", "countries_dropdown.countries.co": "Colombia", "countries_dropdown.countries.gh": "Ghana", "notifications.actions.agent_call.start": "Couldn't start the call", "extension_points.notifications.actions.click_to_call": "Starting call", "panel.targets_list.empty_states.no_agents.title": "No agents found", "countries_dropdown.countries.iq": "Iraq", "extension_points.ui_blocks.components.countries_dropdown.countries.bg": "Bulgaria", "notifications.permission.autoplay.message": "Allow the sound permission by clicking the lock icon next to the URL bar, click on Site Settings, and scroll down to find the Sound permissions. To unmute your ringtone temporarily, click anywhere on the page.", "extension_points.ui_blocks.components.countries_dropdown.countries.bn": "Brunei", "extension_points.ui_blocks.interaction_info.states.email_conversation_info": "Emailing", "extension_points.ui_blocks.components.countries_dropdown.countries.mk": "Macedonia", "countries_dropdown.countries.pw": "<PERSON><PERSON>", "extension_points.ui_blocks.components.countries_dropdown.countries.tw": "Taiwan", "countries_dropdown.countries.ne": "Niger", "countries_dropdown.countries.lr": "Liberia", "panel.titles.consultation": "Consultation", "extension_points.ui_blocks.components.countries_dropdown.countries.ir": "Iran", "extension_points.ui_blocks.components.countries_dropdown.countries.zm": "Zambia", "extension_points.ui_blocks.components.countries_dropdown.countries.tt": "Trinidad and Tobago", "countries_dropdown.countries.aw": "Aruba", "extension_points.ui_blocks.components.countries_dropdown.countries.au": "Australia", "extension_points.notifications.active_text_conversation_error.title": "Couldn't load SMS", "countries_dropdown.countries.mh": "Marshall Islands", "extension_points.ui_blocks.components.countries_dropdown.countries.ht": "Haiti", "extension_points.ui_blocks.components.countries_dropdown.countries.sd": "Sudan", "extension_points.ui_blocks.components.chat_content.chat_message.location_message_title": "You’ve received a location message", "extension_points.ui_blocks.components.email_editor.shortcut_key_tips": "Cmd/Shift + Enter to send", "extension_points.ui_blocks.interaction_actions.buttons.accept_live_chat.text": "Accept <PERSON><PERSON>", "extension_points.ui_blocks.components.countries_dropdown.countries.kp": "North Korea", "extension_points.ui_blocks.components.countries_dropdown.countries.in": "India", "extension_points.notifications.error_persist_contact_administrator": "If this problem persists, please contact your administrator", "extension_points.ui_blocks.components.countries_dropdown.countries.sm": "San Marino", "extension_points.ui_blocks.interaction_actions.buttons.elevate_to_call.text": "Call", "extension_points.ui_blocks.interaction_info.states.whatsapp_conversation_info": "Chatting on WhatsApp", "countries_dropdown.countries.as": "American Samoa", "panel.targets_list.error_states.agents.title": "Could not load the agents list", "extension_points.channels.whatsapp.display_name": "WhatsApp", "extension_points.ui_blocks.interaction_actions.buttons.mute.true.text": "Unmute", "extension_points.notifications.actions.unhold": "Couldn't unhold", "extension_points.ui_blocks.components.countries_dropdown.countries.mx": "Mexico", "extension_points.ui_blocks.components.chat_content.reachability_status.contact_disconnected": "Contact has left the chat at {{time}}", "countries_dropdown.countries.sd": "Sudan", "extension_points.ui_blocks.panel.targets_list.empty_states.no_agents.message": "There are no agents to transfer this call to.", "extension_points.ui_blocks.components.countries_dropdown.countries.gg": "Guernsey", "notifications.actions.start_recording": "Couldn't start the recording", "extension_points.notifications.agent_status_change_error.title": "Couldn't update your status automatically", "core.notifications.permission_microphone.message": "To make and receive phone calls, please grant permission to use the microphone", "extension_points.ui_blocks.panel.targets_list.empty_states.no_agents.title": "No agents found", "extension_points.notifications.actions.agent_call.start": "Couldn't start the call", "notifications.active_text_conversation_error.message": "Please refresh this page", "notifications.actions.wrap_up.submit.title": "Couldn't submit the wrap-up", "conversations_panel.channels.live_chat": "Live chat", "extension_points.ui_blocks.components.email_editor.cannot_send_empty_message": "Can't send an empty message", "extension_points.notifications.actions.conference.remove_guest_success.message": "Guest was successfully removed from conference", "extension_points.ui_blocks.components.chat_content.automatic_message": "Automatic message", "extension_points.ui_blocks.components.countries_dropdown.countries.gq": "Equatorial Guinea", "countries_dropdown.countries.fk": "Falkland Islands", "extension_points.ui_blocks.interaction_actions.buttons.hold.true.text": "Unhold", "extension_points.ui_blocks.components.email_editor.send_tips": "Cmd + Enter to send", "extension_points.ui_blocks.components.countries_dropdown.countries.cl": "Chile", "countries_dropdown.countries.gt": "Guatemala", "extension_points.ui_blocks.components.countries_dropdown.countries.bh": "Bahrain", "extension_points.ui_blocks.panel.targets_list.transfer_target.search_hint": "Type at least 3 characters to start searching", "extension_points.ui_blocks.interaction_actions.buttons.start_add_guest.text": "Add guest", "extension_points.ui_blocks.email_content.hide_quoted_text": "<PERSON><PERSON> quoted text", "extension_points.ui_blocks.components.countries_dropdown.countries.pm": "Saint Pierre and Miquelon", "notifications.actions.consultation.transfer_failure.message": "Please try again", "extension_points.ui_blocks.panel.titles.conference": "Add guest", "extension_points.ui_blocks.panel.titles.agent_call": "Call an agent", "notifications.unrecoverable_errors.prepare_consultation.title": "Couldn't initiate the consultation", "extension_points.ui_blocks.components.countries_dropdown.countries.sx": "Sint Maarten", "extension_points.ui_blocks.interaction_actions.buttons.record.true.text": "Stop recording", "extension_points.commands.topics.agent_call": "Agent to agent call", "extension_points.ui_blocks.components.countries_dropdown.countries.tg": "Togo", "extension_points.ui_blocks.interaction_actions.buttons.hold.false.text": "Hold", "extension_points.notifications.file_upload.file_delete_error": "File delete error", "extension_points.ui_blocks.components.countries_dropdown.countries.hk": "Hong Kong", "extension_points.ui_blocks.components.countries_dropdown.countries.gb": "United Kingdom", "extension_points.ui_blocks.components.countries_dropdown.countries.kw": "Kuwait", "extension_points.ui_blocks.components.countries_dropdown.countries.sz": "Swaziland", "start_new_session.title": "Start new session", "extension_points.notifications.outbound_digital_whatsapp_action": "Couldn't send the WhatsApp", "conversations_panel.tabs.active_tab.sla_policy_popup.hours": "{{hours}}h", "extension_points.notifications.emergency.no_address.title": "No emergency address defined", "conversations_panel.tabs.active_tab.sla_policy_popup.minutes_and_seconds_overdue": "Overdue {{minutes}}m {{seconds}}s", "panel.targets_list.tabs.labels.external_number": "External number", "extension_points.ui_blocks.interaction_actions.buttons.send_to_queue.dialog.title": "Send to Inbox?", "extension_points.ui_blocks.components.countries_dropdown.countries.vn": "Vietnam", "extension_points.ui_blocks.components.countries_dropdown.countries.ci": "Ivory Coast", "countries_dropdown.countries.so": "Somalia", "interaction_info.states.incoming_voice_call": "Inbound call", "countries_dropdown.countries.az": "Azerbaijan", "interaction_info.states.incoming_voice_transfer_call": "Transferred call", "countries_dropdown.countries.fj": "Fiji", "extension_points.ui_blocks.components.countries_dropdown.countries.lc": "Saint Lucia", "extension_points.ui_blocks.components.countries_dropdown.countries.gl": "Greenland", "extension_points.ui_blocks.components.countries_dropdown.countries.kg": "Kyrgyzstan", "extension_points.ui_blocks.components.countries_dropdown.countries.uz": "Uzbekistan", "extension_points.ui_blocks.email_content.bcc": "Bcc", "extension_points.ui_blocks.email_content.show_details": "Show details", "extension_points.ui_blocks.components.countries_dropdown.countries.rw": "Rwanda", "outbound_actions.button_labels.start_sms_conversation": "Start SMS", "notifications.create_channel_not_allowed.title": "Failed to start a new conversation", "countries_dropdown.countries.eg": "Egypt", "extension_points.ui_blocks.components.phone_input.placeholder": "Type or paste a number", "extension_points.ui_blocks.components.countries_dropdown.countries.gr": "Greece", "extension_points.agent_status.offline": "Offline", "extension_points.ui_blocks.components.countries_dropdown.countries.kz": "Kazakhstan", "extension_points.ui_blocks.components.countries_dropdown.countries.py": "Paraguay", "countries_dropdown.countries.ly": "Libya", "countries_dropdown.countries.bw": "Botswana", "countries_dropdown.countries.za": "South Africa", "notifications.actions.mute": "Couldn't mute", "extension_points.ui_blocks.components.countries_dropdown.countries.mv": "Maldives", "countries_dropdown.countries.ir": "Iran", "notifications.actions.end_text_conversation.failure.title": "Couldn't end the chat", "commands.topics.agent_call": "Agent to agent call", "extension_points.ui_blocks.components.countries_dropdown.countries.lb": "Lebanon", "extension_points.ui_blocks.components.countries_dropdown.countries.na": "Namibia", "countries_dropdown.countries.nr": "Nauru", "core.notifications.session_already_exist.title": "Conversations is open in another tab or device", "extension_points.ui_blocks.components.countries_dropdown.countries.bm": "Bermuda", "extension_points.ui_blocks.interaction_info_consultation.disconnected_tag": "Disconnected", "extension_points.ui_blocks.interaction_info.states.incoming_live_chat_conversation": "Incoming Chat", "extension_points.ui_blocks.components.countries_dropdown.countries.ms": "Montserrat", "extension_points.ui_blocks.panel.targets_list.error_states.favorites.message": "There was a problem loading the favorites list, please try again.", "extension_points.ui_blocks.components.countries_dropdown.countries.fk": "Falkland Islands", "templates.empty.message": "Start a new conversation or assign one yourself", "extension_points.ui_blocks.outbound_actions.button_labels.start_voice_conversation": "Call", "countries_dropdown.countries.bb": "Barbados", "countries_dropdown.countries.st": "São Tomé and Príncipe", "translator.show_sent": "Show sent", "extension_points.notifications.actions.end_text_conversation.success": "Chat ended successfully", "outbound_actions.button_labels.start_agent_voice_conversation": "Call an agent", "error.title": "Couldn't load the page", "extension_points.ui_blocks.components.chat_content.assign_message": "The conversation was assigned at {{time}}", "extension_points.ui_blocks.interaction_actions.buttons.send_to_queue.dialog.text": "This chat will be sent back to the Inbox for agent reassignment.", "extension_points.ui_blocks.components.countries_dropdown.countries.to": "Tonga", "countries_dropdown.countries.ad": "Andorra", "countries_dropdown.countries.dm": "Dominica", "extension_points.ui_blocks.components.countries_dropdown.countries.se": "Sweden", "notifications.connection.internet_unstable": "Your internet connection is unstable", "extension_points.ui_blocks.components.countries_dropdown.countries.er": "Eritrea", "extension_points.ui_blocks.components.countries_dropdown.countries.re": "Réunion", "extension_points.notifications.cti_not_connected.title": "CTI is not connected", "extension_points.ui_blocks.email_content.starting_different_conversation": "Starting a different conversation?", "extension_points.notifications.conference.guest_disconnected": "Guest has left the call", "extension_points.ui_blocks.interaction_info.states.text_conversation_info": "Chatting on SMS", "extension_points.ui_blocks.interaction_info.states.incoming_voice_transfer_call": "Transferred call", "extension_points.ui_blocks.components.countries_dropdown.countries.bo": "Bolivia", "extension_points.ui_blocks.components.countries_dropdown.countries.sc": "Seychelles", "countries_dropdown.countries.ai": "<PERSON><PERSON><PERSON>", "countries_dropdown.countries.ie": "Ireland", "extension_points.ui_blocks.components.countries_dropdown.countries.ky": "Cayman Islands", "countries_dropdown.countries.bd": "Bangladesh", "extension_points.notifications.actions.consultation.end.title": "Couldn't end the consultation", "extension_points.ui_blocks.panel.targets_list.transfer_target.store.refine_search": "{{count}} more agents. Use the search to refine further.", "conversations_panel.tabs.active_tab.failed_loading_channel_message_button": "Reload", "countries_dropdown.countries.ug": "Uganda", "countries_dropdown.countries.by": "Belarus", "extension_points.commands.topics.consultation": "Consultation", "extension_points.notifications.actions.mute": "Couldn't mute", "notifications.actions.consultation.failure.message": "Please try again or select another destination", "interaction_info.states.text_conversation_info": "Chatting on SMS", "caller_number_dropdown.search_options.placeholder": "Search...", "extension_points.notifications.actions.send_fbm_failure.title": "Couldn't send the message", "dropdown.error_load_more_message": "Error loading more items. Try again.", "extension_points.ui_blocks.components.countries_dropdown.countries.mr": "Mauritania", "extension_points.ui_blocks.components.countries_dropdown.countries.mc": "Monaco", "extension_points.ui_blocks.components.countries_dropdown.countries.va": "Vatican City", "extension_points.channels.apple_messages_for_business.display_name": "Apple Messages For Business", "session_override.paragraph": "A new session was started in another tab or device. You can now close this one.", "extension_points.notifications.actions.consultation.cancel": "Couldn't cancel the consultation", "countries_dropdown.countries.jo": "Jordan", "countries_dropdown.countries.rs": "Serbia", "conversations_panel.tabs.queue_tab.failure_widget.retry": "Try again", "extension_points.notifications.actions.transfer.success": "Call transferred successfully", "countries_dropdown.countries.gi": "Gibraltar", "extension_points.ui_blocks.components.email_editor.please_select_outbound_id": "Please select outbound ID", "conversations_panel.tabs.active_tab.sort_by_oldest_assignment_option": "Oldest assignments", "extension_points.ui_blocks.components.countries_dropdown.countries.ma": "Morocco"}