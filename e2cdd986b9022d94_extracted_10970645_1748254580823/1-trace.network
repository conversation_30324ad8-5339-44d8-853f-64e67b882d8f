{"type": "resource-snapshot", "snapshot": {"_monotonicTime": 66058.202, "startedDateTime": "2025-05-25T20:03:06.213Z", "time": 353.1240000000107, "request": {"method": "GET", "url": "https://po-automationca.mytalkdeskca.com/atlas/access_token?scopes%5B%5D=campaign-do-not-call-lists%3Aread%2Ccampaign-do-not-call-lists%3Awrite%2Ccampaigns%3Aread%2Ccampaigns%3Awrite%2Cdo-not-call-lists%3Amanage%2Cdo-not-call-lists%3Aread%2Cdo-not-call-lists%3Awrite%2Cflows%3Aread%2Cgraph-users%3Aread%2Cnumbers%3Aread%2Copenid%2Cphone-details%3Aread%2Cphone-info%3Aread%2Cpolicies%3Aevaluate%2Crecord-lists%3Amanage%2Crecord-lists%3Aread%2Crecord-lists%3Awrite%2Cring-groups%3Aread%2Crtm-settings%3Aread%2Cschedule-callbacks%3Aread%2Cschedule-callbacks%3Awrite%2Cteams%3Aread%2Cuser-ring-groups%3Aread%2Cusers%3Aread", "httpVersion": "HTTP/1.1", "cookies": [{"name": "SESSION", "value": "YzEwN2VmMTAtZDllNS00OTBjLTlhYTgtZDgxYTdmZjMwMWVl"}, {"name": "SESSION", "value": "MzE0NDhmMDAtZTA5Yi00MTM1LTg3NzQtOTM5NTdiZWM1ZGIx"}, {"name": "dt<PERSON><PERSON><PERSON>", "value": "v_4_srv_2_sn_3FC9FFACCA65EDB56392EB0AC3474196_perc_100000_ol_0_mul_1_app-3Aea7c4b59f27d43eb_1"}], "headers": [{"name": "user-agent", "value": "Mozilla/5.0"}, {"name": "accept", "value": "application/json, text/plain, */*"}, {"name": "accept-encoding", "value": "gzip, deflate, br"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Content-Type", "value": "application/json"}, {"name": "DNT", "value": "1"}, {"name": "expires", "value": "0"}, {"name": "sec-ch-ua", "value": "\" Not A;Brand\",v =\"99\",\"Chromium\";v = \"100\", \"Google Chrome\";v = \"100\""}, {"name": "Sec-Fetch-Site", "value": "same-origin"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Accept-Language", "value": "zh,en;q=0.9,zh-CN;q=0.8"}, {"name": "cookie", "value": "SESSION=YzEwN2VmMTAtZDllNS00OTBjLTlhYTgtZDgxYTdmZjMwMWVl; SESSION=MzE0NDhmMDAtZTA5Yi00MTM1LTg3NzQtOTM5NTdiZWM1ZGIx; dtCookie=v_4_srv_2_sn_3FC9FFACCA65EDB56392EB0AC3474196_perc_100000_ol_0_mul_1_app-3Aea7c4b59f27d43eb_1"}], "queryString": [{"name": "scopes[]", "value": "campaign-do-not-call-lists:read,campaign-do-not-call-lists:write,campaigns:read,campaigns:write,do-not-call-lists:manage,do-not-call-lists:read,do-not-call-lists:write,flows:read,graph-users:read,numbers:read,openid,phone-details:read,phone-info:read,policies:evaluate,record-lists:manage,record-lists:read,record-lists:write,ring-groups:read,rtm-settings:read,schedule-callbacks:read,schedule-callbacks:write,teams:read,user-ring-groups:read,users:read"}], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "1.1", "cookies": [{"domain": ".mytalkdeskca.com", "path": "/atlas", "expires": "1970-01-21T14:22:19.386Z", "httpOnly": true, "secure": true, "sameSite": "Lax", "name": "ATLAS_DEVICE_ID", "value": "ad681f78-1ea8-46ce-ad81-28b0bf63c06d"}, {"domain": "po-automationca.mytalkdeskca.com", "path": "/atlas", "httpOnly": true, "secure": true, "sameSite": "None", "name": "SESSION", "value": "MzE0NDhmMDAtZTA5Yi00MTM1LTg3NzQtOTM5NTdiZWM1ZGIx"}], "headers": [{"name": "Date", "value": "Sun, 25 May 2025 20:03:06 GMT"}, {"name": "Content-Type", "value": "application/json"}, {"name": "Transfer-Encoding", "value": "chunked"}, {"name": "Connection", "value": "keep-alive"}, {"name": "x-oneagent-js-injection", "value": "true"}, {"name": "x-talkdesk-correlation-id", "value": "38d8d204-a731-426a-a71b-00f1b07fe3b1"}, {"name": "Set-<PERSON><PERSON>", "value": "ATLAS_DEVICE_ID=ad681f78-1ea8-46ce-ad81-28b0bf63c06d; Max-Age=31536000; Expires=Mon, 25 May 2026 20:03:06 GMT; Domain=mytalkdeskca.com; Path=/atlas; Secure; HttpOnly"}, {"name": "Set-<PERSON><PERSON>", "value": "SESSION=MzE0NDhmMDAtZTA5Yi00MTM1LTg3NzQtOTM5NTdiZWM1ZGIx; Path=/atlas; Secure; HttpOnly; SameSite=none"}, {"name": "content-security-policy", "value": "frame-ancestors https://*.visualforce.com https://*.visual.force.com https://*.my.salesforce.com https://*.lightning.force.com https://*.service-now.com https://*.zendesk.com https://*.apps.zdusercontent.com https://prd-cdn-talkdesk.talkdesk.com https://*.crm.ondemand.com https://*.vf.force.com https://*.hubspot.com https://*.dynamics.com https://*.smartcmobile.net https://*.smartcmobile.com https://*.custhelp.com https://*.kustomer.com https://*.kustomerapp.com https://*.creatio.com https://*.genworth.com https://*.slbenfica.pt https://*.gbes https://*.devoted.com https://*.epic.com; object-src 'none'; base-uri 'none'; script-src 'nonce-10YhdTjvAVaxS8BmgTfVqLHHUB+Jo4zn7k+kIkppwtrtKp/nrk1aJlXbxCD73Q9pKdUnxZCK6CpWw5oXLwOfQAPy42xghtgddQQ0SEhAW77srG2l4j5uKHDtqyPWGt7lOfIdm1OMGHqwCkKkikyvVwGiSzhbSxXBg2lKPzJP7qs=' 'strict-dynamic' 'unsafe-inline' http: https:"}, {"name": "x-content-type-options", "value": "nosniff"}, {"name": "x-xss-protection", "value": "0"}, {"name": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"name": "pragma", "value": "no-cache"}, {"name": "expires", "value": "0"}, {"name": "strict-transport-security", "value": "max-age=31536000 ; includeSubDomains"}, {"name": "server-timing", "value": "dtSInfo;desc=\"0\", dtRpid;desc=\"-871674912\""}, {"name": "x-td-provider-region", "value": "ca-central-1"}, {"name": "cf-cache-status", "value": "DYNAMIC"}, {"name": "Server", "value": "cloudflare"}, {"name": "CF-RAY", "value": "9457a25c5f9739ac-IAD"}, {"name": "Content-Encoding", "value": "gzip"}], "content": {"size": 1566, "mimeType": "application/json", "_sha1": "5ebe492278ac7e1b520336786be24974d6c459fe.json"}, "headersSize": -1, "bodySize": 1566, "redirectURL": "", "_transferSize": -1}, "cache": {}, "timings": {"send": 79.02599999999802, "wait": 221.61000000000058, "receive": 5.961999999999534, "dns": 37.38000000000466, "connect": 78.7100000000064, "ssl": 9.461999999999534, "blocked": -1}, "_apiRequest": true, "serverIPAddress": "*************", "_serverPort": 443, "_securityDetails": {"protocol": "TLSv1.3", "subjectName": "mytalkdeskca.com", "validFrom": **********, "validTo": **********, "issuer": "WE1"}}}