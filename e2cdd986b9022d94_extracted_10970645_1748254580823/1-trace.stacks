{"files": ["/pages/basePage.js", "/pages/outbound_dialer/dialerPage.js", "/tests/smoke/test_campaign_record_flow_clone.spec.js"], "stacks": [[1235, [[0, 228, 23, "DialerPage.retry_goto"], [1, 328, 14, "DialerPage.waitForCampaignPage"], [2, 1392, 30, ""], [2, 1387, 20, ""], [2, 1383, 16, ""]]], [1237, [[0, 229, 29, "DialerPage.retry_goto"], [1, 328, 14, "DialerPage.waitForCampaignPage"], [2, 1392, 30, ""], [2, 1387, 20, ""], [2, 1383, 16, ""]]], [1251, [[0, 356, 25, "DialerPage.wait_for_timeout"], [0, 278, 20, "DialerPage.waitForError_new"], [0, 231, 24, "DialerPage.retry_goto"], [1, 328, 3, "DialerPage.waitForCampaignPage"], [2, 1392, 13, ""], [2, 1387, 9, ""], [2, 1383, 5, ""]]], [1253, [[0, 283, 37, "DialerPage.waitForError_new"], [0, 231, 13, "DialerPage.retry_goto"], [1, 328, 3, "DialerPage.waitForCampaignPage"], [2, 1392, 13, ""], [2, 1387, 9, ""], [2, 1383, 5, ""]]], [1255, [[0, 486, 25, "DialerPage.hiddenConflictToast"], [0, 232, 24, "DialerPage.retry_goto"], [1, 328, 3, "DialerPage.waitForCampaignPage"], [2, 1392, 13, ""], [2, 1387, 9, ""], [2, 1383, 5, ""]]], [1257, [[1, 329, 38, "DialerPage.waitForCampaignPage"], [2, 1392, 13, ""], [2, 1387, 9, ""], [2, 1383, 5, ""]]], [1259, [[1, 412, 31, "DialerPage.getPageTitle"], [1, 330, 27, "DialerPage.waitForCampaignPage"], [2, 1392, 13, ""], [2, 1387, 9, ""], [2, 1383, 5, ""]]], [1261, [[0, 292, 47, "DialerPage.waitForVisibleLocator"], [1, 346, 38, "DialerPage.waitForFrameLoad"], [1, 331, 14, "DialerPage.waitForCampaignPage"], [2, 1392, 13, ""], [2, 1387, 9, ""], [2, 1383, 5, ""]]], [1263, [[1, 386, 44, "DialerPage.waitForFrameLoad"], [1, 331, 3, "DialerPage.waitForCampaignPage"], [2, 1392, 13, ""], [2, 1387, 9, ""], [2, 1383, 5, ""]]], [1265, [[1, 388, 34, "DialerPage.waitForFrameLoad"], [1, 331, 3, "DialerPage.waitForCampaignPage"], [2, 1392, 13, ""], [2, 1387, 9, ""], [2, 1383, 5, ""]]], [1267, [[1, 395, 42, "DialerPage.waitForFrameLoad"], [1, 331, 3, "DialerPage.waitForCampaignPage"], [2, 1392, 13, ""], [2, 1387, 9, ""], [2, 1383, 5, ""]]], [1269, [[0, 522, 37, "DialerPage.waitAndClearToast"], [1, 332, 14, "DialerPage.waitForCampaignPage"], [2, 1392, 13, ""], [2, 1387, 9, ""], [2, 1383, 5, ""]]], [1271, [[1, 982, 32, "DialerPage.clickCreateCampaign"], [1, 973, 14, "DialerPage.createCampaign"], [2, 1394, 30, ""], [2, 1387, 9, ""], [2, 1383, 5, ""]]]]}