{"version":7,"type":"context-options","origin":"testRunner","browserName":"","options":{},"platform":"linux","wallTime":1748203409366,"monotonicTime":89210.811,"sdkLanguage":"javascript"}
{"type":"before","callId":"hook@1","startTime":89211.881,"class":"Test","method":"step","apiName":"Before Hooks","params":{},"stack":[]}
{"type":"before","callId":"hook@2","parentId":"hook@1","startTime":89212.157,"class":"Test","method":"step","apiName":"beforeEach hook","params":{},"stack":[{"file":"/tests/smoke/test_campaign_record_flow_clone.spec.js","line":36,"column":6}]}
{"type":"stdout","timestamp":89215.266,"text":"==== 【po_automationca】Running test Smoke-Preview Campaign Create with worker pid 556 on chromium=====\n"}
{"type":"after","callId":"hook@2","endTime":89215.309,"attachments":[]}
{"type":"before","callId":"fixture@3","parentId":"hook@1","startTime":89215.804,"class":"Test","method":"step","apiName":"fixture: context","params":{},"stack":[]}
{"type":"before","callId":"pw:api@4","parentId":"fixture@3","startTime":89216.855,"class":"Test","method":"step","apiName":"browser.newContext","params":{"acceptDownloads":"accept","bypassCSP":"false","colorScheme":"light","deviceScaleFactor":"1","hasTouch":"false","ignoreHTTPSErrors":"false","isMobile":"false","javaScriptEnabled":"true","locale":"en-US","offline":"false","permissions":"[microphone, notifications, camera]","storageState":"Object","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.6778.33 Safari/537.36","viewport":"Object","baseURL":"https://po-automationca.mytalkdeskca.com","serviceWorkers":"allow","recordVideo":"Object","noDefaultViewport":"false","extraHTTPHeaders":"undefined","recordHar":"undefined","reducedMotion":"undefined","forcedColors":"undefined","clientCertificates":"undefined"},"stack":[]}
{"type":"after","callId":"pw:api@4","endTime":89704.519,"attachments":[]}
{"type":"after","callId":"fixture@3","endTime":89707.776,"attachments":[]}
{"type":"before","callId":"fixture@5","parentId":"hook@1","startTime":89708.041,"class":"Test","method":"step","apiName":"fixture: page","params":{},"stack":[]}
{"type":"before","callId":"pw:api@6","parentId":"fixture@5","startTime":89708.747,"class":"Test","method":"step","apiName":"browserContext.newPage","params":{},"stack":[]}
{"type":"after","callId":"pw:api@6","endTime":89743.513,"attachments":[]}
{"type":"after","callId":"fixture@5","endTime":89743.555,"attachments":[]}
{"type":"after","callId":"hook@1","endTime":89743.573,"attachments":[]}
{"type":"before","callId":"test.step@7","startTime":89745.421,"class":"Test","method":"step","apiName":"Smoke-RCX-3738：Create a Preview Campaign","params":{},"stack":[{"file":"/tests/smoke/test_campaign_record_flow_clone.spec.js","line":1383,"column":16}]}
{"type":"before","callId":"test.step@8","parentId":"test.step@7","startTime":89745.843,"class":"Test","method":"step","apiName":"Step1-Click on Create campaign button and write valid for campaign name and click on create button","params":{},"stack":[{"file":"/tests/smoke/test_campaign_record_flow_clone.spec.js","line":1387,"column":20}]}
{"type":"before","callId":"pw:api@9","parentId":"test.step@8","startTime":89746.769,"class":"Test","method":"step","apiName":"page.goto","params":{"url":"/atlas/apps/outbound-dialer","waitUntil":"load"},"stack":[{"file":"/pages/basePage.js","line":228,"column":23,"function":"DialerPage.retry_goto"}]}
{"type":"before","callId":"pw:api@10","parentId":"test.step@8","startTime":89747.92,"class":"Test","method":"step","apiName":"page.waitForNavigation","params":{"info":"Object"},"stack":[{"file":"/pages/basePage.js","line":229,"column":29,"function":"DialerPage.retry_goto"}]}
{"type":"after","callId":"pw:api@10","endTime":90616.324,"attachments":[]}
{"type":"before","callId":"pw:api@11","parentId":"test.step@8","startTime":90617.178,"class":"Test","method":"step","apiName":"page.waitForTimeout","params":{"timeout":"3000"},"stack":[{"file":"/pages/basePage.js","line":356,"column":25,"function":"DialerPage.wait_for_timeout"}]}
{"type":"after","callId":"pw:api@9","endTime":90734.655,"attachments":[]}
{"type":"after","callId":"pw:api@11","endTime":93809.719,"attachments":[]}
{"type":"before","callId":"pw:api@12","parentId":"test.step@8","startTime":93810.85,"class":"Test","method":"step","apiName":"locator.isVisible","params":{"selector":"div:not([style*=\"display: none\"])[data-testid=\"app\"] svg.loader-module__loader >> nth=0","strict":"true"},"stack":[{"file":"/pages/basePage.js","line":283,"column":37,"function":"DialerPage.waitForError_new"}]}
{"type":"after","callId":"pw:api@12","endTime":93944.214,"attachments":[]}
{"type":"before","callId":"pw:api@13","parentId":"test.step@8","startTime":93945.543,"class":"Test","method":"step","apiName":"page.evaluate","params":{"expression":"() => {\n      console.log(\"========START EXECUTE JQUERY=============\");\n      // let toastRoot = document.querySelector('div#toast');\n      const toastRoot = document.querySelector('div[data-testid=\"toaster\"]');\n      let MutationObserver = window.MutationObserver || window.WebKitMutationObserver || window.MozMutationObserver;\n      let mutationObserver = new MutationObserver(mutations => {\n        const toast = document.querySelector('div[data-testid=\"toaster\"] div[data-co-name=\"Message\"] div[data-testid]');\n        const toast_content = document.querySelector('div[data-testid=\"toaster\"] div[data-co-name=\"Message\"] div[data-testid] p').textContent;\n        console.log(toast);\n        if (toast_content.includes('Conversations is open in another tab or device')) {\n          console.log(\"=====find the conversation conflict toast======\");\n          toast.remove();\n        } else {\n          console.log(\"=======No need to fix this toast=======\");\n        }\n      });\n      mutationObserver.observe(toastRoot, {\n        childList: true,\n        //\n        // attributes: true, //\n        // characterData: true, //\n        subtree: true //\n        // attributesFilter: ['class', 'style'], //\n        // attributesOldValue: true, //\n        // characterDataOldValue: true //\n      });\n      console.log(\"========START EXECUTE JQUERY=============\");\n    }","isFunction":"true","arg":"Object"},"stack":[{"file":"/pages/basePage.js","line":486,"column":25,"function":"DialerPage.hiddenConflictToast"}]}
{"type":"after","callId":"pw:api@13","endTime":93958.772,"attachments":[]}
{"type":"before","callId":"expect@14","parentId":"test.step@8","startTime":93959.78,"class":"Test","method":"step","apiName":"expect.toBeVisible","params":{},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":329,"column":38,"function":"DialerPage.waitForCampaignPage"}]}
{"type":"after","callId":"expect@14","endTime":93971.498,"attachments":[]}
{"type":"before","callId":"pw:api@15","parentId":"test.step@8","startTime":93972.366,"class":"Test","method":"step","apiName":"locator.textContent","params":{"selector":"header.dock-drawer-component-module__header h4","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":412,"column":31,"function":"DialerPage.getPageTitle"}]}
{"type":"after","callId":"pw:api@15","endTime":94004.721,"attachments":[]}
{"type":"before","callId":"expect@16","parentId":"test.step@8","startTime":94005.48,"class":"Test","method":"step","apiName":"expect.toEqual","params":{"expected":"Dialer"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":330,"column":43,"function":"DialerPage.waitForCampaignPage"}]}
{"type":"after","callId":"expect@16","endTime":94005.926,"attachments":[]}
{"type":"before","callId":"pw:api@17","parentId":"test.step@8","startTime":94006.808,"class":"Test","method":"step","apiName":"locator.isVisible","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> header h1","strict":"true"},"stack":[{"file":"/pages/basePage.js","line":292,"column":47,"function":"DialerPage.waitForVisibleLocator"}]}
{"type":"after","callId":"pw:api@17","endTime":94113.477,"attachments":[]}
{"type":"before","callId":"pw:api@18","parentId":"test.step@8","startTime":94114.479,"class":"Test","method":"step","apiName":"locator.isVisible","params":{"selector":"button.secondary-area-module__active","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":386,"column":44,"function":"DialerPage.waitForFrameLoad"}]}
{"type":"after","callId":"pw:api@18","endTime":94216.856,"attachments":[]}
{"type":"stdout","timestamp":94217.113,"text":"get Second Area Panel again\n"}
{"type":"before","callId":"pw:api@19","parentId":"test.step@8","startTime":94218.833,"class":"Test","method":"step","apiName":"locator.isVisible","params":{"selector":"div._pendo-step-container-size","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":388,"column":34,"function":"DialerPage.waitForFrameLoad"}]}
{"type":"after","callId":"pw:api@19","endTime":94329.754,"attachments":[]}
{"type":"stdout","timestamp":94329.809,"text":"the recommandAdv is hidden\n"}
{"type":"before","callId":"pw:api@20","parentId":"test.step@8","startTime":94330.855,"class":"Test","method":"step","apiName":"locator.click","params":{"selector":"button.secondary-area-module__active","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":395,"column":42,"function":"DialerPage.waitForFrameLoad"}]}
{"type":"after","callId":"pw:api@20","endTime":94786.374,"attachments":[]}
{"type":"stdout","timestamp":94786.432,"text":"The frame content load successfully.\n"}
{"type":"before","callId":"pw:api@21","parentId":"test.step@8","startTime":94787.488,"class":"Test","method":"step","apiName":"locator.isVisible","params":{"selector":"div[data-testid=\"toaster\"] div[data-co-name=\"Message\"]:nth-child(1) div[data-testid]","strict":"true"},"stack":[{"file":"/pages/basePage.js","line":522,"column":37,"function":"DialerPage.waitAndClearToast"}]}
{"type":"after","callId":"pw:api@21","endTime":94807.897,"attachments":[]}
{"type":"before","callId":"pw:api@22","parentId":"test.step@8","startTime":94808.908,"class":"Test","method":"step","apiName":"locator.click","params":{"selector":"iframe[data-testid=\"frame-outbound-dialer\"] >> internal:control=enter-frame >> div button[data-pendo-campaignlist-header-createbutton]","strict":"true"},"stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":982,"column":32,"function":"DialerPage.clickCreateCampaign"}]}
{"type":"after","callId":"pw:api@22","endTime":154944.578,"attachments":[],"error":{"name":"","message":"TimeoutError: locator.click: Timeout 60000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div button[data-pendo-campaignlist-header-createbutton]')\u001b[22m\n\u001b[2m    - locator resolved to <button data-co-name=\"Button\" data-co-version=\"1.4.0\" data-co-project=\"cobalt\" data-pendo-campaignlist-header-createbutton=\"true\" class=\"react-button_1-4-0_co-button react-button_1-4-0_co-button--primary react-button_1-4-0_co-button--medium\">Create campaign</button>\u001b[22m\n\u001b[2m  - attempting click action\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is visible, enabled and stable\u001b[22m\n\u001b[2m      - scrolling into view if needed\u001b[22m\n\u001b[2m      - done scrolling\u001b[22m\n\u001b[2m      - <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m    - waiting 20ms\u001b[22m\n\u001b[2m    - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m    - element is visible, enabled and stable\u001b[22m\n\u001b[2m    - scrolling into view if needed\u001b[22m\n\u001b[2m    - done scrolling\u001b[22m\n\u001b[2m    - <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m  2 × retrying click action\u001b[22m\n\u001b[2m      - waiting 100ms\u001b[22m\n\u001b[2m      - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is visible, enabled and stable\u001b[22m\n\u001b[2m      - scrolling into view if needed\u001b[22m\n\u001b[2m      - done scrolling\u001b[22m\n\u001b[2m      - <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m  28 × retrying click action\u001b[22m\n\u001b[2m       - waiting 500ms\u001b[22m\n\u001b[2m       - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m       - element is visible, enabled and stable\u001b[22m\n\u001b[2m       - scrolling into view if needed\u001b[22m\n\u001b[2m       - done scrolling\u001b[22m\n\u001b[2m       - <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m     - retrying click action\u001b[22m\n\u001b[2m       - waiting 500ms\u001b[22m\n\u001b[2m       - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m       - element is visible, enabled and stable\u001b[22m\n\u001b[2m       - scrolling into view if needed\u001b[22m\n\u001b[2m       - done scrolling\u001b[22m\n\u001b[2m       - <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m     - retrying click action\u001b[22m\n\u001b[2m       - waiting 500ms\u001b[22m\n\u001b[2m       - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m       - element is visible, enabled and stable\u001b[22m\n\u001b[2m       - scrolling into view if needed\u001b[22m\n\u001b[2m       - done scrolling\u001b[22m\n\u001b[2m       - <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m     - retrying click action\u001b[22m\n\u001b[2m       - waiting 500ms\u001b[22m\n\u001b[2m       - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m       - element is visible, enabled and stable\u001b[22m\n\u001b[2m       - scrolling into view if needed\u001b[22m\n\u001b[2m       - done scrolling\u001b[22m\n\u001b[2m       - <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m  - retrying click action\u001b[22m\n\u001b[2m    - waiting 500ms\u001b[22m\n\u001b[2m    - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m    - element is visible, enabled and stable\u001b[22m\n\u001b[2m    - scrolling into view if needed\u001b[22m\n\u001b[2m    - done scrolling\u001b[22m\n\u001b[2m    - <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m  - retrying click action\u001b[22m\n\u001b[2m    - waiting 500ms\u001b[22m\n","stack":"TimeoutError: locator.click: Timeout 60000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div button[data-pendo-campaignlist-header-createbutton]')\u001b[22m\n\u001b[2m    - locator resolved to <button data-co-name=\"Button\" data-co-version=\"1.4.0\" data-co-project=\"cobalt\" data-pendo-campaignlist-header-createbutton=\"true\" class=\"react-button_1-4-0_co-button react-button_1-4-0_co-button--primary react-button_1-4-0_co-button--medium\">Create campaign</button>\u001b[22m\n\u001b[2m  - attempting click action\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is visible, enabled and stable\u001b[22m\n\u001b[2m      - scrolling into view if needed\u001b[22m\n\u001b[2m      - done scrolling\u001b[22m\n\u001b[2m      - <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m    - waiting 20ms\u001b[22m\n\u001b[2m    - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m    - element is visible, enabled and stable\u001b[22m\n\u001b[2m    - scrolling into view if needed\u001b[22m\n\u001b[2m    - done scrolling\u001b[22m\n\u001b[2m    - <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m  2 × retrying click action\u001b[22m\n\u001b[2m      - waiting 100ms\u001b[22m\n\u001b[2m      - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is visible, enabled and stable\u001b[22m\n\u001b[2m      - scrolling into view if needed\u001b[22m\n\u001b[2m      - done scrolling\u001b[22m\n\u001b[2m      - <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m  28 × retrying click action\u001b[22m\n\u001b[2m       - waiting 500ms\u001b[22m\n\u001b[2m       - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m       - element is visible, enabled and stable\u001b[22m\n\u001b[2m       - scrolling into view if needed\u001b[22m\n\u001b[2m       - done scrolling\u001b[22m\n\u001b[2m       - <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m     - retrying click action\u001b[22m\n\u001b[2m       - waiting 500ms\u001b[22m\n\u001b[2m       - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m       - element is visible, enabled and stable\u001b[22m\n\u001b[2m       - scrolling into view if needed\u001b[22m\n\u001b[2m       - done scrolling\u001b[22m\n\u001b[2m       - <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m     - retrying click action\u001b[22m\n\u001b[2m       - waiting 500ms\u001b[22m\n\u001b[2m       - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m       - element is visible, enabled and stable\u001b[22m\n\u001b[2m       - scrolling into view if needed\u001b[22m\n\u001b[2m       - done scrolling\u001b[22m\n\u001b[2m       - <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m     - retrying click action\u001b[22m\n\u001b[2m       - waiting 500ms\u001b[22m\n\u001b[2m       - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m       - element is visible, enabled and stable\u001b[22m\n\u001b[2m       - scrolling into view if needed\u001b[22m\n\u001b[2m       - done scrolling\u001b[22m\n\u001b[2m       - <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m  - retrying click action\u001b[22m\n\u001b[2m    - waiting 500ms\u001b[22m\n\u001b[2m    - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m    - element is visible, enabled and stable\u001b[22m\n\u001b[2m    - scrolling into view if needed\u001b[22m\n\u001b[2m    - done scrolling\u001b[22m\n\u001b[2m    - <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m  - retrying click action\u001b[22m\n\u001b[2m    - waiting 500ms\u001b[22m\n\n    at DialerPage.clickCreateCampaign (/pages/outbound_dialer/dialerPage.js:982:32)\n    at DialerPage.createCampaign (/pages/outbound_dialer/dialerPage.js:973:14)\n    at /tests/smoke/test_campaign_record_flow_clone.spec.js:1394:30\n    at /tests/smoke/test_campaign_record_flow_clone.spec.js:1387:9\n    at /tests/smoke/test_campaign_record_flow_clone.spec.js:1383:5"}}
{"type":"after","callId":"test.step@8","endTime":154944.89,"attachments":[],"error":{"name":"","message":"TimeoutError: locator.click: Timeout 60000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div button[data-pendo-campaignlist-header-createbutton]')\u001b[22m\n\u001b[2m    - locator resolved to <button data-co-name=\"Button\" data-co-version=\"1.4.0\" data-co-project=\"cobalt\" data-pendo-campaignlist-header-createbutton=\"true\" class=\"react-button_1-4-0_co-button react-button_1-4-0_co-button--primary react-button_1-4-0_co-button--medium\">Create campaign</button>\u001b[22m\n\u001b[2m  - attempting click action\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is visible, enabled and stable\u001b[22m\n\u001b[2m      - scrolling into view if needed\u001b[22m\n\u001b[2m      - done scrolling\u001b[22m\n\u001b[2m      - <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m    - waiting 20ms\u001b[22m\n\u001b[2m    - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m    - element is visible, enabled and stable\u001b[22m\n\u001b[2m    - scrolling into view if needed\u001b[22m\n\u001b[2m    - done scrolling\u001b[22m\n\u001b[2m    - <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m  2 × retrying click action\u001b[22m\n\u001b[2m      - waiting 100ms\u001b[22m\n\u001b[2m      - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is visible, enabled and stable\u001b[22m\n\u001b[2m      - scrolling into view if needed\u001b[22m\n\u001b[2m      - done scrolling\u001b[22m\n\u001b[2m      - <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m  28 × retrying click action\u001b[22m\n\u001b[2m       - waiting 500ms\u001b[22m\n\u001b[2m       - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m       - element is visible, enabled and stable\u001b[22m\n\u001b[2m       - scrolling into view if needed\u001b[22m\n\u001b[2m       - done scrolling\u001b[22m\n\u001b[2m       - <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m     - retrying click action\u001b[22m\n\u001b[2m       - waiting 500ms\u001b[22m\n\u001b[2m       - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m       - element is visible, enabled and stable\u001b[22m\n\u001b[2m       - scrolling into view if needed\u001b[22m\n\u001b[2m       - done scrolling\u001b[22m\n\u001b[2m       - <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m     - retrying click action\u001b[22m\n\u001b[2m       - waiting 500ms\u001b[22m\n\u001b[2m       - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m       - element is visible, enabled and stable\u001b[22m\n\u001b[2m       - scrolling into view if needed\u001b[22m\n\u001b[2m       - done scrolling\u001b[22m\n\u001b[2m       - <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m     - retrying click action\u001b[22m\n\u001b[2m       - waiting 500ms\u001b[22m\n\u001b[2m       - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m       - element is visible, enabled and stable\u001b[22m\n\u001b[2m       - scrolling into view if needed\u001b[22m\n\u001b[2m       - done scrolling\u001b[22m\n\u001b[2m       - <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m  - retrying click action\u001b[22m\n\u001b[2m    - waiting 500ms\u001b[22m\n\u001b[2m    - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m    - element is visible, enabled and stable\u001b[22m\n\u001b[2m    - scrolling into view if needed\u001b[22m\n\u001b[2m    - done scrolling\u001b[22m\n\u001b[2m    - <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m  - retrying click action\u001b[22m\n\u001b[2m    - waiting 500ms\u001b[22m\n","stack":"TimeoutError: locator.click: Timeout 60000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div button[data-pendo-campaignlist-header-createbutton]')\u001b[22m\n\u001b[2m    - locator resolved to <button data-co-name=\"Button\" data-co-version=\"1.4.0\" data-co-project=\"cobalt\" data-pendo-campaignlist-header-createbutton=\"true\" class=\"react-button_1-4-0_co-button react-button_1-4-0_co-button--primary react-button_1-4-0_co-button--medium\">Create campaign</button>\u001b[22m\n\u001b[2m  - attempting click action\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is visible, enabled and stable\u001b[22m\n\u001b[2m      - scrolling into view if needed\u001b[22m\n\u001b[2m      - done scrolling\u001b[22m\n\u001b[2m      - <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m    - waiting 20ms\u001b[22m\n\u001b[2m    - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m    - element is visible, enabled and stable\u001b[22m\n\u001b[2m    - scrolling into view if needed\u001b[22m\n\u001b[2m    - done scrolling\u001b[22m\n\u001b[2m    - <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m  2 × retrying click action\u001b[22m\n\u001b[2m      - waiting 100ms\u001b[22m\n\u001b[2m      - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is visible, enabled and stable\u001b[22m\n\u001b[2m      - scrolling into view if needed\u001b[22m\n\u001b[2m      - done scrolling\u001b[22m\n\u001b[2m      - <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m  28 × retrying click action\u001b[22m\n\u001b[2m       - waiting 500ms\u001b[22m\n\u001b[2m       - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m       - element is visible, enabled and stable\u001b[22m\n\u001b[2m       - scrolling into view if needed\u001b[22m\n\u001b[2m       - done scrolling\u001b[22m\n\u001b[2m       - <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m     - retrying click action\u001b[22m\n\u001b[2m       - waiting 500ms\u001b[22m\n\u001b[2m       - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m       - element is visible, enabled and stable\u001b[22m\n\u001b[2m       - scrolling into view if needed\u001b[22m\n\u001b[2m       - done scrolling\u001b[22m\n\u001b[2m       - <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m     - retrying click action\u001b[22m\n\u001b[2m       - waiting 500ms\u001b[22m\n\u001b[2m       - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m       - element is visible, enabled and stable\u001b[22m\n\u001b[2m       - scrolling into view if needed\u001b[22m\n\u001b[2m       - done scrolling\u001b[22m\n\u001b[2m       - <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m     - retrying click action\u001b[22m\n\u001b[2m       - waiting 500ms\u001b[22m\n\u001b[2m       - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m       - element is visible, enabled and stable\u001b[22m\n\u001b[2m       - scrolling into view if needed\u001b[22m\n\u001b[2m       - done scrolling\u001b[22m\n\u001b[2m       - <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m  - retrying click action\u001b[22m\n\u001b[2m    - waiting 500ms\u001b[22m\n\u001b[2m    - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m    - element is visible, enabled and stable\u001b[22m\n\u001b[2m    - scrolling into view if needed\u001b[22m\n\u001b[2m    - done scrolling\u001b[22m\n\u001b[2m    - <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m  - retrying click action\u001b[22m\n\u001b[2m    - waiting 500ms\u001b[22m\n\n    at DialerPage.clickCreateCampaign (/pages/outbound_dialer/dialerPage.js:982:32)\n    at DialerPage.createCampaign (/pages/outbound_dialer/dialerPage.js:973:14)\n    at /tests/smoke/test_campaign_record_flow_clone.spec.js:1394:30\n    at /tests/smoke/test_campaign_record_flow_clone.spec.js:1387:9\n    at /tests/smoke/test_campaign_record_flow_clone.spec.js:1383:5"}}
{"type":"after","callId":"test.step@7","endTime":154945.114,"attachments":[],"error":{"name":"","message":"TimeoutError: locator.click: Timeout 60000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div button[data-pendo-campaignlist-header-createbutton]')\u001b[22m\n\u001b[2m    - locator resolved to <button data-co-name=\"Button\" data-co-version=\"1.4.0\" data-co-project=\"cobalt\" data-pendo-campaignlist-header-createbutton=\"true\" class=\"react-button_1-4-0_co-button react-button_1-4-0_co-button--primary react-button_1-4-0_co-button--medium\">Create campaign</button>\u001b[22m\n\u001b[2m  - attempting click action\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is visible, enabled and stable\u001b[22m\n\u001b[2m      - scrolling into view if needed\u001b[22m\n\u001b[2m      - done scrolling\u001b[22m\n\u001b[2m      - <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m    - waiting 20ms\u001b[22m\n\u001b[2m    - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m    - element is visible, enabled and stable\u001b[22m\n\u001b[2m    - scrolling into view if needed\u001b[22m\n\u001b[2m    - done scrolling\u001b[22m\n\u001b[2m    - <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m  2 × retrying click action\u001b[22m\n\u001b[2m      - waiting 100ms\u001b[22m\n\u001b[2m      - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is visible, enabled and stable\u001b[22m\n\u001b[2m      - scrolling into view if needed\u001b[22m\n\u001b[2m      - done scrolling\u001b[22m\n\u001b[2m      - <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m  28 × retrying click action\u001b[22m\n\u001b[2m       - waiting 500ms\u001b[22m\n\u001b[2m       - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m       - element is visible, enabled and stable\u001b[22m\n\u001b[2m       - scrolling into view if needed\u001b[22m\n\u001b[2m       - done scrolling\u001b[22m\n\u001b[2m       - <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m     - retrying click action\u001b[22m\n\u001b[2m       - waiting 500ms\u001b[22m\n\u001b[2m       - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m       - element is visible, enabled and stable\u001b[22m\n\u001b[2m       - scrolling into view if needed\u001b[22m\n\u001b[2m       - done scrolling\u001b[22m\n\u001b[2m       - <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m     - retrying click action\u001b[22m\n\u001b[2m       - waiting 500ms\u001b[22m\n\u001b[2m       - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m       - element is visible, enabled and stable\u001b[22m\n\u001b[2m       - scrolling into view if needed\u001b[22m\n\u001b[2m       - done scrolling\u001b[22m\n\u001b[2m       - <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m     - retrying click action\u001b[22m\n\u001b[2m       - waiting 500ms\u001b[22m\n\u001b[2m       - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m       - element is visible, enabled and stable\u001b[22m\n\u001b[2m       - scrolling into view if needed\u001b[22m\n\u001b[2m       - done scrolling\u001b[22m\n\u001b[2m       - <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m  - retrying click action\u001b[22m\n\u001b[2m    - waiting 500ms\u001b[22m\n\u001b[2m    - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m    - element is visible, enabled and stable\u001b[22m\n\u001b[2m    - scrolling into view if needed\u001b[22m\n\u001b[2m    - done scrolling\u001b[22m\n\u001b[2m    - <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m  - retrying click action\u001b[22m\n\u001b[2m    - waiting 500ms\u001b[22m\n","stack":"TimeoutError: locator.click: Timeout 60000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div button[data-pendo-campaignlist-header-createbutton]')\u001b[22m\n\u001b[2m    - locator resolved to <button data-co-name=\"Button\" data-co-version=\"1.4.0\" data-co-project=\"cobalt\" data-pendo-campaignlist-header-createbutton=\"true\" class=\"react-button_1-4-0_co-button react-button_1-4-0_co-button--primary react-button_1-4-0_co-button--medium\">Create campaign</button>\u001b[22m\n\u001b[2m  - attempting click action\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is visible, enabled and stable\u001b[22m\n\u001b[2m      - scrolling into view if needed\u001b[22m\n\u001b[2m      - done scrolling\u001b[22m\n\u001b[2m      - <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m    - waiting 20ms\u001b[22m\n\u001b[2m    - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m    - element is visible, enabled and stable\u001b[22m\n\u001b[2m    - scrolling into view if needed\u001b[22m\n\u001b[2m    - done scrolling\u001b[22m\n\u001b[2m    - <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m  2 × retrying click action\u001b[22m\n\u001b[2m      - waiting 100ms\u001b[22m\n\u001b[2m      - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is visible, enabled and stable\u001b[22m\n\u001b[2m      - scrolling into view if needed\u001b[22m\n\u001b[2m      - done scrolling\u001b[22m\n\u001b[2m      - <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m  28 × retrying click action\u001b[22m\n\u001b[2m       - waiting 500ms\u001b[22m\n\u001b[2m       - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m       - element is visible, enabled and stable\u001b[22m\n\u001b[2m       - scrolling into view if needed\u001b[22m\n\u001b[2m       - done scrolling\u001b[22m\n\u001b[2m       - <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m     - retrying click action\u001b[22m\n\u001b[2m       - waiting 500ms\u001b[22m\n\u001b[2m       - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m       - element is visible, enabled and stable\u001b[22m\n\u001b[2m       - scrolling into view if needed\u001b[22m\n\u001b[2m       - done scrolling\u001b[22m\n\u001b[2m       - <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m     - retrying click action\u001b[22m\n\u001b[2m       - waiting 500ms\u001b[22m\n\u001b[2m       - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m       - element is visible, enabled and stable\u001b[22m\n\u001b[2m       - scrolling into view if needed\u001b[22m\n\u001b[2m       - done scrolling\u001b[22m\n\u001b[2m       - <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m     - retrying click action\u001b[22m\n\u001b[2m       - waiting 500ms\u001b[22m\n\u001b[2m       - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m       - element is visible, enabled and stable\u001b[22m\n\u001b[2m       - scrolling into view if needed\u001b[22m\n\u001b[2m       - done scrolling\u001b[22m\n\u001b[2m       - <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m  - retrying click action\u001b[22m\n\u001b[2m    - waiting 500ms\u001b[22m\n\u001b[2m    - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m    - element is visible, enabled and stable\u001b[22m\n\u001b[2m    - scrolling into view if needed\u001b[22m\n\u001b[2m    - done scrolling\u001b[22m\n\u001b[2m    - <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m  - retrying click action\u001b[22m\n\u001b[2m    - waiting 500ms\u001b[22m\n\n    at DialerPage.clickCreateCampaign (/pages/outbound_dialer/dialerPage.js:982:32)\n    at DialerPage.createCampaign (/pages/outbound_dialer/dialerPage.js:973:14)\n    at /tests/smoke/test_campaign_record_flow_clone.spec.js:1394:30\n    at /tests/smoke/test_campaign_record_flow_clone.spec.js:1387:9\n    at /tests/smoke/test_campaign_record_flow_clone.spec.js:1383:5"}}
{"type":"error","message":"TimeoutError: locator.click: Timeout 60000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('iframe[data-testid=\"frame-outbound-dialer\"]').contentFrame().locator('div button[data-pendo-campaignlist-header-createbutton]')\u001b[22m\n\u001b[2m    - locator resolved to <button data-co-name=\"Button\" data-co-version=\"1.4.0\" data-co-project=\"cobalt\" data-pendo-campaignlist-header-createbutton=\"true\" class=\"react-button_1-4-0_co-button react-button_1-4-0_co-button--primary react-button_1-4-0_co-button--medium\">Create campaign</button>\u001b[22m\n\u001b[2m  - attempting click action\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is visible, enabled and stable\u001b[22m\n\u001b[2m      - scrolling into view if needed\u001b[22m\n\u001b[2m      - done scrolling\u001b[22m\n\u001b[2m      - <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m    - waiting 20ms\u001b[22m\n\u001b[2m    - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m    - element is visible, enabled and stable\u001b[22m\n\u001b[2m    - scrolling into view if needed\u001b[22m\n\u001b[2m    - done scrolling\u001b[22m\n\u001b[2m    - <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m  2 × retrying click action\u001b[22m\n\u001b[2m      - waiting 100ms\u001b[22m\n\u001b[2m      - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is visible, enabled and stable\u001b[22m\n\u001b[2m      - scrolling into view if needed\u001b[22m\n\u001b[2m      - done scrolling\u001b[22m\n\u001b[2m      - <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m  28 × retrying click action\u001b[22m\n\u001b[2m       - waiting 500ms\u001b[22m\n\u001b[2m       - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m       - element is visible, enabled and stable\u001b[22m\n\u001b[2m       - scrolling into view if needed\u001b[22m\n\u001b[2m       - done scrolling\u001b[22m\n\u001b[2m       - <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m     - retrying click action\u001b[22m\n\u001b[2m       - waiting 500ms\u001b[22m\n\u001b[2m       - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m       - element is visible, enabled and stable\u001b[22m\n\u001b[2m       - scrolling into view if needed\u001b[22m\n\u001b[2m       - done scrolling\u001b[22m\n\u001b[2m       - <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m     - retrying click action\u001b[22m\n\u001b[2m       - waiting 500ms\u001b[22m\n\u001b[2m       - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m       - element is visible, enabled and stable\u001b[22m\n\u001b[2m       - scrolling into view if needed\u001b[22m\n\u001b[2m       - done scrolling\u001b[22m\n\u001b[2m       - <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m     - retrying click action\u001b[22m\n\u001b[2m       - waiting 500ms\u001b[22m\n\u001b[2m       - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m       - element is visible, enabled and stable\u001b[22m\n\u001b[2m       - scrolling into view if needed\u001b[22m\n\u001b[2m       - done scrolling\u001b[22m\n\u001b[2m       - <p data-co-name=\"Text\" data-co-version=\"2.0.1\" data-co-project=\"cobalt\" class=\"react-typography_2-0-1_co-text\">Toggle secondary area</p> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m  - retrying click action\u001b[22m\n\u001b[2m    - waiting 500ms\u001b[22m\n\u001b[2m    - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m    - element is visible, enabled and stable\u001b[22m\n\u001b[2m    - scrolling into view if needed\u001b[22m\n\u001b[2m    - done scrolling\u001b[22m\n\u001b[2m    - <div data-co-name=\"Box\" data-co-version=\"2.0.0\" data-co-project=\"cobalt\" class=\"react-box_2-0-0_co-box react-box_2-0-0_co-box--padding-2\">…</div> from <div id=\"co-portal-container\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m  - retrying click action\u001b[22m\n\u001b[2m    - waiting 500ms\u001b[22m\n","stack":[{"file":"/pages/outbound_dialer/dialerPage.js","line":982,"column":32,"function":"DialerPage.clickCreateCampaign"},{"file":"/pages/outbound_dialer/dialerPage.js","line":973,"column":14,"function":"DialerPage.createCampaign"},{"file":"/tests/smoke/test_campaign_record_flow_clone.spec.js","line":1394,"column":30},{"file":"/tests/smoke/test_campaign_record_flow_clone.spec.js","line":1387,"column":9},{"file":"/tests/smoke/test_campaign_record_flow_clone.spec.js","line":1383,"column":5}]}
{"type":"before","callId":"hook@23","startTime":154945.783,"class":"Test","method":"step","apiName":"After Hooks","params":{},"stack":[]}
{"type":"before","callId":"pw:api@24","parentId":"hook@23","startTime":154947.049,"class":"Test","method":"step","apiName":"page.screenshot","params":{"mode":"only-on-failure","fullPage":"true","timeout":"5000","path":"/test-results/smoke-test_campaign_record-c62b2-oke-Preview-Campaign-Create-Chrome-ca-prod/test-failed-1.png","caret":"initial","mask":"undefined","type":"png"},"stack":[]}
{"type":"after","callId":"pw:api@24","endTime":155042.273,"attachments":[]}
{"type":"before","callId":"attach@25","parentId":"hook@23","startTime":155042.992,"class":"Test","method":"step","apiName":"attach \"screenshot\"","params":{},"stack":[]}
{"type":"after","callId":"attach@25","endTime":155043.037,"attachments":[{"name":"screenshot","contentType":"image/png","sha1":"d64455ad8aa73b2565a281d1f2cbeebb8c286c9b"}]}
{"type":"before","callId":"hook@26","parentId":"hook@23","startTime":155043.447,"class":"Test","method":"step","apiName":"afterEach hook","params":{},"stack":[{"file":"/tests/smoke/test_campaign_record_flow_clone.spec.js","line":63,"column":6}]}
{"type":"stdout","timestamp":155043.677,"text":"==== 【po-automationca】【failed】Finished test Smoke-Preview Campaign Create on chromium with status failed=====\n"}
{"type":"after","callId":"hook@26","endTime":155043.713,"attachments":[]}
{"type":"before","callId":"fixture@27","parentId":"hook@23","startTime":155044.022,"class":"Test","method":"step","apiName":"fixture: page","params":{},"stack":[]}
{"type":"after","callId":"fixture@27","endTime":155044.082,"attachments":[]}
{"type":"before","callId":"fixture@28","parentId":"hook@23","startTime":155044.27,"class":"Test","method":"step","apiName":"fixture: context","params":{},"stack":[]}
{"type":"after","callId":"fixture@28","endTime":155044.304,"attachments":[]}
{"type":"before","callId":"pw:api@29","parentId":"hook@23","startTime":155590.425,"class":"Test","method":"step","apiName":"video.saveAs","params":{"path":"/test-results/smoke-test_campaign_record-c62b2-oke-Preview-Campaign-Create-Chrome-ca-prod/video.webm"},"stack":[]}
{"type":"after","callId":"pw:api@29","endTime":155593.346,"attachments":[]}
{"type":"before","callId":"attach@30","parentId":"hook@23","startTime":155593.876,"class":"Test","method":"step","apiName":"attach \"video\"","params":{},"stack":[]}
{"type":"after","callId":"attach@30","endTime":155593.905,"attachments":[{"name":"video","contentType":"video/webm","sha1":"7390c65a4bba23794376cdef5a485d6253274e9a"}]}
{"type":"after","callId":"hook@23","endTime":155606.414,"attachments":[]}
{"type":"before","callId":"hook@31","startTime":155606.667,"class":"Test","method":"step","apiName":"Worker Cleanup","params":{},"stack":[]}
{"type":"before","callId":"fixture@32","parentId":"hook@31","startTime":155607.056,"class":"Test","method":"step","apiName":"fixture: browser","params":{},"stack":[]}
{"type":"after","callId":"fixture@32","endTime":155725.097,"attachments":[]}
{"type":"after","callId":"hook@31","endTime":155725.351,"attachments":[]}