name: Release
on:
  push:
    branches:
      - main
jobs:
  test:
    environment: release
    name: Test
    strategy:
      fail-fast: true
      matrix:
        node:
          - 22
    runs-on: ubuntu-latest
    permissions:
      contents: write
      id-token: write
    steps:
      - name: setup repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: setup node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node }}
      - name: Setup NodeJS ${{ matrix.node }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node }}
      - name: Install dependencies
        run: npm install
      - name: Run lint
        run: npm run lint
      - name: Run tests
        run: npm run test
      - name: Build
        run: npm run build
      - name: Release
        run: npx semantic-release
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
