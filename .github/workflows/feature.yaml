name: Run Tests
on:
  pull_request:
    branches:
      - main
    types:
      - opened
      - synchronize
      - reopened
      - ready_for_review
jobs:
  test:
    runs-on: ubuntu-latest
    name: Test
    strategy:
      fail-fast: true
      matrix:
        node:
          - 22
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Setup NodeJS ${{ matrix.node }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node }}
      - name: Install dependencies
        run: npm install
      - name: Run lint
        run: npm run lint
      - name: Run tests
        run: npm run test
