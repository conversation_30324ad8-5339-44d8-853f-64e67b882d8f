# 调试指南 - 解决 "No lowest priority node found" 错误

## 🔍 问题分析

"No lowest priority node found (path: Ok)" 错误很可能来自：
1. FastMCP 内部的图片处理逻辑
2. 底层的图片编码/解码库
3. 内存管理或资源竞争问题

## 🛠 新增的调试功能

### 1. 详细错误日志
现在每个工具都有详细的错误追踪：

```typescript
// 每个关键步骤都有日志
logInfo('get-screenshots-start', `Starting screenshot analysis for: ${traceZipPath}`);
logInfo('get-screenshots-extracted', `Extracted to: ${outputDir}`);
logInfo('get-screenshots-reading', `Reading directory: ${resourcesDir}`);
logInfo('get-screenshots-files', `Found ${files.length} files: ${files.join(', ')}`);
```

### 2. 开发模式启动
使用调试模式启动服务器：

```bash
# 方法1: 使用调试脚本
node start-debug.js

# 方法2: 设置环境变量
NODE_ENV=development node dist/server.js
```

### 3. 错误上下文
每个错误都包含：
- 时间戳
- 具体的执行步骤
- 完整的错误信息
- 堆栈跟踪（开发模式）

## 🎯 调试步骤

### 步骤1: 启用调试模式

更新你的 VS Code MCP 配置：

```json
{
  "servers": {
    "playwright-trace-analyzer": {
      "command": "node",
      "args": [
        "/Users/<USER>/Github/playwright-test-runner-and-debugger/start-debug.js"
      ]
    }
  }
}
```

### 步骤2: 重现问题

1. 重启 VS Code MCP 客户端
2. 尝试分析 trace 文件
3. 观察 VS Code 的 MCP 日志输出

### 步骤3: 查看详细日志

在开发模式下，你会看到类似这样的日志：

```
[2024-01-01T12:00:00.000Z] INFO in get-screenshots-start: Starting screenshot analysis for: /path/to/trace.zip
[2024-01-01T12:00:00.100Z] INFO in get-screenshots-extracted: Extracted to: /path/to/extracted
[2024-01-01T12:00:00.200Z] INFO in get-screenshots-reading: Reading directory: /path/to/resources
[2024-01-01T12:00:00.300Z] INFO in get-screenshots-files: Found 3 files: screenshot-1.png, screenshot-2.png, data.json
[2024-01-01T12:00:00.400Z] INFO in get-screenshots-images: Found 2 image files: screenshot-1.png, screenshot-2.png
[2024-01-01T12:00:00.500Z] INFO in get-screenshots-stat: Getting stats for: /path/to/screenshot-1.png
[2024-01-01T12:00:00.600Z] INFO in get-screenshots-processed: Successfully processed: screenshot-1.png
```

如果出现错误，你会看到：

```
[2024-01-01T12:00:00.700Z] ERROR in get-screenshots-file-error: Error processing file screenshot-2.png: No lowest priority node found
Stack trace: [详细的堆栈信息]
```

## 🔧 可能的解决方案

### 方案1: 完全避免图片处理

如果问题持续出现，我们可以创建一个"安全模式"版本：

```typescript
// 完全跳过可能有问题的操作
if (process.env.SAFE_MODE === 'true') {
  // 只返回文件列表，不进行任何文件操作
  return `Found ${imageFiles.length} screenshots: ${imageFiles.join(', ')}`;
}
```

### 方案2: 异步处理

将文件操作改为异步队列处理：

```typescript
// 一次只处理一个文件，避免资源竞争
for (const file of imageFiles) {
  await processFileAsync(file);
}
```

### 方案3: 内存限制

添加内存和资源限制：

```typescript
// 限制同时处理的文件数量
const maxConcurrent = 1;
const semaphore = new Semaphore(maxConcurrent);
```

## 📊 错误模式分析

### 如果错误总是在特定步骤出现：

1. **解压阶段**：可能是 ZIP 文件损坏或权限问题
2. **读取目录阶段**：可能是路径问题或权限问题
3. **文件统计阶段**：可能是文件锁定或权限问题
4. **图片处理阶段**：可能是图片格式问题或内存问题

### 如果错误随机出现：

1. **内存压力**：可能需要添加内存限制
2. **并发问题**：可能需要序列化处理
3. **资源竞争**：可能需要添加重试机制

## 🚀 临时解决方案

如果问题持续，可以使用这个临时配置：

```json
{
  "servers": {
    "playwright-trace-analyzer-safe": {
      "command": "node",
      "args": [
        "/Users/<USER>/Github/playwright-test-runner-and-debugger/dist/server.js"
      ],
      "env": {
        "SAFE_MODE": "true",
        "NODE_ENV": "production"
      }
    }
  }
}
```

## 📝 收集调试信息

当错误出现时，请收集：

1. **完整的错误日志**（从 VS Code MCP 输出）
2. **trace.zip 文件的基本信息**：
   ```bash
   ls -la /path/to/trace.zip
   file /path/to/trace.zip
   unzip -l /path/to/trace.zip
   ```
3. **系统信息**：
   ```bash
   node --version
   npm --version
   uname -a
   ```

## 🎯 下一步

1. 先使用调试模式重现问题
2. 收集详细的错误日志
3. 根据日志确定具体的失败点
4. 应用相应的解决方案

这样我们就能精确定位 "No lowest priority node found" 错误的具体原因了！
