import eslint from "@eslint/js";
import eslint<PERSON>onfig<PERSON><PERSON>tier from "eslint-config-prettier/flat";
import perfectionist from "eslint-plugin-perfectionist";
import tseslint from "typescript-eslint";

export default tseslint.config(
  eslint.configs.recommended,
  tseslint.configs.recommended,
  perfectionist.configs["recommended-alphabetical"],
  eslintConfig<PERSON>rettier,
  {
    ignores: ["**/*.js"],
  },
);
